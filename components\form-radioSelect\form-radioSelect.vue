<template>
	<view class="flex justify-center">
		<view class="fromRadioSelectComponent">
			<view
				:style="{color: `${newData.titleColor}`, fontSize: `${newData.titleSize}px`,fontWeight:`${newData.titleWeight?'bold':'normal'}`}">
				{{newData.title}}<i v-show="newData.required" style="color: #FF0000">*</i>
			</view>
			<view class="margin-tb-xs"
				:style="{color: `${newData.describeColor}`, fontSize: `${newData.describeSize}px`,fontWeight:`${newData.describeWeight?'bold':'normal'}`}">
				{{newData.describe}}
			</view>
			<view>
				<view v-if="newData.type==0">
					<radio-group >
						<view  class="justify-around" v-for="(item, index) in radioList" :key="index" >
							<label @click="oneRadioClick(item)">
								<radio class="flex-sub" style="transform:scale(0.9)" :value="item.value"
									:checked="item.checked" />
								<label class="flex-sub">{{item.value}}</label>
							</label>
						</view>
					</radio-group>
				</view>
				<view v-if="newData.type==1">
					<view class="justify-around">
						<view class="justify-around" v-for="(item, index) in radioList" :key="item.index">
							<label style="display: inline;" @click="moreRadioClick(item)">
								<radio class="flex-sub" style="transform:scale(0.9)" :value="item.value"
									:checked="item.checked" />
								<label class="flex-sub">{{item.value}}</label>
							</label>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				radioList: [],
				selected: '',
			};
		},

		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		mounted() {
			for (let i = 0; i < this.newData.optionList.length; i++) {
				this.radioList.push({
					value: this.newData.optionList[i],
					checked: false
				})
			}
		},
		methods: {
			radioChange(e) {
				e.detail.checked = true;
			},
			oneRadioClick(obj) {
				for (var i = 0; i < this.radioList.length; i++) {
					this.radioList[i].checked = false;
				}
				obj.checked = !obj.checked;
			},
			moreRadioClick(obj) {
				obj.checked = !obj.checked;
			},
			checkValue() {
				let res = "";
				console.log("检验值了fromRadioSelectComponent:")

				for (let i = 0; i < this.radioList.length; i++) {
					if (this.radioList[i].checked) {
						res = res +this.radioList[i].value +","
					}
				}
				if (!res || res.length == 0) {
					uni.showToast({
						title: "请完善" + this.newData.title + "的内容",
						icon: 'none',
						duration: 2000
					});
					return false;
				}
				return {
					value: res
				};
			}
		}
	};
</script>
<style>
	.fromRadioSelectComponent {
		padding: 5px 15px;
		width: 90%;
		border-bottom: 1px dashed rgba(0, 0, 0, .2);
	}
</style>
