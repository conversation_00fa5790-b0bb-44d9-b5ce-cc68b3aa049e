<!--
	底部提示 显示技术归属
-->
<template>
	<view class="bottom_remind" v-if="false">
		<view class="flex justify-center align-center" v-if="pageData.logo">
			<view class="cu-avatar round bg-img    padding-top"
				:style="{'background-image':`${'url('+pageData.logo+')'}`,height: '20px',width: '20px'}">
			</view>
		</view>
<!--		<view class="flex justify-center align-center" @tap="jumpUrl">
			技术支持:福州市微微笑文化传播公司
		</view>-->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				hiddenFlag:true,
				pageData:{}
			};
		},
		watch: {
			pageDivData(newVal,oldVal){
				// console.log("dsada",newVal)
				this.pageData = newVal;
			}
		},
		components: {},
		props: {
			pageDivData: {
				type: Object,
			},
		},
		mounted() {
			this.hiddenFlag = this.pageData.bottomHiddenFlag
		},
		methods: {
			//页面跳转
			jumpUrl(obj) {
				uni.navigateTo({
					url: "/pages/cooperation/index"
				});
			},

		}
	};
</script>
<style>
	.bottom_remind{
		padding: 2px 0;
		background-color: #828282;
		color: #ffffff;
		font-size: 12px;
	}
</style>
