<template>
	<!-- 店铺推荐 组件 -->
	<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="bg-white">
		<view class="wrapper-list-shop">
			<view class="cu-bar margin-left-xs">
				<view class="shop-selection margin-left" :style="{color: `${newData.titleColor}`}">
					<text class="text-bold" :class="newData.titleIcon"></text>
					<text class="margin-left-xs">{{newData.title}}</text>
				</view>
				<navigator hover-class="none" url="/pages/shop/shop-list/index?type=2" class="shop-more text-sm margin-right-xs">更多<text
					 class="cuIcon-right"></text></navigator>
			</view>
			<scroll-view class="scroll-view_x shop-detail" scroll-x>
				<block v-for="(item, index) in newData.shopInfoData" :key="index">
					<navigator hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + item.id" class="item shadow-warp flex shop-box radius">
						<view class="bg-mask flex shop-image radius">
							<image :src="item.imgUrl" class="radius"></image>
						</view>
						<view class="shop-information text-center">
							<view class="text-white enter-shop text-sm ">进店<text class="cuIcon-right"></text></view>
							<view class="bg-white round enter-bg"></view>
						</view>
						<view class=" text-white text-center text-xs shop-name overflow-1">{{item.name}}</view>
					</navigator>
				</block>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	const app = getApp();

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						pageSpacing: 0,
						shopInfoData: []
					}
				}
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.wrapper-list-shop {
		white-space: nowrap;
		padding:10rpx;
	}

	.wrapper-list-shop .item {
		display: inline-block;
		width: 260rpx;
		height: 380rpx;
		margin-top: 10px;
		margin-bottom: 10px;
		margin-left: 10rpx;
		margin-right: 10rpx;
		border: 1rpx solid #eee;
		background-color: #fff;
	}

	.wrapper-list-shop .item:nth-last-child(1) {
		margin-right: 0;
	}

	.wrapper-list-shop .item .img-box {
		width: 100%;
		height: 260rpx;

	}

	.wrapper-list-shop .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx 5rpx 0 0;
	}

	.enter-bg {
		width: 100rpx;
		height: 40rpx;
		opacity: 0.3;
	}

	.shop-information {
		position: absolute;
		top: 140rpx;
		left: 50rpx;
	}

	.shop-box {
		height: 200rpx !important;
		width: 200rpx !important;
		margin-right: 0rpx !important;
	}

	.shop-selection {
		margin-left: 0rpx !important;
		color: #666666;
	}

	.shop-more {
		margin-right: 0rpx !important;
		color: #666666;
	}

	.shop-image {
		width: 200rpx;
		height: 200rpx !important;
	}

	.shop-image image {
		height: 200rpx !important;
	}

	.shop-detail {
		margin-top: -30rpx !important;
	}

	.shop-name {
		position: absolute;
		top: 0;
		line-height: 200rpx;
		padding: 0 10rpx 0 10rpx !important;
		width: 200rpx;
		overflow: hidden;
		text-overflow: ellipsis !important;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}

	.enter-shop {
		position: absolute;
		left: 20rpx;
	}
</style>
