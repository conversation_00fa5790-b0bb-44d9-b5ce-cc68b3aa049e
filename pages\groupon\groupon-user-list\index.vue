<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">我的拼团</block>
		</cu-custom>
		<scroll-view scroll-x class="bg-white nav fixed">
			<view class="flex text-center">
				<view :class="'cu-item flex-sub ' + (index==tabCur?'cur text-'+theme.themeColor:'')" v-for="(item, index) in status" :key="index"
				 @tap="tabSelect" :data-index="index" :data-key="item.key">{{item.value}}</view>
			</view>
		</scroll-view>
		<view class="cu-card article no-card margin-top-bar">
			<view class="cu-item groupon-card padding-top-xs solid-bottom" v-for="(item, index) in grouponUserList" :key="index">
				<navigator class="padding-sm" hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + item.grouponInfo.shopInfo.id">
					<view class="cu-avatar sm radius " :style="'background-image:url(' + item.grouponInfo.shopInfo.imgUrl + ')'"></view>
					<text class="text-black text-bold margin-left-sm">{{item.grouponInfo.shopInfo.name}}</text>
					<text class="cuIcon-right text-sm"></text>
				</navigator>
				<view class="content align-center">
					<image :src="item.grouponInfo.picUrl" mode="aspectFill" class="row-img margin-top-xs"></image>
					<view class="desc row-info block">
						<view class="text-black margin-top-sm overflow-1">
							<text class="cu-tag bg-red radius margin-right-xs regmen">{{item.grouponNum}}人团</text>{{item.grouponInfo.name}}</view>
						<view class="flex justify-start margin-top-sm align-center">
							<view class="text-price text-xl text-bold text-red">{{item.grouponPrice}}</view>
							<view class="text-price text-decorat text-df text-gray margin-left-sm">{{item.grouponInfo.goodsSku.salesPrice}}</view>
							<view class="cu-tag bg-red radius sm margin-left" v-if="item.grouponInfo.goodsSpu.freightTemplat.type == '2'">包邮</view>
						</view>
						<view class="flex margin-top">
							<view class="state text-xs radius" :class="'cu-tag line-' + (item.status == '0' ? 'orange' : item.status == '1' ? 'green' : 'gray' )">{{item.status == '0' ? '进行中' : item.status == '1' ? '已完成' : '已过期' }}</view>
							<navigator class="cu-btn round shadow-blur check-details" :class="'bg-'+theme.themeColor" hover-class="none" :url="'/pages/groupon/groupon-user-detail/index?id=' + item.groupId">查看详情</navigator>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				loadmore: true,
				grouponUserList: [],
				tabCur: 0,
				status: [{
					value: '全部拼团',
					key: ''
				}, {
					value: '拼团中',
					key: '0'
				}, {
					value: '已完成',
					key: '1'
				}, {
					value: '已过期',
					key: '2'
				}]
			};
		},

		components: {},
		props: {},

		onShow() {},

		onLoad: function(options) {
			app.initPage().then(res => {
				this.grouponUserPage();
			});
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.grouponUserPage();
			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框

			uni.hideNavigationBarLoading(); // 停止下拉动作

			uni.stopPullDownRefresh();
		},

		methods: {
			grouponUserPage() {
				api.grouponUserPage(Object.assign({
					userId: uni.getStorageSync('user_info').id
				}, this.page, util.filterForm(this.parameter))).then(res => {
					let grouponUserList = res.data.records;
					this.grouponUserList = [...this.grouponUserList, ...grouponUserList];
					if (grouponUserList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			tabSelect(e) {
				let dataset = e.currentTarget.dataset;

				if (dataset.index != this.tabCur) {

					this.tabCur = dataset.index;
					this.parameter.status = dataset.key;

					this.refresh();
				}
			},

			refresh() {
				this.loadmore = true;
				this.grouponUserList = [];
				this.page.current = 1;
				this.grouponUserPage();
			}

		}
	};
</script>
<style>
	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx;
	}
	
	.nav{
		top: unset !important;
	}
	
	.article{
		margin-top: 90rpx;
	}
	
	.regmen{
		height: 36rpx;
		padding: 10rpx;
	}
	
	.state{
		width: 80rpx;
		height:40rpx ;
		margin-top: -10rpx;
	}
	
	.check-details{
		margin: -20rpx 0 0 180rpx;
	}
</style>
