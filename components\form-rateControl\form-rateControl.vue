<template>
	<view class="flex justify-center">
		<view class="rateControlComponent">
			<view>
				<view
					:style="{color: `${newData.titleColor}`, fontSize: `${newData.titleSize}px`,fontWeight:`${newData.titleWeight?'bold':'normal'}`}">
					{{newData.title}}<i v-show="newData.required" style="color: #FF0000">*</i>
				</view>
				<view class="margin-tb-xs"
					:style="{color: `${newData.describeColor}`, fontSize: `${newData.describeSize}px`,fontWeight:`${newData.describeWeight?'bold':'normal'}`}">
					{{newData.describe}}
				</view>
				<view class="flex" style="align-items: center;">
					<uni-rate v-model="rateValue" :allowHalf="newData.isHalf" color="#EFF2F7"
						:activeColor="newData.selectColor" @change="onChange" />
					<text v-if="newData.rateType==1" :style="{color: `${newData.selectColor}`}"
						style="justify-content: center;">{{formateRate()}}</text>
					<text v-if="newData.rateType==2" :style="{color: `${newData.selectColor}`}"
						style="justify-content: center;">{{rateValue}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				rateValue: 0,
			};
		},

		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		created() {
			this.rateValue = this.newData.initRate
		},
		methods: {
			onChange() {

			},
			formateRate() {
				if (this.newData.isHalf) {
					let step = 0.5;
					return this.newData.textList[(this.rateValue / step) - 1];
				} else {
					return this.newData.textList[(this.rateValue) - 1];
				}
			},
			checkValue() {
				console.log("检验值了rateControlComponent", )
				if (!this.rateValue) {
					uni.showToast({
						title: "请完善" + this.newData.title + "的内容",
						icon: 'none',
						duration: 2000
					});
					return false;
				}
				return {
					value: this.rateValue
				};
			}
		}
	};
</script>
<style>
	.rateControlComponent {
		padding: 5px 15px;
		width: 90%;
		border-bottom: 1px dashed rgba(0, 0, 0, .2);
	}
</style>
