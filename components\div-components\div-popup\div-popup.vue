<template>
	<view class="div-popup" v-if="showPopup">
		<view class="popup-mask" @click="closePopup" @touchmove.prevent @mousewheel.prevent></view>
		<view class="popup-content" :style="{
			width: data.width + 'px',
			height: data.height + 'px',
			'background-size': data.backgroundSize,
			'background-position': data.backgroundPosition,
			'background-repeat': data.backgroundRepeat,
			'background-image': 'url(' + data.imageUrl + ')'
		}" @click="jumpToLink">
			<view class="popup-close" @click.stop="closePopup">×</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'div-popup',
		props: {
			value: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				data: this.value,
				showPopup: false,
				timer: null,
				bodyOverflow: '', // 保存body原始overflow值
				scrollEvent: null // 存储滚动事件处理函数
			}
		},
		watch: {
			value: {
				handler(val) {
					this.data = val;
				},
				deep: true
			},
			showPopup(val) {
				// 监听弹窗显示状态变化
				if (val) {
					this.lockBodyScroll();
				} else {
					this.unlockBodyScroll();
				}
			}
		},
		mounted() {
			// 延迟显示弹窗
			setTimeout(() => {
				this.showPopup = true;
				
				// 如果设置了自动关闭
				if (this.data.autoClose && this.data.closeDelay > 0) {
					this.timer = setTimeout(() => {
						this.closePopup();
					}, this.data.closeDelay * 1000);
				}
			}, (this.data.showDelay || 0) * 1000);
		},
		beforeDestroy() {
			// 清除定时器
			if (this.timer) {
				clearTimeout(this.timer);
			}
			// 确保恢复滚动
			this.unlockBodyScroll();
		},
		methods: {
			closePopup() {
				this.showPopup = false;
			},
			jumpToLink() {
				if (this.data.linkUrl) {
					if (this.data.isSystemUrl) {
						uni.navigateTo({
							url: this.data.linkUrl
						});
					} else {
						window.location.href = this.data.linkUrl;
					}
				}
			},
			lockBodyScroll() {
				// #ifdef H5
				// 记录并设置body样式，阻止滚动
				const body = document.body;
				this.bodyOverflow = body.style.overflow;
				body.style.overflow = 'hidden';
				
				// 阻止滚轮事件
				this.scrollEvent = (e) => {
					e.preventDefault();
					return false;
				};
				
				// 添加事件监听
				document.addEventListener('wheel', this.scrollEvent, { passive: false });
				document.addEventListener('touchmove', this.scrollEvent, { passive: false });
				// #endif
				
				// #ifdef APP-PLUS || MP-WEIXIN
				// 小程序和APP端的滚动阻止逻辑
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				// #endif
			},
			unlockBodyScroll() {
				// #ifdef H5
				// 恢复body原始样式
				const body = document.body;
				body.style.overflow = this.bodyOverflow;
				
				// 移除事件监听
				if (this.scrollEvent) {
					document.removeEventListener('wheel', this.scrollEvent);
					document.removeEventListener('touchmove', this.scrollEvent);
					this.scrollEvent = null;
				}
				// #endif
			}
		}
	}
</script>

<style scoped>
	.div-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.popup-mask {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
		touch-action: none; /* 阻止所有触摸操作 */
	}
	
	.popup-content {
		position: relative;
		z-index: 1000;
		background-color: #fff;
		border-radius: 8px;
		overflow: hidden;
	}
	
	.popup-close {
		position: absolute;
		top: 10px;
		right: 10px;
		width: 30px;
		height: 30px;
		line-height: 30px;
		text-align: center;
		background-color: rgba(0, 0, 0, 0.5);
		color: #fff;
		border-radius: 50%;
		font-size: 20px;
		cursor: pointer;
	}
</style> 