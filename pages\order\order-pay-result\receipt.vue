<template>
	<view>
<!-- 		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="false">
			<block slot="backText">返回</block>
			<block slot="content">支付结果</block>
		</cu-custom> -->
		<view class="cu-card">
			<view class="cu-item">
				<!-- {{options}} -->
				<view class="solid-bottom text-xxl padding text-center">
					<text class="cuIcon-roundcheckfill text-green"> 支付成功</text>
				</view>
				<view class="margin-left-sm margin-top flex">
					<text class="margin-left flex-sub text-sm">订单名称</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.name}}</view>
				</view>
				<view class="margin-left-sm margin-top flex">
					<text class="margin-left flex-sub text-sm">订单编号</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.orderNo}}</view>
				</view>
				<view class="margin-left-sm margin-top flex">
					<text class="margin-left flex-sub text-sm">付款金额</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.paymentPrice?orderInfo.paymentPrice:'0.00'}}</view>
				</view>			
<!-- 				<view class="margin-left-sm margin-top flex">
					<text class="margin-left flex-sub text-sm">创建时间</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.createTime}}</view>
				</view> -->
				<view class="margin-left-sm margin-top flex" v-if="orderInfo.paymentTime">
					<text class="margin-left flex-sub text-sm">付款时间</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.paymentTime}}</view>
				</view>
				<view class="padding flex flex-direction">
					<button class="cu-btn margin-tb-sm lg goBackButton " @tap="toBack()">返回</button>
				</view>
			</view>
		</view>
	</view>
</template>
<script type="text/javascript" charset="UTF-8" src="https://wx.gtimg.com/pay_h5/goldplan/js/jgoldplan-1.0.0.js">
</script>
<script>
	const app = getApp();
	import api from 'utils/api'
	import util from 'utils/util'
	import __config from 'config/env';

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				sub_mch_id: '',
				out_trade_no: '',
				orderInfo: {},
				id: '',
				options: '',
				appId : '',
				pageId : '',
				pageType : '',
				componentAppId : '',
				tenantId : '',
			};
		},
		components: {
			
		},
		props: {
			
		},
		onShow() {
			
		},
		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.id = options.id
			this.sub_mch_id = options.sub_mch_id; //特约商户号
			this.out_trade_no = options.out_trade_no; //商户订单号
			this.options = options
			// app.initPage().then(res => {
			console.log("页面参数",options)
			//初始化
			this.orderGet();
			// });
		},
		onReady() {
			// 点金计划页面打开时，页面上方的 iframe 框架会请求服务商填写的商家小票链接，会在链
			// 接后附上：“特约商户号（sub_mch_id）”、“商户订单号（out_trade_no）”、
			// “md5 校验码（check_code）”三个字段的信息，方便服务商快速定位到具体订单的同
			// 时，也保证链接的可靠性。
			let mchData = {
				action: 'onIframeReady',
				displayStyle: 'SHOW_CUSTOM_PAGE'
			};
			// let mchData ={action:'onIframeReady',displayStyle:'SHOW_OFFICIAL_PAGE'}
			let postData = JSON.stringify(mchData);
			parent.postMessage(postData, 'https://payapp.weixin.qq.com');
		},
		methods: {
			show() {
				uni.showToast({
					title: this.options
				})
			},
			orderGet() {
				let that = this;
				let pages =  getCurrentPages()
				let route = pages[pages.length - 1].route;
				let url =__config.receiptPath+'/'+route+'?tenant_id=1&sub_mch_id='+this.options.sub_mch_id+'&out_trade_no='+this.options.out_trade_no;
				let params = {
					subMchId:this.options.sub_mch_id,
					outTradeNo:this.options.out_trade_no,
					checkCode:this.options.check_code,
					url:url
				}
				console.log("params",params)
				// let params = {
				// 	subMchId: '1614371511',
				// 	outTradeNo: '1448111454858248192',
				// 	checkCode: 'edf5dd23da6503b618fbcf482620a8aa',
				// 	url: "https://mall.gongjunqi.com/pages/spellgroup/groupon-pay-result/index?sub_mch_id=1614371511&out_trade_no=1448111454858248192",
				// }
				// let params = {
				// 	subMchId: '1604508213',
				// 	outTradeNo: '1442329380234723328',
				// 	checkCode: 'edf5dd23da6503b618fbcf482620a8aa',
				// 	url: "https://mall.gongjunqi.com/pages/spellgroup/groupon-pay-result/index?sub_mch_id=1614371511&out_trade_no=1448111454858248192",
				// }

				api.orderReceiptGet(params).then(res => {
					console.log("订单结果",res.data)
					this.orderInfo = res.data.orderInfo
					this.appId = res.data.appId
					this.pageId = res.data.pageId
					this.pageType = res.data.pageType
					this.componentAppId = res.data.componentAppId
					this.tenantId = res.data.tenantId
				});
			},
			toBack() {
				let url =""
				if(this.pageType == "1"){
					url = __config.receiptPath+'/pages/home/<USER>'+this.pageId+'&tenant_id='+this.tenantId+'&app_id='+this.appId+'&component_appid='+this.componentAppId;
				}else if(this.pageType == "2"){
					url = __config.receiptPath+'/pages/groupon/index?page_id='+this.pageId+'&tenant_id='+this.tenantId+'&app_id='+this.appId+'&component_appid='+this.componentAppId;
				}else if(this.pageType == "3"){
					url = __config.receiptPath+'/pages/goods/goods-detail/index?page_id='+this.pageId+'&tenant_id='+this.tenantId+'&app_id='+this.appId+'&component_appid='+this.componentAppId;
				}else if(this.pageType == "4"){
					url = __config.receiptPath+'/pages/form/index?page_id='+this.pageId+'&tenant_id='+this.tenantId+'&app_id='+this.appId+'&component_appid='+this.componentAppId;
				}else if(this.pageType == "5"){
					url = __config.receiptPath+'/pages/img-share/index?page_id='+this.pageId+'&tenant_id='+this.tenantId+'&app_id='+this.appId+'&component_appid='+this.componentAppId;
				}
				let mchData = {
					action: 'jumpOut',
					jumpOutUrl: url //跳转的页面
				}
				var pData = JSON.stringify(mchData);
				parent.postMessage(pData, 'https://payapp.weixin.qq.com')
			}
		}
	};
</script>

<style>
	.goBackButton {
		border: 1px solid black;
		background-color: white;
	}
</style>
