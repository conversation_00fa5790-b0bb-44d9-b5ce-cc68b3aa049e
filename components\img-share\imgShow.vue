<template>
	<view class="imgShowComponent"
		:style="{marginBottom: `${newData.pageMarginBottom}px`,marginTop: `${newData.pageMarginTop}px`,marginLeft: `${newData.pageMarginLeft}px`,marginRight: `${newData.pageMarginRight}px`}">
		<view v-if="newData.showRules == 0">
			<!-- <view v-for="(url,index) in urls "> -->
			<view v-for="(url,index) in shareData.shareImgList ">
				<view class="img_box flex align-center justify-center"
					:style="{backgroundColor:newData.imgBackGround, paddingTop:`${newData.imgPaddingTop}px`,paddingBottom:`${newData.imgPaddingBottom}px`,paddingLeft:`${newData.imgPaddingLeft}px`,paddingRight:`${newData.imgPaddingRight}px`}">
					<image v-if="newData.saveRules==1" class="img_popup" src="../../static/public/img/img-share/lucency.png"></image>
					<image style="width: 100%;" :style="{width:newData.width+`%`}" mode="widthFix" :src="url"></image>
				</view>
			</view>
		</view>
		<view v-if="newData.showRules == 1">
			<!-- <view v-for="(url,index) in urls "> -->
			<view v-for="(url,index) in shareData.shareImgList ">
				<view v-if="getImgShow(index)" class="img_box flex align-center justify-center"
					:style="{backgroundColor:newData.imgBackGround, paddingTop:`${newData.imgPaddingTop}px`,paddingBottom:`${newData.imgPaddingBottom}px`,paddingLeft:`${newData.imgPaddingLeft}px`,paddingRight:`${newData.imgPaddingRight}px`}">
					<image v-if="newData.saveRules==1" class="img_popup" src="../../static/public/img/img-share/lucency.png"></image>
					<image style="width: 100%;display: block;" :style="{width:newData.width+`%`}" mode="widthFix" :src="url"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				shareData: this.imgShareData,
				urls: [
					'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg',
					'https://fuss10.elemecdn.com/1/34/19aa98b1fcb2781c4fba33d850549jpeg.jpeg',
					'https://fuss10.elemecdn.com/0/6f/e35ff375812e6b0020b6b4e8f9583jpeg.jpeg',
					'https://fuss10.elemecdn.com/9/bb/e27858e973f5d7d3904835f46abbdjpeg.jpeg',
					'https://fuss10.elemecdn.com/d/e6/c4d93a3805b3ce3f323f7974e6f78jpeg.jpeg',
					'https://fuss10.elemecdn.com/3/28/bbf893f792f03a54408b3b7a7ebf0jpeg.jpeg',
					'https://fuss10.elemecdn.com/2/11/6535bcfb26e4c79b48ddde44f4b6fjpeg.jpeg'
				]
			};
		},
		watch: {
			imgShareData(val, oldVal) {
				if (val != oldVal) {
					this.shareData = val;
				}
			}
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
			imgShareData: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		created() {},
		mounted() {},
		methods: {
			//完善组件信息
			getImgShow(index) {
				if (index + 1 >= this.newData.minLimit && index + 1 <= this.newData.maxLimit) {
					return true;
				} else {
					return false;
				}
			},
		},
	};
</script>
<style>
	.imgShowComponent{
		height: auto;
	}
	.img_box{
		position: relative;
	}
	.img_popup{
		display: block;
		width: 100%;
		height: 100%;
		position: absolute;
		background-color: rgb(0,0,0,0);
		z-index: 2;
	}
</style>
