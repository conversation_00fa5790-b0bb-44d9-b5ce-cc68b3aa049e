<template>
	<!-- 商品显示 组件 -->
	<view :style="{marginBottom: `${newData.pageSpacing}px`}" >
		<view class="cu-bar justify-center" style="min-height: 80upx;">
			<view class="action text-bold text-sm" :style="{color: `${newData.titleColor}`}" >
				<text class="cuIcon-move "></text> <text class="text-sm" :class="newData.titleIcon"></text>{{newData.title}}<text class="cuIcon-move"></text>
			</view>
		</view>
		<view style="margin-top: -20rpx;margin-bottom: 14rpx;">
			<view v-if="newData.showType=='row'">
				<goods-row :goodsList="newData.goodsList"></goods-row>
			</view>
			<view v-else-if="newData.showType=='card'">
				<goods-card :goodsList="newData.goodsList"></goods-card>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import goodsCard from "components/goods-card/index";
	import goodsRow from "components/goods-row/index";

    export default {
		components: {
			goodsCard,
			goodsRow
		},
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
						showType: 'row',
					    pageSpacing: 0,
					    goodsList: []
	                }
	            }
            }
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
    }
</script>

<style scoped lang="scss">


</style>
