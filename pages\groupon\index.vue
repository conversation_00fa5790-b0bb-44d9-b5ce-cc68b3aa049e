<template>
	<view class="index_page" :style="pageStyle" v-if="pageShowFlag">
		<view >
			<!-- <view v-if="false"> -->
			<!-- 自定义页面组件 -->
			<block v-for="(temp, index) in pageDivData.pageComponent.componentsList" :key="index">
				<!-- 弹窗组件 -->
				<template v-if="temp.componentName === 'popupComponent'">
					<view>
						<div-popup v-model="temp.data"></div-popup>
					</view>
				</template>
				<!-- 首页导航 -->
				<template v-else-if="temp.componentName === 'navTitleComponent'">
					<view>
						<nav-title v-model="temp.data"></nav-title>
					</view>
				</template>
				<!-- 单列图片 -->
				<template v-else-if="temp.componentName === 'imageComponent'">
					<view>
						<div-image v-model="temp.data"></div-image>
					</view>
				</template>
				<!-- 双列图片 -->
				<template v-else-if="temp.componentName === 'doubleRowImageComponent'">
					<view>
						<double-row-image v-model="temp.data"></double-row-image>
					</view>
				</template>
				<!-- 富文本 -->
				<template v-else-if="temp.componentName === 'richTextComponent'">
					<view>
						<base-rich-text v-model="temp.data"></base-rich-text>
					</view>
				</template>
				<!-- 轮播图 -->
				<template v-else-if="temp.componentName === 'swiperComponent'">
					<view>
						<div-swiper v-model="temp.data"></div-swiper>
					</view>
				</template>
				<!-- 消息通知 -->
				<template v-else-if="temp.componentName === 'noticeComponent'">
					<view>
						<div-notice v-model="temp.data"></div-notice>
					</view>
				</template>
				<!-- 标题文字 -->
				<template v-else-if="temp.componentName === 'titleTextComponent'">
					<view>
						<div-title-text v-model="temp.data"></div-title-text>
					</view>
				</template>
				<!-- 导航按钮 -->
				<template v-else-if="temp.componentName === 'navButtonComponent'">
					<view>
						<div-nav-button v-model="temp.data"></div-nav-button>
					</view>
				</template>
				<!-- 悬浮按钮 -->
				<template v-else-if="temp.componentName === 'suspendBtnComponent'">
					<view>
						<suspend-btn v-model="temp.data"></suspend-btn>
					</view>
				</template>
				<!-- 商品封面   注意名字-->
				<template v-else-if="temp.componentName === 'categoryComponent'">
					<view>
						<goods-category v-model="temp.data"></goods-category>
					</view>
				</template>
				<!-- 分割线 -->
				<template v-else-if="temp.componentName === 'cuttingLineComponent'">
					<view>
						<cutting-line v-model="temp.data"></cutting-line>
					</view>
				</template>
				<!-- 背景音乐 -->
				<template v-else-if="temp.componentName === 'musicComComponent'">
					<view>
						<music-com v-model="temp.data"></music-com>
					</view>
				</template>
				<!-- 底部导航 -->
				<template v-else-if="temp.componentName === 'tabBarComponent'">
					<view>
						<cus-tab-bar @bottomHeightShow="bottomHeightShow" v-model="temp.data"></cus-tab-bar>
					</view>
				</template>
				<!-- 多功能按钮 -->
				<template v-else-if="temp.componentName === 'functionButtonComponent'">
					<view v-if="showFunctionButtonComponent(temp.data)">
						<function-button @bottomHeightShow="bottomHeightShow" :order="buttonOrder" v-model="temp.data">
						</function-button>
					</view>
				</template>
				<!-- 地图 -->
				<template v-else-if="temp.componentName === 'mapComponent'">
					<view>
						<map-com v-model="temp.data"></map-com>
					</view>
				</template>
				<!-- 热点图片 -->
				<template v-else-if="temp.componentName === 'areaImageComponent'">
					<view>
						<area-image v-model="temp.data"></area-image>
					</view>
				</template>
				<!-- 视频 -->
				<template v-else-if="temp.componentName === 'videoComponent'">
					<view>
						<div-video v-model="temp.data"></div-video>
					</view>
				</template>
				
				<!-- 底部弹出选择组件 -->
				<template v-else-if="temp.componentName === 'pullUpComponent'">
					<!-- 这个组件默认是隐藏的，只在需要时通过 ref 调用显示 -->
				</template>

				<!-- 团主头像+参加按钮-->
				<template v-else-if="temp.componentName === 'captainComponent'">
					<view>
						<captain :ref="'captainComponent'+index" @joinGroupnAndPay="joinGroupnAndPay" v-model="temp.data" :grouponInfo="grouponInfo"></captain>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'memberComponent'">
					<view>
						<member :ref="'memberComponent'+index" v-model="temp.data" :grouponInfo="grouponInfo"></member>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'rollingListComponent'">
					<view>
						<rollingList :ref="'rollingListComponent'+index" v-model="temp.data" :grouponInfo="grouponInfo">
						</rollingList>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'rollingBarrageComponent'">
					<view>
						<rollingBarrage :ref="'rollingBarrageComponent'+index" v-model="temp.data"
							:grouponInfo="grouponInfo"></rollingBarrage>
					</view>
				</template>
				<!-- 拼团多功能按钮 -->
				<template v-else-if="temp.componentName === 'groupFunctionComponent'">
					<view v-if="showGroupFunctionComponent(temp.data)">
						<group-function @bottomHeightShow="bottomHeightShow"  @joinGroupnAndPay="joinGroupnAndPay" :order="grouponOrder" v-model="temp.data">
						</group-function>
					</view>
				</template>
			</block>
			<view :class="'bg-gray ' + (loadmore3?'loading':'over')"></view>
		</view>
		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog mode="input" title="请输入您的手机号码" message="成功消息" :duration="2000" :before-close="true"
				@close="close" @confirm="confirm"></uni-popup-dialog>
		</uni-popup>
		<uni-popup ref="captainPhoneMessage" type="message">
			<uni-popup-message type="warn" message="请输入正确的手机号码" :duration="2000"></uni-popup-message>
		</uni-popup>
		
		<!-- 限制用户弹窗-->
		<uni-popup ref="limituserpopup" type="dialog">
			<uni-popup-dialog v-if="grouponInfo" :title="grouponInfo.limitRemindMessage" :content="grouponInfo.limitButtonMessage" mode="base"
				:duration="2000" :before-close="true" @close="closeLimituser" @confirm="confirmlLimituser">
			</uni-popup-dialog>
		</uni-popup>
		
		<!-- 是否开新团弹窗-->
		<uni-popup ref="newJoinPopup" type="dialog">
			<uni-popup-dialog title="温馨提示" content="此团已满,请问是否需要开新团" mode="base"
				:duration="2000" :before-close="true" @close="closeNewJoinPopup" @confirm="confirmlNewJoinPopup">
			</uni-popup-dialog>
		</uni-popup>
		
		<!-- <button @click="look">查看是否登录</button> -->
		<!-- 用户隐私政策授权弹框,小程序端首页不弹出 -->
		<!-- #ifdef APP-PLUS -->
		<privacy-policy v-if="showPrivacyPolicy"></privacy-policy>
		<!-- #endif -->
		<bottomRemind :pageDivData="pageDivData"></bottomRemind>
		<view v-if="cusTabBarShowFlag" :style="{height: `${bottomHeight}px`}"></view>
		<!-- 分享组件 可以拉起来的-->
		<share-component v-model="showShare" :shareFriendsCustom="false" :shareParams="shareParams"></share-component>
		
		
		
	</view>

</template>

<script>
	// #ifdef APP-PLUS
	import privacyPolicy from '@/components/privacy-policy/index';
	// #endif
	const app = getApp();
	const util = require("utils/util.js");
	import api from '@/utils/api'
	import goodsCard from "@/components/goods-card/index";
	import goodsRow from "@/components/goods-row/index";
	import divGoods from "@/components/div-components/div-goods/div-goods.vue";
	import divGoodsRow from "@/components/div-components/div-goods-row/div-goods-row.vue";

	import __config from "@/config/env";
	import divNavButton from "@/components/div-components/div-nav-button/div-nav-button.vue";
	import divNotice from "@/components/div-components/div-notice/div-notice.vue";
	import divTitleText from "@/components/div-components/div-title-text/div-title-text.vue";
	import divSwiper from "@/components/div-components/div-swiper/div-swiper.vue";
	import goodsCategory from "@/components/base/category.vue";
	import navTitle from "@/components/base/navTitle.vue";
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import goodsCover from '@/components/goods-cover/goods-cover.vue'
	import suspendBtn from '@/components/base/suspendBtn.vue'
	import divImage from "@/components/div-components/div-image/div-image.vue";
	import doubleRowImage from "@/components/double-row-image/index.vue";
	import baseRichText from "@/components/rich-text/index.vue";
	import cuttingLine from "@/components/base/cuttingLine.vue";
	import musicCom from "@/components/base/musicCom.vue";
	import cusTabBar from "@/components/base/cusTabBar.vue";
	import functionButton from "@/components/base/function-button/functionButton.vue";
	import mapCom from "@/components/base/mapCom.vue";
	import areaImage from "@/components/base/areaImage.vue";
	import divVideo from "@/components/div-components/div-video/div-video.vue";
	import divPopup from "@/components/div-components/div-popup/div-popup.vue";
	import groupFunction from '@/components/groupon/group-function/groupFunction.vue'
	// 拼团组件
	import captain from '@/components/groupon/captain/captain.vue'
	import member from '@/components/groupon/member/member.vue'
	import rollingList from '@/components/groupon/rolling-list/rollingList.vue'
	import rollingBarrage from '@/components/groupon/rolling-barrage/rollingBarrage.vue'
	import pullUpComponent from '@/components/base/pullUpComponent.vue'

	import shareComponent from "@/components/share-component/index"
	import jweixin from '@/utils/jweixin'
	
	import bottomRemind from "@/components/bottom-remind/index.vue";
	export default {
		components: {
			shareComponent,
			// #ifdef APP-PLUS
			privacyPolicy,
			// #endif
			goodsCard,
			goodsRow,
			divImage,
			divSwiper,
			divNavButton,
			divNotice,
			divTitleText,
			divGoods,
			divGoodsRow,
			uniNavBar,
			goodsCover,
			navTitle, //导航栏
			goodsCategory, //分类
			suspendBtn, //悬浮图标
			doubleRowImage, //双列图片
			baseRichText, //富文本
			cuttingLine, //分割线
			musicCom, //背景音乐
			cusTabBar, //底部导航
			functionButton, //功能按钮
			mapCom, //地图按钮
			areaImage, //热点图片
			divVideo, //视频组件
			divPopup, //弹窗组件
			pullUpComponent, //底部弹出选择组件
			//拼团组件
			captain,
			member,
			rollingList,
			rollingBarrage,
			groupFunction,
			bottomRemind, //底部提示
		},
		data() {
			return {
				// 页面配置相关
				showPrivacyPolicy: __config.showPrivacyPolicy,
				CustomBar: this.CustomBar,
				pageStyle: {}, // 页面样式
				pageShowFlag: false, // 页面展示开关
				permitFlag: false, // 页面权限标志
				
				// 加载状态相关
				loadmore: true,
				loadmore3: true,
				loading: false,
				debugInfo: '', // 调试信息
				
				// 页面数据相关
				pageDivData: {
					pageComponent: {
						componentsList: []
					}
				}, // 页面自定义配置组件数据
				
				// 拼团相关
				grouponInfo: {}, // 拼团活动数据
				grouponOrder: {}, // 拼团订单
				buttonOrder: {}, // 按钮订单
				soloFlag: false, // 是否单独成团
				newJoinFlag: false, // 是否开新团
				phone: '', // 拼团使用的电话号码
				selectedSkuData: null, // 选中的SKU数据
				
				// URL参数相关
				pageId: '', // 页面ID
				appId: '', // 应用ID
				tenantId: '', // 租户ID
				groupId: '', // 拼团ID
				sharerFriendId: '', // 分享人ID
				loginStatus: '', // 登录状态
				
				// 分享相关
				showShare: false,
				shareParams: {},
				shareTitle: '', // 分享标题
				shareImageUrl: '', // 分享图片
				
				// 底部菜单相关
				bottomHeight: '', // 底部菜单高度
				cusTabBarShowFlag: false, // 底部菜单是否显示
			};
		},
		props: {},
		onLoad(options) {
			console.log("页面加载 - groupon/index onLoad options:", options);
			
			// 1. 根据不同环境获取并保存URL参数
			this.initPageParams(options);
			
			// 2. 检查登录状态
			this.loginStatus = uni.getStorageSync('third_session') ? '已登录' : '未登录';
			console.log("当前登录状态:", this.loginStatus);
			
			// 3. 初始化页面
			app.initPage().then(res => {
				console.log("页面初始化完成:", res);
				
				// 4. 处理H5环境下的授权回调
				// #ifdef H5
				let code = options.code;
				let state = options.state;
				if (code && state == 'snsapi_userinfo') {
					console.log("检测到授权回调，处理用户信息");
					this.userInfoUpdateByMp({
						jsCode: code,
						scope: state
					});
				}
				// #endif
				
				// 5. 加载页面数据
				this.loadData();
			}).catch(err => {
				console.error("页面初始化错误:", err);
				this.debugInfo = 'initPage错误: ' + JSON.stringify(err);
			});
		},
		onShow() {
			console.log("页面显示 - groupon/index onShow");
			// {{ AURA-X: Add - 页面显示时重新加载数据，确保切换页面后数据是最新的. }}
			// 页面显示时重新加载数据，确保从其他页面返回时数据是最新的
			this.loadData();
		},
		onShareAppMessage: function() {
			// 获取分享标题、描述和图片URL
			let title = this.pageDivData.pageBase.shareTitle || '';
			let imageUrl = this.pageDivData.pageBase.shareImgUrl || '';
			
			// 构建当前页面路径
			let path = 'pages/groupon/index';
			
			// 添加当前页面的查询参数
			const query = [];
			if (this.pageId) query.push('page_id=' + this.pageId);
			if (this.appId) query.push('app_id=' + this.appId);
			if (this.tenantId) query.push('tenant_id=' + this.tenantId);
			if (this.groupId) query.push('group_id=' + this.groupId);
			
			// 添加用户标识
			const userInfo = uni.getStorageSync('user_info');
			if (userInfo && userInfo.id) {
				query.push('sharer_friend_id=' + userInfo.id);
			}
			
			// 拼接查询参数
			if (query.length > 0) {
				path = path + '?' + query.join('&');
			}
			
			return {
				title: title,
				path: path,
				imageUrl: imageUrl,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log(res.errMsg);
					}
				},
				fail: function(res) { // 转发失败
				}
			};
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		onReachBottom() {
		
		},

		methods: {
			getUserProfile() {
				// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
				// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
				console.log("打算桑达大厦");
				// #ifdef MP-WEIXIN
				uni.getUserProfile({
					desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (detail) => {
						console.log("success", detail);

					},
					fail(err) {
						console.log("success", err);
					}
				})
				// #endif
			},
			/**
			 * 获取拼团活动数据
			 * @returns {Promise} 请求Promise
			 */
			getGrouponInfo() {
				// 准备请求参数
				const params = {
					pageId: this.pageId,
					groupId: this.groupId
				};
				
				console.log("获取拼团活动数据, 参数:", params);
				
				return api.getSpellGroup(params)
					.then(res => {
						console.log("拼团活动数据获取成功:", res);
						this.grouponInfo = res.data;
						
						// 如果有拼团数据，保存订单信息
						if (res.data) {
							this.grouponOrder = res.data.orderInfo;
						}
						
						return res;
					})
					.catch(err => {
						console.error("获取拼团数据失败:", err);
						this.debugInfo = '获取拼团数据失败: ' + JSON.stringify(err);
						throw err;
					});
			},
			/**
			 * 加载页面数据
			 * 包括按钮订单、拼团信息和页面组件数据
			 */
			loadData() {
				console.log("开始加载页面数据");
				this.debugInfo = '加载页面数据开始';
				this.loadmore3 = true;
				
				// 1. 准备API请求参数
				const params = this.prepareApiParams();
				console.log("API请求参数:", params,uni.getStorageSync('third_session'));
				if(!uni.getStorageSync('third_session')){
					this.fetchPageDesign(params,false);
					return 
				}
				// 2. 获取按钮订单数据
				this.fetchButtonOrder()
					.then(() => {
						// 3. 获取拼团信息
						return this.getGrouponInfo();
					})
					.then(() => {
						// 4. 获取页面设计数据
						return this.fetchPageDesign(params);
					})
					.catch(err => {
						console.error("数据加载过程中发生错误:", err);
						this.debugInfo = '数据加载错误: ' + JSON.stringify(err);
					});
			},
			/**
			 * 准备API请求参数
			 * @returns {Object} 请求参数对象
			 */
			prepareApiParams() {
				return {
					id: this.pageId,
					appid: this.appId,
					tenantid: this.tenantId,
					shareFriendId: this.sharerFriendId || ''
				};
			},
			/**
			 * 获取按钮订单数据
			 * @returns {Promise} 请求Promise
			 */
			fetchButtonOrder() {
				console.log("获取按钮订单数据, pageId:", this.pageId);
				
				return api.getButtonOrder(this.pageId)
					.then(res => {
						console.log("按钮订单数据获取成功:", res);
						this.debugInfo = '按钮订单获取成功';
						this.buttonOrder = res.data;
						return res;
					})
					.catch(err => {
						console.error("按钮订单获取失败:", err);
						this.debugInfo = '按钮订单获取失败: ' + JSON.stringify(err);
						throw err;
					});
			},
			/**
       * 获取页面设计数据
       * @param {Object} params - 请求参数
       * @param isLogin
       * @returns {Promise} 请求Promise
       */
			fetchPageDesign(params,isLogin=true) {
				console.log("获取页面设计数据, 参数:", params);
        let promise
				if (isLogin){
          promise=api.pagedevise(params)
        }else{
          promise=api.pageDeviseFreedom(params)
        }
				return promise
					.then(res => {
						console.log("页面设计数据获取成功:", res);
						this.debugInfo = '页面数据获取成功';
						
						const pageDivData = res.data;
						if (pageDivData) {
							// 确保pageBase对象存在
							if (!pageDivData.pageBase) {
								pageDivData.pageBase = {};
							}
							
							// 确保必要的属性有默认值
							pageDivData.pageBase.backgroundColor = pageDivData.pageBase.backgroundColor || '';
							pageDivData.pageBase.background = pageDivData.pageBase.background || '';
							pageDivData.pageBase.pageTitle = pageDivData.pageBase.pageTitle || '拼团页面';
							pageDivData.pageBase.shareTitle = pageDivData.pageBase.shareTitle || '';
							pageDivData.pageBase.describe = pageDivData.pageBase.describe || '';
							pageDivData.pageBase.shareImgUrl = pageDivData.pageBase.shareImgUrl || '';
							pageDivData.pageBase.forbidPageUrl = pageDivData.pageBase.forbidPageUrl || '';
							pageDivData.pageBase.permitPageUrl = pageDivData.pageBase.permitPageUrl || '';
							
							// 设置页面数据
							this.pageDivData = pageDivData;
							
							// 设置页面样式
							this.pageStyle = {
								backgroundColor: this.pageDivData.pageBase.backgroundColor,
								backgroundImage: this.pageDivData.pageBase.background ? "url(" + this.pageDivData.pageBase.background + ")" : '',
							};
							
							// 设置页面标题
							uni.setNavigationBarTitle({
								title: this.pageDivData.pageBase.pageTitle
							});

              if (isLogin){

                // 检查页面权限并处理
                console.log("检查页面权限前, permitFlag:", this.pageDivData.permitFlag);
                this.permitFlag = this.pageDivData.permitFlag || false;
                this.checkJump();
                console.log("检查页面权限后, pageShowFlag:", this.pageShowFlag);
              }else{
                this.pageShowFlag=true
              }
							
							// 初始化分享功能
							this.initShareWx();
							
							// 检查页面组件列表
							if (!this.pageDivData.pageComponent || 
								!this.pageDivData.pageComponent.componentsList || 
								this.pageDivData.pageComponent.componentsList.length === 0) {
								console.log("页面组件列表为空");
								this.debugInfo = '页面组件列表为空';
								
								// 确保componentsList是一个空数组而不是null
								if (this.pageDivData.pageComponent) {
									this.pageDivData.pageComponent.componentsList = [];
								}
							}
						} else {
							console.log("未获取到页面数据");
							this.debugInfo = '未获取到页面数据';
							
							// 创建一个默认的页面数据结构
							this.pageDivData = {
								pageBase: {
									backgroundColor: '',
									background: '',
									pageTitle: '拼团页面',
									shareTitle: '',
									describe: '',
									shareImgUrl: '',
									forbidPageUrl: '',
									permitPageUrl: ''
								},
								pageComponent: {
									componentsList: []
								},
								permitFlag: true
							};
						}
						
						return res;
					})
					.catch(err => {
						console.error("页面设计数据获取失败:", err);
						this.debugInfo = '页面数据获取失败: ' + JSON.stringify(err);
						
						// 创建一个默认的页面数据结构
						this.pageDivData = {
							pageBase: {
								backgroundColor: '',
								background: '',
								pageTitle: '拼团页面',
								shareTitle: '',
								describe: '',
								shareImgUrl: '',
								forbidPageUrl: '',
								permitPageUrl: ''
							},
							pageComponent: {
								componentsList: []
							},
							permitFlag: true
						};
						
						// 强制显示页面
						this.pageShowFlag = true;
						
						throw err;
					});
			},
			/**
			 * 加入拼团并支付
			 * @param {Number} type - 购买类型，2表示参团，6表示单独购买
			 * @param {Object} skuData - 选中的商品SKU数据
			 */
			joinGroupnAndPay(type, skuData) {
				// 1. 检查是否已经参加过拼团
				if (this.checkAlreadyJoined()) {
					return;
				}
				
				// 2. 检查用户头像信息是否完整
				if (!this.checkUserInfoComplete()) {
          if (!uni.getStorageSync('third_session')){
            // 获取当前页面信息用于登录后跳转
            let pages = getCurrentPages();
            let currPage = pages[pages.length - 1];
            let reUrl = '';

            if (currPage) {
              let curParam = currPage.options;
              reUrl = '/' + currPage.route;
              if (curParam != null) {
                let param = '';
                for (let key in curParam) {
                  param += '&' + key + '=' + curParam[key];
                }
                param = param.substr(1);
                reUrl = reUrl + '?' + param;
              }
              reUrl = encodeURIComponent(reUrl);
            }
            // 跳转到微信授权登录页面
            uni.navigateTo({
              url: '/pages/login/wechat-auth' + (reUrl ? '?reUrl=' + reUrl : '')
            });
            return
          }
					this.updateUserInfo();
					return;
				}
				
				// 3. 检查手机号码是否需要填写
				if (this.openPhoneBox()) {
					return;
				}
				
				// 4. 设置购买类型（单独购买或参团）
				this.soloFlag = (type === 6);
				
				// 5. 如果传入了skuData，保存它
				if (skuData) {
					this.selectedSkuData = skuData;
				}
				
				// 6. 提交拼团请求
				this.submitJoinGroupRequest();
			},
			/**
			 * 检查用户是否已经参加过拼团
			 * @returns {Boolean} 是否已参加
			 */
			checkAlreadyJoined() {
				if (this.grouponInfo && this.grouponInfo.joinFlag) {
					uni.showToast({
						title: '已经参加过了！',
						icon: 'success',
						duration: 2000
					});
					return true;
				}
				return false;
			},
			/**
			 * 检查用户信息是否完整
			 * @returns {Boolean} 用户信息是否完整
			 */
			checkUserInfoComplete() {
				const userInfo = uni.getStorageSync('user_info');
				return userInfo && userInfo.nickName && userInfo.headimgUrl;
			},
			/**
			 * 提交加入拼团请求
			 */
			submitJoinGroupRequest() {
				this.loading = true;
				
				const params = {
					pageId: this.pageId,
					groupId: this.groupId,
					phone: this.phone,
					soloFlag: this.soloFlag,
					newJoinFlag: this.newJoinFlag,
				};
				
				// 如果有选中的SKU数据，添加到请求参数中
				if (this.selectedSkuData) {
					params.skus = [this.selectedSkuData];
				}
				
				api.joinSpellGroup(params)
					.then((res) => {
						console.log("加入拼团成功:", res.data);
						this.grouponOrder = res.data.orderInfo;
						
						// 处理限制标签提示
						if (res.data.limitFlag) {
							this.$refs.limituserpopup.open();
							return;
						}
						
						// 处理满团提示
						if (res.data.completeFlag) {
							this.$refs.newJoinPopup.open();
							return;
						}
						
						// 进行支付
						this.pay();
					})
					.catch((err) => {
						console.error("加入拼团失败:", err);
						this.loading = false;
					});
			},
			openPhoneBox() {
				if ((!this.phone || this.phone == '') && this.grouponInfo.phoneFlag == "1") {
					this.$refs.popup.open()
					return true
				}
				return false;
			},
			//确认该订单是否支付
			pay() {
				console.log("订单信息",this.grouponOrder)
        let that = this;
				api.wxunifiedOrder({
					id: this.grouponOrder.id,
					// #ifdef MP-WEIXIN
					tradeType: 'JSAPI',
					// #endif
					// #ifdef H5
					tradeType: util.isWeiXinBrowser() ? 'JSAPI' : 'MWEB',
					// #endif
					// #ifdef APP-PLUS
					tradeType: 'APP',
					// #endif
				}).then(res => {
					console.log("res的结果", res)
					this.loading = false;
					if (this.grouponOrder.isPay == 1) {
						return
					}
					if (this.grouponOrder.paymentPrice <= 0) {
						//0元付款
						console.log("0元付款了",this.grouponOrder)
						this.getGrouponInfo()
					} else {
						let payData = res.data;
						// #ifdef MP-WEIXIN
						//微信小程序
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: payData.timeStamp,
							nonceStr: payData.nonceStr,
							package: payData.packageValue,
							signType: payData.signType,
							paySign: payData.paySign,
							success: function(res) {
                that.paySuccess();
							},
							fail: function(res) {
								console.log('fail:' + JSON.stringify(res));
							},
							complete: function(res) {
								console.log(res);
							}
						});
						// #endif
						// #ifdef APP-PLUS
						//app支付
						let grouponOrder = {
							"appid": payData.appId,
							"noncestr": payData.nonceStr,
							"package": payData.packageValue,
							"partnerid": payData.partnerId,
							"prepayid": payData.prepayId,
							"timestamp": payData.timeStamp,
							"sign": payData.sign
						}
						uni.requestPayment({
							provider: 'wxpay',
							grouponOrder: grouponOrder,
							success: function(res) {
                that.paySuccess();
							},
							fail: function(res) {
								console.log('fail:' + JSON.stringify(res));
							},
							complete: function(res) {
								console.log(res);
							}
						});
						// #endif
						// #ifdef H5
						if (util.isWeiXinBrowser()) {
							//公众号H5
							jweixin.payRequest(payData, res => {
                that.paySuccess();
							}, e => {
			
							})
						}
						// #endif
					}
				}).catch(() => {
					this.loading = false;
				});
			},
			refresh() {
				this.loadmore = true;
				this.loadData();
			},
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},
			/**
			 * 更新微信公众号用户信息
			 * @param {Object} parm - 请求参数
			 */
			userInfoUpdateByMp(parm) {
				// #ifdef H5
				// 检查this.$Route是否存在
				if (!this.$Route || !this.$Route.query) {
					console.log('H5环境中this.$Route不存在，跳过URL参数处理');
					return;
				}
				
				let query = this.$Route.query;
				
				// 如果用户信息已经完整，则不需要更新
				if (uni.getStorageSync('user_info') && 
					uni.getStorageSync('user_info').nickName && 
					uni.getStorageSync('user_info').headimgUrl) {
					delete query.code;
					delete query.state;
					util.resetPageUrl(query);
					return;
				}
				
				// 调用API更新用户信息
				api.userInfoUpdateByMp(parm)
					.then(res => {
						// 公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
						delete query.code;
						delete query.state;
						util.resetPageUrl(query);
						
						console.log("授权更新用户信息成功:", res);
						
						// 更新本地存储的用户信息
						if (res && res.data) {
							let userInfo = uni.getStorageSync('user_info') || {};
							userInfo.headimgUrl = res.data.headimgUrl;
							userInfo.nickName = res.data.nickName;
							uni.setStorageSync('user_info', userInfo);
						}
					})
					.catch(err => {
						console.error("授权更新用户信息失败:", err);
					});
				// #endif
				
				// #ifdef MP-WEIXIN
				console.log("小程序环境不需要使用userInfoUpdateByMp方法");
				// #endif
			},
			/**
			 * 更新用户信息
			 * 根据不同环境采用不同的方式获取用户信息
			 */
			updateUserInfo() {
				// #ifdef H5
				this.updateUserInfoInH5();
				// #endif
				
				// #ifdef MP-WEIXIN
				this.updateUserInfoInMiniProgram();
				// #endif
			},
			/**
			 * 在H5环境中更新用户信息
			 * 使用微信公众号授权方式
			 */
			updateUserInfoInH5() {
				// 只在微信浏览器中执行
				if (!util.isWeiXinBrowser()) {
					return;
				}
				
				console.log("H5环境，使用公众号授权获取用户信息");
				
				// 构建授权URL
				const appId = app.globalData.appId;
				const redirectUri = location.href;
				const componentAppId_str = app.globalData.componentAppId 
					? '&component_appid=' + app.globalData.componentAppId 
					: '';
				
				// 跳转到微信授权页面
				location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + appId +
					'&redirect_uri=' + encodeURIComponent(redirectUri) + componentAppId_str +
					'&response_type=code&scope=snsapi_userinfo&state=snsapi_userinfo#wechat_redirect';
			},
			/**
			 * 在小程序环境中更新用户信息
			 * 使用uni.getUserProfile获取用户信息
			 */
			updateUserInfoInMiniProgram() {
				console.log("小程序环境，跳转到用户信息页面");

				
				// 跳转到用户信息页面
				uni.navigateTo({
					url: '/pages/user/user-info/index?pay=true',
					success: () => {
						console.log("成功跳转到用户信息页面");
					},
					fail: (err) => {
						console.error("跳转到用户信息页面失败:", err);
					}
				});
			},
			/**
			 * 更新本地存储的用户信息
			 * @param {Object} userInfo - 用户信息对象
			 */
			updateLocalUserInfo(userInfo) {
				let localUserInfo = uni.getStorageSync('user_info');
				localUserInfo.nickName = userInfo.nickName;
				localUserInfo.headimgUrl = userInfo.avatarUrl;
				uni.setStorageSync('user_info', localUserInfo);
			},
			/**
			 * 显示需要授权的提示
			 */
			showUserAuthorizationToast() {
				uni.showToast({
					title: '需要授权才能参与拼团',
					icon: 'none',
					duration: 2000
				});
			},
			//手机号弹框点击取消按钮触发
			close() {
				this.$refs.popup.close()
			},
			//手机号弹框点击确认按钮触发
			confirm(value) {
				// 输入框的值
				if (!(/^1[3456789]\d{9}$/.test(value))) {
					console.log("格式不正确")
					this.$refs.captainPhoneMessage.open()
					return
				}
				this.phone = value;
				this.$refs.popup.close()
				this.joinGroupnAndPay()
			},
			paySuccess() {
				// {{ AURA-X: Modify - 支付成功后清理上拉组件状态，然后刷新页面数据. }}
				// 清理上拉组件的缓存状态
				this.clearPullUpComponentState();
				// 支付成功后刷新当前页面数据
				this.refresh();
			},
			//打开
			open() {
				console.log("完善组件信息", this.grouponInfo)
				this.$refs.limituserpopup.open()
			},
			//关闭用户限制框
			closeLimituser() {
				this.$refs.limituserpopup.close()
			},
			//确认用户限制框
			confirmlLimituser() {
				this.$refs.limituserpopup.close()
				this.pay();
			},
			//关闭开新团限制框
			closeNewJoinPopup() {
				this.$refs.newJoinPopup.close()
			},
			//确认开新团限制框
			confirmlNewJoinPopup() {
				this.$refs.newJoinPopup.close()
				this.newJoinFlag = true;
				this.joinGroupnAndPay();
			},
			
			
			//判断底部导航栏出现并腾出位置
			bottomHeightShow(obj) {
				// console.log("开了", obj)
				if (obj) {
					this.bottomHeight = obj
					this.cusTabBarShowFlag = true
				}
			},
			//拿取当前页订单信息 用于多功能按钮的显示
			getButtonOrderMsg() {
				//判断有无 支付类型的多功能按钮
				for (let i = 0; i < this.pageDivData.pageComponent.componentsList.length; i++) {
					let obj = this.pageDivData.pageComponent.componentsList[i];
					if (obj.componentName == "functionButtonComponent") { //有无多功能菜单
						console.log("有多功能按钮", obj)
						for (let j = 0; j < obj.data.buttonList.length; j++) {
							if (obj.data.buttonList[j].button.type == 2) { //有无支付按钮
								let pageId = this.pageDivData.id;
								console.log("请求参数", pageId)
								api.getButtonOrder(pageId).then(res => {
									console.log("查询按钮订单", res)
									this.buttonOrder = res.data;
								});
								break;
							}
						}
					}
				}
			},
			//判断是否显示多功能按钮  和拼团多功能按钮
			showFunctionButtonComponent(obj) {
				if (obj.showRule == 1) {
					return true
				} else if (obj.showRule == 2 && this.buttonOrder.isPay == "0") {
					return true
				} else if (obj.showRule == 3 && this.buttonOrder.isPay == "1") {
					return true
				}
				return false
			},
			//拼团判断是否显示多功能按钮  
			showGroupFunctionComponent(obj) {
				if (obj.showRule == 1) {
					return true
				} else if (obj.showRule == 2 && !this.grouponInfo.joinFlag) {
					return true
				} else if (obj.showRule == 3 && this.grouponInfo.joinFlag) {
					return true
				}
				return false
			},
			/**
			 * 修改拼团URL参数
			 * 根据不同环境处理URL参数
			 */
			changeGrouponUrl() {
				// 检查拼团信息是否存在且状态为2
				if (!this.grouponInfo || this.grouponInfo.status !== "2") {
					return;
				}
				
				// 获取团长的groupId
				const list = this.grouponInfo.groupUserList || [];
				let groupId = "";
				for (let i = 0; i < list.length; i++) {
					if (list[i].isLeader) {
						groupId = list[i].groupId;
						break;
					}
				}
				
				if (!groupId) {
					return;
				}
				
				// 根据不同环境处理URL参数
				// #ifdef H5
				// H5环境使用this.$Route.query
				if (this.$Route && this.$Route.query) {
					let query = this.$Route.query;
					query.group_id = groupId;
					util.resetPageUrl(query);
				}
				// #endif
				
				// #ifdef MP-WEIXIN
				// 小程序环境直接更新this.groupId
				this.groupId = groupId;
				// #endif
			},
			/**
			 * 初始化分享功能
			 * 根据不同环境处理分享逻辑
			 */
			initShareWx() {
				// 更新拼团URL参数
				this.changeGrouponUrl();
				
				// #ifdef H5
				// H5环境下的分享逻辑
				this.initShareWxForH5();
				// #endif
				
				// #ifdef MP-WEIXIN
				// 小程序环境下的分享逻辑
				this.initShareWxForMiniProgram();
				// #endif
			},
			/**
			 * H5环境下的分享初始化
			 */
			initShareWxForH5() {
				// 只在微信浏览器中执行
				if (!util.isWeiXinBrowser()) {
					return;
				}
				
				// 准备分享对象
				const shareObj = {
					title: this.pageDivData.pageBase.shareTitle || '',
					desc: this.pageDivData.pageBase.describe || '',
					imgUrl: this.pageDivData.pageBase.shareImgUrl || ''
				};
				
				// 设置分享URL
				const url = util.setH5ShareUrl();
				
				// 检查this.$Route是否存在
				if (!this.$Route || !this.$Route.query) {
					console.log('H5环境中this.$Route不存在，跳过URL参数处理');
					return;
				}
				
				// 重新设置URL参数
				const query = this.$Route.query;
				delete query.code;
				delete query.state;
				query.sharer_friend_id = util.getUrlParam(url, "sharer_friend_id");
				
				// 处理拼团信息
				if (this.grouponInfo && this.grouponInfo.status === "2") {
					const list = this.grouponInfo.groupUserList || [];
					let groupId = "";
					for (let i = 0; i < list.length; i++) {
						if (list[i].isLeader) {
							groupId = list[i].groupId;
							break;
						}
					}
					if (groupId) {
						query.group_id = groupId;
					}
				}
				
				// 重置页面URL
				util.resetPageUrl(query);
				
				// 获取JSSDK配置
				api.getJsSdkConfig({
					url: location.href
				}).then(res => {
					const wxConfig = res.data;
					const shareObjTemp = {
						title: shareObj.title || '',
						desc: shareObj.desc || '',
						link: wxConfig.url,
						imgUrl: shareObj.imgUrl || '',
					};
					
					// 调用分享接口
					jweixin.shareWxFriend(wxConfig, shareObjTemp, function() {
						// 分享成功回调
					}, function(e) {
						// 分享失败回调
					});
				}).catch(res => {
					console.log('调用getJsSdkConfig失败：' + res);
				});
			},
			/**
			 * 小程序环境下的分享初始化
			 */
			initShareWxForMiniProgram() {
				if (this.pageDivData && this.pageDivData.pageBase) {
					// 设置小程序分享标题和图片
					this.shareTitle = this.pageDivData.pageBase.shareTitle || '';
					this.shareImageUrl = this.pageDivData.pageBase.shareImgUrl || '';
				}
			},
			/**
			 * 检查页面访问权限并处理页面跳转
			 * 根据permitFlag和forbidFlag决定页面是否显示或跳转
			 */
			checkJump() {
				console.log("检查页面访问权限");
				
				// 如果页面数据为空，强制显示页面
				if (!this.pageDivData) {
					console.log("页面数据为空，强制显示页面");
					this.pageShowFlag = true;
					this.debugInfo = '页面数据为空，强制显示页面';
					return;
				}
				
				// 检查页面是否被禁止访问
				if (this.pageDivData.forbidFlag) {
					return this.handleForbiddenPage();
				}
				
				// 检查页面是否允许访问
				if (this.pageDivData.permitFlag) {
					console.log("页面允许访问");
					this.debugInfo = '页面允许访问，即将显示';
					this.pageShowFlag = true;
				} else {
					return this.handleUnpermittedPage();
				}
			},
			/**
			 * 处理被禁止访问的页面
			 * 如果有跳转URL则跳转，否则强制显示当前页面
			 */
			handleForbiddenPage() {
				console.log("页面被禁止访问");
				this.debugInfo = '页面被禁止访问';
				
				// 检查是否有跳转页面URL
				if (!this.pageDivData.pageBase || 
					!this.pageDivData.pageBase.forbidPageUrl || 
					this.pageDivData.pageBase.forbidPageUrl.indexOf('/') < 0) {
					// 没有跳转页面，强制显示当前页面
					this.pageShowFlag = true;
					return;
				}
				
				// 根据URL类型和环境执行跳转
				return this.navigateToPage(
					this.pageDivData.pageBase.forbidPageUrl,
					this.pageDivData.pageBase.forbidIsSystemUrl
				);
			},
			/**
			 * 处理未获得访问权限的页面
			 * 如果有跳转URL则跳转，否则强制显示当前页面
			 */
			handleUnpermittedPage() {
				console.log("页面不允许访问");
				this.debugInfo = '页面不允许访问';
				
				// 检查是否有跳转页面URL
				if (!this.pageDivData.pageBase || 
					!this.pageDivData.pageBase.permitPageUrl || 
					this.pageDivData.pageBase.permitPageUrl.indexOf('/') < 0) {
					// 没有跳转页面，强制显示当前页面
					this.pageShowFlag = true;
					return;
				}
				
				// 根据URL类型和环境执行跳转
				return this.navigateToPage(
					this.pageDivData.pageBase.permitPageUrl,
					this.pageDivData.pageBase.permitIsSystemUrl
				);
			},
			/**
			 * 根据环境和URL类型执行页面跳转
			 * @param {String} url - 跳转目标URL
			 * @param {Boolean} isSystemUrl - 是否是系统URL
			 */
			navigateToPage(url, isSystemUrl) {
				if (isSystemUrl) {
					return uni.navigateTo({
						url: url
					});
				} else {
					// #ifdef H5
					return window.location.href = url;
					// #endif
					
					// #ifdef MP-WEIXIN
					// 小程序环境下使用uni.navigateTo
					return uni.navigateTo({
						url: url
					});
					// #endif
				}
			},
			/**
			 * 初始化页面参数，根据不同环境获取参数
			 * @param {Object} options - 页面加载时的参数
			 */
			initPageParams(options) {
				// #ifdef MP-WEIXIN
				// 小程序环境从options直接获取参数
				this.pageId = options.page_id||'1951046883800109057' ||'1950058661469081602' ;
				this.appId = options.app_id || 'wx4c280d1cf1bab14a';
				this.tenantId = options.tenant_id || '1468808703988994048';
				this.groupId = options.group_id || '';
				this.sharerFriendId = options.sharer_friend_id || '';
				console.log("小程序环境参数获取完成:", { 
					pageId: this.pageId, 
					appId: this.appId, 
					tenantId: this.tenantId,
					groupId: this.groupId
				});
				// #endif
				
				// #ifdef H5
				// H5环境使用location.href获取参数
				this.pageId = util.getUrlParam(location.href, "page_id");
				this.appId = util.getUrlParam(location.href, "app_id");
				this.tenantId = util.getUrlParam(location.href, "tenant_id");
				this.groupId = util.getUrlParam(location.href, "group_id");
				this.sharerFriendId = util.getUrlParam(location.href, "sharer_friend_id");
				console.log("H5环境参数获取完成:", { 
					pageId: this.pageId, 
					appId: this.appId, 
					tenantId: this.tenantId,
					groupId: this.groupId
				});
				// #endif
			},
			handlePullUpConfirm(data) {
				console.log("pullUpComponent 确认选择:", data);
				// 保存选中的商品数据
				this.selectedSkuData = data.skuData;
				// 调用加入拼团方法
				this.joinGroupnAndPay(2); // 2表示参团
			},
			// {{ AURA-X: Add - 检查并恢复上拉组件状态. }}
			checkAndRestorePullUpState() {
				console.log('开始检查并恢复上拉组件状态');

				// 递归查找所有组件中的pullUpComponent
				const findPullUpComponent = (component) => {
					if (!component) return null;

					// 检查当前组件是否是pullUpComponent
					if (component.$options.name === 'pullUpComponent') {
						return component;
					}

					// 检查refs中是否有pullUpComponent
					if (component.$refs && component.$refs.pullUpComponent) {
						return component.$refs.pullUpComponent;
					}

					// 递归检查子组件
					if (component.$children && component.$children.length > 0) {
						for (let child of component.$children) {
							const found = findPullUpComponent(child);
							if (found) return found;
						}
					}

					return null;
				};

				const pullUpComponent = findPullUpComponent(this);

				if (pullUpComponent) {
					console.log('找到pullUpComponent，调用状态恢复方法');
					// 调用组件的状态恢复方法
					pullUpComponent.checkAndRestoreState();
				} else {
					console.log('未找到pullUpComponent组件');
				}
			},
			// {{ AURA-X: Add - 清理上拉组件状态. }}
			clearPullUpComponentState() {
				console.log('开始清理上拉组件状态');

				// 递归查找所有组件中的pullUpComponent
				const findPullUpComponent = (component) => {
					if (!component) return null;

					// 检查当前组件是否是pullUpComponent
					if (component.$options.name === 'pullUpComponent') {
						return component;
					}

					// 检查refs中是否有pullUpComponent
					if (component.$refs && component.$refs.pullUpComponent) {
						return component.$refs.pullUpComponent;
					}

					// 递归检查子组件
					if (component.$children && component.$children.length > 0) {
						for (let child of component.$children) {
							const found = findPullUpComponent(child);
							if (found) return found;
						}
					}

					return null;
				};

				const pullUpComponent = findPullUpComponent(this);

				if (pullUpComponent) {
					console.log('找到pullUpComponent，清理状态');
					// 调用组件的状态清理方法
					pullUpComponent.clearStateAfterPayment();
				} else {
					console.log('未找到pullUpComponent组件');
				}
			}
		}
	};
</script>
<style>
	@import "./index.css";
</style>
