<!-- 倒计时组件 -->
<template>
	<view class="countDownComponent " 
	      :style="{marginBottom: `${newData.pageMarginBottom}px`,
	       marginTop: `${newData.pageMarginTop}px`,
	       marginLeft: `${newData.pageMarginLeft}px`,
	       marginRight: `${newData.pageMarginRight}px`}">
		<view class="box" :style="{
         paddingBottom: `${newData.contentPaddingBottom}px`,
         paddingTop: `${newData.contentPaddingTop}px`,
         paddingLeft: `${newData.contentPaddingLeft}px`,
         paddingRight: `${newData.contentPaddingRight}px`,
         backgroundColor:newData.backColor,
         borderTopLeftRadius:`${newData.topBorderRadius}px`,
         borderTopRightRadius:`${newData.topBorderRadius}px`,
         borderBottomLeftRadius:`${newData.bottomBorderRadius}px`,
         borderBottomRightRadius:`${newData.bottomBorderRadius}px`}">
			<view v-if="!shareData.pastFlag" class="time_box " :style="{borderColor:newData.color}">
				<view class="font_day" :style="{backgroundColor:newData.color,color:newData.fontColor}">
					{{ newData.title }}{{day?day:'00'}}天
				</view>
				<view class="font_min">{{hour?hour:'00'}}:{{minute?minute:'00'}}:{{second?second:'00'}}</view>
			</view>
			<view v-if="shareData.pastFlag">
				已经过期啦
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				shareData: this.imgShareData,
				day: null,
				hour: null,
				minute: null,
				second: null,
				timer: null,
				totalTime: 0,
			};
		},
		watch: {
			imgShareData(val, oldVal) {
				if (val != oldVal) {
					this.shareData = val;
					this.caculateDate()
				}
			}
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
			imgShareData: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		computed: {},
		created() {
			this.caculateDate()
		},
		mounted() {},
		methods: {
			caculateDate() {
				var that = this;
				if (this.totalTime == 0) {
					this.totalTime = this.shareData.countDown;
				}
				clearInterval(this.timer);
				this.timer = setInterval(function() {
					var leftTime = that.totalTime - 1000;
					var days = parseInt(leftTime / 1000 / 60 / 60 / 24, 10);
					var hours = parseInt(leftTime / 1000 / 60 / 60 % 24, 10);
					var minutes = parseInt(leftTime / 1000 / 60 % 60, 10);
					var seconds = parseInt(leftTime / 1000 % 60, 10);
					if (leftTime > 0) {
						that.totalTime = leftTime;
						that.day = days > 0 ? that.timeFormat(days) : null;
						that.hour = hours > 0 ? that.timeFormat(hours) : null;
						that.minute = minutes > 0 ? that.timeFormat(minutes) : null;
						that.second = seconds > 0 ? that.timeFormat(seconds) : 0;
					} else {
						//结束
						clearInterval(that.timer);
						setTimeout(function() {
							that.$emit('countDownDone', null);
						}, 2000);
					}
				}, 1000);
			},
			timeFormat(param) {
				//小于10的格式化函数
				return param < 10 ? '0' + param : param;
			}

		},
	};
</script>
<style>
	.box {
		display: flex;
		justify-content: center;
		/* 水平居中 */
		align-items: center;
		/* 垂直居中 */
		text-align: center;
	}

	.time_box {
		display: flex;
		border-radius: 60px;
		justify-content: space-between;
		align-items: center;
		border: 1px solid;
	}

	.font_day {
		font-weight: bolder;
		padding: 4px 20px;
		border-radius: 10px;
	}

	.font_min {
		padding: 4px 10px;
	}
</style>
