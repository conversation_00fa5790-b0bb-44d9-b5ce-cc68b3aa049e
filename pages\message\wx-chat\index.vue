<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">{{title}}</block>
		</cu-custom>
		<view class="cu-chat" id="chatlist">
			<view class="margin-top message">
				<view class="cu-item" :class="item.from_id==username?'self':''" v-for="(item,index) in Chat_Record" :key="index"
				 :id="(item)">
					<view v-if="item.from_id==chater_info.username" class="cu-avatar round" :style="{backgroundImage:'url('+chater_info.extras.avatar+')'}"></view>
					<view class="main">
						<view class="content message-bg" :class="item.from_id==username?'bg-white':''">
							<view v-if="item.msg_type=='custom'">
								<!-- 自定义的消息类型,可以根据类型分别显示不同的消息,类型可自定义 -->
								<base-message :msgBody="item.msg_body"></base-message>
							</view>
							<view v-else><text>{{item.msg_body.text}}</text></view>
						</view>
					</view>
					<view v-if="item.from_id==username" class="cu-avatar round" :style="{backgroundImage:'url('+my_avatar+')'}"></view>
					<view class="date ">{{item.timess}}</view>
				</view>
			</view>
			<view v-if="topMsgContent" style="padding-bottom: 160rpx;">
				<view class="bg-white goods-bg">
					<view class="cu-item">
						<view class="cu-avatar image-box" :style="{'background-image':topMsgContent.imgUrl?'url('+topMsgContent.imgUrl+')':''}"></view>
						<view class="content flex-sub padding-left-sm text-sm margin-top-xs">
							<view class="text-sm overflow-2">{{topMsgContent.name}}</view>
							<view class="cu-tag sm bg-red radius margin-top-xs">{{topMsgContent.type}}</view>
							<view class="margin-top-xs flex justify-between">
								<view class="text-red text-xl text-bold">{{topMsgContent.desc}}</view>
								<view class="cu-btn round sm shadow-blur" @click="sendTopMsg" :class="'bg-'+theme.themeColor">发送客服</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="cu-bar foot input" :style="[{bottom:InputBottom+'px'}]">
			<view class="action">
				<!-- <text class="cuIcon-sound text-grey"></text> -->
			</view>
			<input class="solid-bottom" :adjust-position="false" :focus="false" maxlength="300" cursor-spacing="10" @focus="InputFocus"
			 @blur="InputBlur" v-model="my_say_text"></input>
			<view class="action">
				<!-- <text class="cuIcon-emojifill text-grey"></text> -->
			</view>
			<button class="cu-btn bg-green shadow" @click="to_send()">发送</button>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	// 基础消息类型
	import baseMessage from '@/components/chat-message/index.vue';

	export default {
		components: {
			baseMessage
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				title: '消息',
				username: uni.getStorageSync('user_info').id,
				msgss: null,
				my_avatar: uni.getStorageSync("user_info").headimgUrl,
				chater_info: null,
				Chat_Record: [],
				my_say_text: '',
				InputBottom: 0,
				Time_now: {
					Date: '',
					Y: '',
					M: '',
					D: '',
					h: '',
					m: '',
					s: ''
				},
				topMsgContent: null, // 顶部消息的内容
			};
		},

		props: {},

		onShow() {
			this.to_bottom()
		},
		onLoad(options) {
		
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let userId = options.userId
			this.userId = userId
			app.initJIM().then(res => {
				console.log('initJIM')
				if (!uni.getStorageSync('user_info').id) {
					uni.redirectTo({
						url: '/pages/login/index'
					});
				}
				this.Times_now();
				this.load_chater_info()
				this.to_bottom()
				this.get_msg_ol()
				if (options.goodsSpuId) {
					this.goodsGet(options.goodsSpuId);
				} else if (options.orderInfoId) {
					this.orderGet(options.orderInfoId);
				}
			});

		},

		methods: {
			orderGet(id) {
				let that = this;

				api.orderGet(id).then(res => {
					let orderInfo = res.data;
					let imgUrl = orderInfo.listOrderItem[0].picUrl
					this.topMsgContent = { // 极光自定义消息内容的长度有限制，所以精简一些
						id: orderInfo.id, //ID
						imgUrl: imgUrl, //图片
						name: orderInfo.name, //一级标题
						desc: '¥' + orderInfo.salesPrice, //二级标题
						url: '/pages/order/order-detail/index?id=' + id, //消息跳转的url
						type: '订单', // 消息类型：商品、订单
					}
				});
			},
			goodsGet(id) {
				api.goodsGet(id).then(res => {
					let goodsSpu = res.data;
					let desc = '¥' + goodsSpu.priceDown;
					if (goodsSpu.priceUp && goodsSpu.priceDown != goodsSpu.priceUp) {
						desc = desc + ' - ¥' + goodsSpu.priceUp;
					}
					this.topMsgContent = { // 极光自定义消息内容的长度有限制，所以精简一些
						id: goodsSpu.id, //ID
						imgUrl: goodsSpu.picUrls ? goodsSpu.picUrls[0] : '', //图片
						name: goodsSpu.name, //一级标题
						desc: desc, //二级标题
						url: '/pages/goods/goods-detail/index?id=' + id, //消息跳转的url
						type: '商品', // 消息类型：商品、订单
					}
				});
			},
			sendTopMsg() { // 发送顶部的消息详情
				if (!this.topMsgContent) return;
				let JIM = app.globalData.JIM;
				let that = this;
				JIM.sendSingleCustom({
					'target_username': this.chater_info.username,
					'custom': this.topMsgContent,
				}).onSuccess(function(data, msg) {
					if (data.code == 0) {
						//发送成功了
						that.Times_now();
						var msgss = that.Chat_Record
						console.log(msgss)
						console.log(msg)
						msg.content.timess = that.Time_now.h + that.Time_now.m
						msgss.push(msg.content)
						that.my_say_text = ''
						that.to_bottom()
						that.topMsgContent = null
					} else {
						uni.showToast({
							title: '发送失败'
						})
					}
					console.log(data)
					console.log(msg)
				}).onFail(function(data) {
					console.log(data)
					//data.code 返回码
					//data.message 描述
				});
			},
			load_chater_info() {
				console.log('load_chater_info')
				var that = this;
				var chater_info;
				let JIM = app.globalData.JIM;
				try{
					JIM.getUserInfo({
						'username': this.userId
					}).onSuccess(function(data) {
						console.log(data)
						chater_info = data.user_info
						//data.code 返回码
						//data.message 描述
						//data.user_info.username
						//data.user_info.appkey
						//data.user_info.nickname
						//data.user_info.avatar 头像
						//data.user_info.birthday 生日，默认空
						//data.user_info.gender 性别 0 - 未知， 1 - 男 ，2 - 女
						//data.user_info.signature 用户签名
						//data.user_info.region 用户所属地区
						//data.user_info.address 用户地址
						//data.user_info.mtime 用户信息最后修改时间
						//data.extras 自定义json字段
						that.chater_info = chater_info
						that.title = chater_info.nickname

						//下方初始化聊天记录
						var Chat_Record

						var Chat_Record_ol = []
						let end_time = that.$moment().format('YYYY-MM-DD HH:mm:ss');
						let begin_time = that.$moment().subtract(7, "days").format('YYYY-MM-DD HH:mm:ss');
						api.getJiguangMessages({
							userName: that.username,
							beginTime: begin_time,
							endTime: end_time
						}).then(res => {
							let resData = res && res.data && !res.data.startsWith("<html>")? JSON.parse(res.data) : null
							let lszs = resData ? resData.messages : []
							for (var i = 0; i < lszs.length; i++) {
								if (lszs[i].from_id == that.chater_info.username && lszs[i].target_id == that
									.username || lszs[i].from_id == that.username && lszs[i].target_id == that
									.chater_info.username) {
									Chat_Record_ol.push(lszs[i])
								}
							}
							that.Chat_Record = Chat_Record_ol
							for (var e = 0; e < Chat_Record_ol.length; e++) {
								that.get_message_time(Chat_Record_ol[e].msg_ctime, e)
							}
						});
						that.to_bottom()
						uni.hideLoading()
						//更新会话未读消息数   填对方的username，不要填自己的
						JIM.resetUnreadCount({
							'username': that.chater_info.username
						});
					}).onFail(function(data) {
						console.log(data)
						if (data.code == 882002) {
							uni.showModal({
								title: '提示',
								content: '该客服未初始化，请选择其他客服',
								success() {},
								complete() {
									uni.navigateBack({
										delta: 1
									});
								}
							});
						} else {
							uni.showModal({
								title: '提示',
								content: data.message,
								success() {},
								complete() {

								}
							});
						}
						//data.code 返回码
						//data.message 描述
					});
				} catch (e){
					console.log('JIM catch fail:' + e )
				}
			},
			to_bottom() {
				setTimeout(function() {
					uni.pageScrollTo({
						scrollTop: 300000
					})
				}, 1000);

			},
			Times_now() {
				var that = this;
				var date = new Date();
				var Y = date.getFullYear() + '-';
				var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
				var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
				var h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
				var m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()) + '';
				var s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
				// console.log(date)
				that.Time_now.Date = date;
				that.Time_now.Y = Y;
				that.Time_now.M = M;
				that.Time_now.D = D;
				that.Time_now.h = h;
				that.Time_now.m = m;
				that.Time_now.s = s;
			},
			get_message_time(timestamp, msg_ids) {
				let that = this;
				let Time_now = that.Time_now;

				var date = new Date(timestamp);
				var Y = date.getFullYear() + '-';
				var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
				var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
				var h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
				var m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()) + '';
				var s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
				if (Y == Time_now.Y && M == Time_now.M && D == Time_now.D) {
					that.Chat_Record[msg_ids].timess = h + m;
				} else {
					that.Chat_Record[msg_ids].timess = M + D;
				}
				// return Y + M + D + h + m + s;
			},
			to_send() {
				console.log('to_send')
				var that = this;
				var my_say_text = that.my_say_text
				if (!my_say_text || my_say_text == '') {
					//无内容直接跳出
					return;
				}
				let JIM = app.globalData.JIM;
				console.log(that.chater_info)
				JIM.sendSingleMsg({
					'target_username': that.chater_info.username,
					'content': my_say_text
				}).onSuccess(function(data, msg) {
					//data.code 返回码
					//data.message 描述
					//data.msg_id 发送成功后的消息 id
					//data.ctime_ms 消息生成时间,毫秒
					//data.appkey 用户所属 appkey
					//data.target_username 用户名
					//msg.content 发送成功消息体,见下面消息体详情
					if (data.code == 0) {
						//发送成功了
						that.Times_now();
						var msgss = that.Chat_Record
						console.log(msgss)
						msg.content.timess = that.Time_now.h + that.Time_now.m
						msgss.push(msg.content)
						that.my_say_text = ''
						that.to_bottom()
					} else {
						uni.showToast({
							title: '发送失败'
						})
					}
					console.log(data)
					console.log(msg)
				}).onFail(function(data) {
					console.log(data)
					//data.code 返回码
					//data.message 描述
				});
			},
			get_msg_ol() {
				var that = this;
				uni.$on('msg_ol', function(data) {
					console.log('收到消息，收到消息，收到消息')
					if (data.from_id == that.chater_info.username) {
						that.Times_now();
						var msgss = that.Chat_Record
						data.timess = that.Time_now.h + that.Time_now.m
						msgss.push(data)
						that.to_bottom()
						let JIM = app.globalData.JIM;
						JIM.resetUnreadCount({
							'username': that.chater_info.username
						});
					}
				})
			},

			//编辑框事件
			InputFocus(e) {
				this.InputBottom = e.detail.height
			},
			InputBlur(e) {
				this.InputBottom = 0
			}
		}
	};
</script>
<style>
		.goods-bg {
			height: 240rpx;
			width: 94%;
			border-radius: 10rpx;
			margin: auto;
			box-shadow:0px 5px 5px #e5e5e5;
		}

		.image-box {
			width: 160rpx !important;
			height: 160rpx !important;
			margin: auto;
		}

		.message{
			margin-bottom: 100rpx;
		}

		.message-bg {
			border-radius: 10rpx !important;
			box-shadow:0px 5px 5px #e5e5e5;
			word-break:break-all;
			word-wrap:break-word;
		}
</style>
