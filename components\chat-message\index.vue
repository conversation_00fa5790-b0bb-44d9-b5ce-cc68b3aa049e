<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 聊天消息类型 -->
<!-- 默认(通用)类型 -->
<template>
	<view>
		<navigator :url="msgBody.url">
			<view>
				<view class="cu-avatar image-bg" :style="{'background-image':msgBody.imgUrl?'url('+msgBody.imgUrl+')':''}"></view>
				<view class="text-sm padding-left-xs">
					<view class="text-darkgrey margin-top-sm overflow-2">{{msgBody.name}}</view>
					<text class="cu-tag sm radius bg-red">{{msgBody.type}}</text>
					<view class="text-red text-xl text-bold margin-top-xs">{{msgBody.desc}}</view>
				</view>
			</view>
		</navigator>
	</view>
</template>

<script>
	export default {
		props: {
			msgBody: {//消息体
				type: Object,
				default: {}
			}
		},
		data() {
			return {};
		},
	};
</script>

<style>
	.image-bg{
		width: 390rpx;
		height: 390rpx;
	}
</style>
