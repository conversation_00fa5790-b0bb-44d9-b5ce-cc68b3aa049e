<template>
	<!-- 拼团 -->
	<view class="bg-white">
		<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="groupon-bg">
			<image v-if="newData.backgroundImg" :src="newData.backgroundImg" class="groupon-image"></image>
			<view class="wrapper-list-goods radius" :style="{backgroundColor: newData.background,}" :class="newData.background? newData.background.indexOf('bg-') != -1 ? newData.background : '': '' "
			 v-if="newData.goodsList&&newData.goodsList.length > 0">
				<view class="cu-bar padding-left-sm padding-right-sm">
					<view class="goods-selection text-df margin-top-sm">
						<view class="margin-left-xs text-bold" :style="{color: `${newData.titleColor}`}">{{newData.title}}</view>
						<view class="margin-left-xs text-sm" :style="{color: `${newData.subtitleColor}`}">{{newData.subtitle}}</view>
					</view>
					<navigator :url="'/pages/groupon/groupon-list/index?shopId='+shopId" class="bg-white round margin-top-sm text-sm groupon-more">更多</navigator>
				</view>
				<view class="groupon-list">
					<scroll-view class="scroll-view_x goods-detail margin-top-xs" scroll-x>
						<block v-for="(item, index) in newData.goodsList" :key="index">
							<navigator hover-class="none" :url="'/pages/groupon/groupon-detail/index?id=' + item.id" class="item flex goods-box radius">
								<view class="img-box padding-sm">
									<image :src="item.picUrl ? item.picUrl : '/static/public/img/no_pic.png'"></image>
								</view>
								<view class="text-cut text-sm text-white padding-left-sm padding-right-xs">{{item.name}}</view>
								<view class="padding-left-sm margin-top-xs">
									<text class="cu-tag line-orange radius margin-right-xs sm">{{item.grouponNum?item.grouponNum:1}}人团</text>
									<text class="text-xs" :style="{color: `${newData.titleColor}`}">已有{{item.launchNum?item.launchNum:0}}人参与</text>
								</view>
								<view class="text-price text-white text-bold margin-left-sm margin-top-xs">{{item.grouponPrice}}</view>
							</navigator>
						</block>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
            shopId: {
                type: String
            }
		},
		mounted() {},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				couponInfoList: [],
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.groupon-bg {
		width: 96%;
		height: 498rpx;
		margin: auto;
	}

	.groupon-image {
		width: 96%;
		height: 498rpx;
		margin: auto;
		position: absolute;
	}

	.groupon-more {
		padding: 2px 10px;
		background: rgba(255, 255, 255, 0.71);
	}

	.groupon-list {
		padding-left: 5rpx;
		padding-right: 5rpx;
	}

	.goods-detail {
		width: auto;
		overflow: hidden;
	}

	.wrapper-list-goods {
		height: 498rpx;
		white-space: nowrap;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 240rpx;
		height: 240rpx;
	}

	.wrapper-list-goods .item .img-box {
		width: 100%;
		height: 240rpx;
	}

	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx;
	}
</style>
