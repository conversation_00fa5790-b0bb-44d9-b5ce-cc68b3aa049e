<!--
  - 隐私政策页面
  - 暂时显示空白内容，后续可添加具体隐私政策内容
-->
<template>
	<view class="privacy-container">
		
		<view class="privacy-content">
			<!-- 隐私政策标题 -->
			<view class="privacy-header">
				<text class="privacy-title">隐私保护政策</text>
				<text class="update-time">更新时间：2024年01月01日</text>
			</view>
			
			<!-- 隐私政策内容 -->
			<view class="privacy-body">
				<view class="content-placeholder">
					<text class="placeholder-text">隐私政策内容待完善...</text>
					<text class="placeholder-desc">
						此处将显示详细的隐私保护政策，包括但不限于：
						个人信息收集、使用、存储、共享、保护措施等相关条款。
					</text>
				</view>
				
				<!-- 预留的隐私政策内容结构 -->
				<view class="privacy-section" style="display: none;">
					<text class="section-title">一、我们收集的信息</text>
					<text class="section-content">
						我们会收集您在使用服务过程中主动提供的信息...
					</text>
				</view>
				
				<view class="privacy-section" style="display: none;">
					<text class="section-title">二、信息的使用</text>
					<text class="section-content">
						我们会将收集到的信息用于以下目的...
					</text>
				</view>
				
				<view class="privacy-section" style="display: none;">
					<text class="section-title">三、信息的存储</text>
					<text class="section-content">
						我们会采取合理的安全措施保护您的个人信息...
					</text>
				</view>
				
				<view class="privacy-section" style="display: none;">
					<text class="section-title">四、信息的共享</text>
					<text class="section-content">
						除以下情况外，我们不会与第三方共享您的个人信息...
					</text>
				</view>
				
				<view class="privacy-section" style="display: none;">
					<text class="section-title">五、您的权利</text>
					<text class="section-content">
						您对自己的个人信息享有以下权利...
					</text>
				</view>
				
				<view class="privacy-section" style="display: none;">
					<text class="section-title">六、未成年人保护</text>
					<text class="section-content">
						我们非常重视对未成年人个人信息的保护...
					</text>
				</view>
				
				<view class="privacy-section" style="display: none;">
					<text class="section-title">七、政策更新</text>
					<text class="section-content">
						我们可能会不时更新本隐私政策...
					</text>
				</view>
			</view>
			
			<!-- 底部联系方式 -->
			<view class="privacy-footer">
				<text class="footer-text">
					如您对本隐私政策有任何疑问或建议，请联系我们。
				</text>
			</view>
		</view>
	</view>
</template>

<script>
const app = getApp();

export default {
	data() {
		return {
			theme: app.globalData.theme, // 全局颜色变量
			CustomBar: this.CustomBar, // 自定义导航栏高度
			StatusBar: this.StatusBar // 状态栏高度
		};
	},
	
	onLoad() {
		// 页面加载时的逻辑
	},
	
	methods: {
		// 预留方法：联系我们
		contactUs() {
			// 可以跳转到联系页面或显示联系方式
			uni.showModal({
				title: '联系我们',
				content: '联系功能待开发，请稍后再试',
				showCancel: false
			});
		}
	}
};
</script>

<style scoped>
.privacy-container {
	min-height: 100vh;
	background-color: #fff;
}

.privacy-content {
	padding: 40rpx;
}

.privacy-header {
	text-align: center;
	margin-bottom: 60rpx;
	padding-bottom: 40rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.privacy-title {
	display: block;
	font-size: 44rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.update-time {
	display: block;
	font-size: 24rpx;
	color: #999;
}

.privacy-body {
	line-height: 1.8;
	color: #333;
}

.content-placeholder {
	text-align: center;
	padding: 120rpx 40rpx;
	background-color: #f8f8f8;
	border-radius: 20rpx;
	margin-bottom: 40rpx;
}

.placeholder-text {
	display: block;
	font-size: 32rpx;
	color: #666;
	margin-bottom: 30rpx;
	font-weight: bold;
}

.placeholder-desc {
	display: block;
	font-size: 26rpx;
	color: #999;
	line-height: 1.6;
}

.privacy-section {
	margin-bottom: 40rpx;
}

.section-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.section-content {
	display: block;
	font-size: 28rpx;
	color: #666;
	line-height: 1.8;
	text-indent: 2em;
}

.privacy-footer {
	margin-top: 80rpx;
	padding-top: 40rpx;
	border-top: 2rpx solid #f0f0f0;
	text-align: center;
}

.footer-text {
	font-size: 26rpx;
	color: #999;
}
</style>
