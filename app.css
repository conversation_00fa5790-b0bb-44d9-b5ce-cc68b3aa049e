@import "./public/colorui/main.css";
@import "./public/colorui/icon.css";
@import "./public/colorui/animation.css";

.flex-four{
    flex:4
}
.overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.overflow-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.overflow-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.display-ib{
  display: inline-block
}
.display-i{
  display: inline
}
.margin-top-bar{
  margin-top: 80rpx
}
.margin-bottom-bar{
  margin-bottom: 80rpx
}
.vertical-center{
  margin: auto 0rpx
}
.text-decorat{
  text-decoration:line-through;
}
.mar-top-30{
  margin-top: -30rpx!important
}

.basis-3 {
	flex-basis: 30%;
}

.basis-7 {
	flex-basis: 70%;
}

.bg-gradual-scarlet {
	background-image: linear-gradient(90deg, #e5432e, #e53c43);
	color: #ffffff;
}

.bg-gradual-red {
	background-image: linear-gradient(90deg, #f43f3b, #ec008c);
	color: #ffffff;
}

.bg-gradual-orange {
	background-image: linear-gradient(90deg, #ff9700, #ed1c24);
	color: #ffffff;
}

.bg-gradual-green {
	background-image: linear-gradient(90deg, #39b54a, #8dc63f);
	color: #ffffff;
}

.bg-gradual-purple {
	background-image: linear-gradient(90deg, #9000ff, #5e00ff);
	color: #ffffff;
}

.bg-gradual-pink {
	background-image: linear-gradient(90deg, #ec008c, #6739b6);
	color: #ffffff;
}

.bg-gradual-darkblue {
	background-image: linear-gradient(90deg, #0055ff, #1cbbb4);
	color: #ffffff;
}

.bg-gradual-blue {
	background-image: linear-gradient(90deg, #0081ff, #1cbbb4);
	color: #ffffff;
}

.bg-darkblue {
	background-color: #0055ff;
	color: #ffffff;
}

.bg-scarlet {
	background-color: #e53c43;
	color: #ffffff;
}

.cu-steps.steps-bottom .cu-item::before,
.cu-steps.steps-bottom .cu-item::after {
	left: -10upx;
	bottom: 70upx;
	top: initial;
	}
/* #ifdef H5 */
uni-checkbox[disabled] .uni-checkbox-wrapper .uni-checkbox-input{
	background: #d5d3d8!important;
}
uni-toast{
	z-index: 9999;
}
/* #endif */
