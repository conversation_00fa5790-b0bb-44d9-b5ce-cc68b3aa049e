/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
/**
 * 自动构建路由表 API: http://hhyang.cn/src/router/tutorial/rgRoutes.html#%E5%AE%89%E8%A3%85
 */
// #ifdef H5
const TransformPages = require('uni-read-pages')
const tfPages = new TransformPages()
let filePath = ''
let Timestamp = new Date().getTime()
module.exports = {
	// ... webpack 相关配置
	filenameHashing: false,
    configureWebpack: {
        // 配置输出路径 原有H5打包用的 解决小程序打包后 Component({}) 问题
		/* output: { // 输出重构  打包编译后的 文件目录 文件名称 【模块名称.时间戳】
			filename: `${filePath}[name].js?v=${Timestamp}`,
			chunkFilename: `${filePath}[name].js?v=${Timestamp}`
		}, */
        plugins: [
            new tfPages.webpack.DefinePlugin({
                ROUTES: JSON.stringify(tfPages.routes)
            })
        ]
    }
}
// #endif