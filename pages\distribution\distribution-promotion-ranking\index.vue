<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">推广排行</block>
		</cu-custom>
		<view class="font-weight" :class="'bg-'+theme.backgroundColor">
			<view class="title-text text-center">
				<view v-if="distributionRank.length===0">
					<view class="">榜首虚位以待</view>
				</view>
				<view v-else>
					<view v-if="rankIndex>0">您当前排行第<text class="text-bold title-text-1 margin-left-sm margin-right-sm">{{rankIndex}}</text>位</view>
					<view v-else>
						<view>第1名:<text class="text-xl text-bold margin-left-sm margin-right-sm">{{distributionRank[0].nickName}}</text></view>
						<view class="text-xs padding">只显示前10名排行，您当前暂未达到哦，请继续加油吧~</view>
					</view>
				</view>
			</view>
			<image class="title-image" src="https://joolun-base-test.oss-cn-zhangjiakou.aliyuncs.com/1/material/2ddd78c4-c6c5-44d1-9433-41d9a7f8008d.png"></image>
		</view>
		<view class="cu-list menu-avatar promotion-list bg-white">
			<view v-for="(item,index) in distributionRank" :key="index"  class="flex justify-between align-center font-weight solid-bottom">
				<view class="flex align-center padding">
					<template v-if="index == 0">
						<image  src="/static/public/img/distribution-icon/one_icon.png" style="width: 50rpx;height: 60rpx;"></image>
					</template>
					<template v-else-if="index == 1">
						<image  src="/static/public/img/distribution-icon/two_icon.png" style="width: 50rpx;height: 60rpx;"></image>
					</template>
					<template v-else-if="index == 2">
						<image  src="/static/public/img/distribution-icon/three_icon.png" style="width: 50rpx;height: 60rpx;"></image>
					</template>
					<template v-else>
						<text class=" text-center text-red text-bold" style="width: 50rpx;height: 60rpx;">{{index+1}}</text>
					</template>
					<view class="cu-avatar round lg margin-left"
					:style="item.userInfo.headimgUrl?'background-image:url(' + item.userInfo.headimgUrl + ')':''">{{!item.userInfo.headimgUrl ? '头' : ''}}</view>
					<view class="text-sm margin-left-sm">{{item.userInfo?item.userInfo.nickName:item.userId}}</view>
				</view>
				<view class="text-xl text-red text-price text-bold margin-right-sm text-price margin-right-xl">{{item.commissionTotal}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				userInfo: {},
				distributionRank: [],
				rankIndex: -1,
				page: {
					current: 1,
					size:10,
					//升序字段
					descs: 'commission_total'
				},
			}
		},

		onLoad(){
			this.userInfo = uni.getStorageSync('user_info')
			this.initData();
		},
		methods: {
			initData(){
				// 分销员详情，当前已有佣金金额：commissionTotal
				api.distributionuserPage(this.page).then(res => {
					if(res.data) {
						this.distributionRank = res.data.records;
						if(res.data.records){
							// 排名
							res.data.records.map((item,index)=>{
								if(item.userId == this.userInfo.id){
									this.rankIndex = index + 1;
									return
								}
							})
						}
					}
				});
			}
		}
	}
</script>

<style>
	page{
		background-color: #fff;
	}

	.title-text{
		position: absolute;
		top: 320rpx;
		left: 50rpx;
		color: #ffebac;
	}

	.title-text-1{
		font-size: 68rpx;
		color: #ffebac;
	}

	.title-image{
		width: 100vh;
	}

	.promotion-list{
		border-radius: 30rpx 30rpx 0 0;
		margin-top: -30rpx;
	}

	.font-weight{
		font-weight: 300;
	}
</style>
