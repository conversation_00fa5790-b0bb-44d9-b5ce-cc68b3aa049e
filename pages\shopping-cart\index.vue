<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="bg-white">
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="false">
			<block slot="backText">返回</block>
			<block slot="content">购物车</block>
		</cu-custom>

		<view class="cu-bar bg-gray fixed" style="min-height: 80rpx;">
			<view class="grid col-3 response text-center text-grey text-sm">
				<view><text class="cuIcon-selection margin-right-xs"></text>100%正品保证</view>
				<view><text class="cuIcon-discover margin-right-xs"></text>精挑细选</view>
				<view><text class="cuIcon-squarecheck margin-right-xs"></text>售后无忧</view>
			</view>
		</view>
		<view class="cu-bar bg-white solid-bottom fixed security">
			<view class="action">共{{ shoppingCartData.length }}件宝贝</view>
			<view class="action">
				<button class="cu-btn line-blue sm" @tap="operationFun">{{operation ? '管理' : '完成'}}</button>
			</view>
		</view>
		<view class="shopcart-card">
			<checkbox-group>
				<view class="cu-card" v-for="(shopInfo, shopIndex) in shopInfoShoppingCartData" :key="shopInfo.id">
					<view class="padding-bottom-xl padding-top-xl solid-bottom radius" style="width: 90%; margin: auto;">
						<navigator hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + shopInfo.id">
							<!-- 店铺全选 -->
							<checkbox class="round" :class="shopInfo.checked?theme.themeColor+' checked':''" @tap.stop="shopInfoCheckboxChange(shopInfo)"
							 :checked="shopInfo.checked" :disabled="shopInfo.quantity==0&&operation"></checkbox>
							<view class="cu-avatar sm radius margin-left-sm" :style="'background-image:url(' + shopInfo.imgUrl + ')'"></view>
							<text class="text-black text-bold margin-left-sm">{{shopInfo.name}}</text>
							<text class="cuIcon-right text-sm"></text>
						</navigator>
						<view class="margin-top-xs" v-for="(item, index) in shopInfo.shoppingCartData" :key="index">
							<view class="flex align-center ">
								<checkbox class="round margin-right-sm"
									 :class="item.checked ? theme.themeColor+' checked ':''"
									 @tap="checkboxChange(item,shopInfo)"
									 :value="item.id"
									 :disabled="(item.quantity==0 || item.quantity > item.goodsSku.stock) && operation"
									 :checked="item.checked"></checkbox>
								<navigator hover-class="none" style="width:100%" :url="'/pages/goods/goods-detail/index?id=' + item.spuId">
									<view class="content">
										<view class="flex flex-wrap">
											<image :src="item.goodsSku && item.goodsSku.picUrl ? item.goodsSku.picUrl : item.goodsSpu.picUrls[0]"
											 mode="aspectFill" class="row-img margin-top-sm basis-3"></image>
											<view class="desc row-info padding-left-sm block basis-7">
												<view class="text-black margin-top-sm overflow-2">{{item.goodsSpu.name}}</view>
												<view class="text-gray text-sm margin-top-xs cu-tag round specification" @tap.stop="changeSpecs(item)" :data-spuid="item.goodsSpu.id"
												 :data-index="index" v-if="item.goodsSpu.specType == '1'">
													<text class="overflow-1">
														<text v-if="item.goodsSku" v-for="(item2, index2) in item.specs" :key="index2">{{item2.specValueName}}<text
															 v-if="item.specs.length != (index2+1)">;</text>
														</text>
													</text>
													<text class="cuIcon-unfold"></text>
												</view>
												<view class="flex justify-between" v-if="item.goodsSku">
													<view>
														<view class="text-red text-sm margin-top-sm" v-if="item.goodsSku && (item.addPrice-item.goodsSku.salesPrice) > 0">
															比加入时降<view class="text-price display-ib">{{item.addPrice-item.goodsSku.salesPrice}}</view>
														</view>
													</view>
													<view :class="'text-' + (item.quantity > item.goodsSku.stock ? 'red' : 'gray') + ' margin-top-sm text-right text-sm'">库存{{item.goodsSku.stock}}</view>
												</view>
												<view v-if="item.goodsSku">
													<view class="flex justify-between" @tap.stop>
														<view class="text-price text-bold text-xl text-red margin-top-xs">{{item.goodsSku.salesPrice}}</view>
														<view class="margin-top-xs">
															<base-stepper :stNum="item.quantity" :min="1" :max="item.goodsSku.stock" @numChange="cartNumChang($event,item)"
															 :data-index="index"></base-stepper>
														</view>
													</view>
												</view>
											</view>
										</view>
									</view>
								</navigator>
							</view>
						</view>
					</view>
				</view>
			</checkbox-group>
			<!-- 失效商品 -->
			<view class="cu-bar bg-white solid-bottom margin-top" v-if="shoppingCartDataInvalid.length > 0">
				<view class="action">失效宝贝{{ shoppingCartDataInvalid.length }}件</view>
				<view class="action">
					<button class="cu-btn line-red round sm" @tap="clearInvalid">清空失效宝贝</button>
				</view>
			</view>
			<view class="cu-card article no-card">
				<view class="cu-item" v-for="(item, index) in shoppingCartDataInvalid" :key="index">
					<view class="flex align-center">
						<navigator hover-class="none" style="width:100%" :url="'/pages/goods/goods-detail/index?id=' + item.spuId">
							<view class="content">
								<image :src="item.picUrl"
								 mode="aspectFill" class="row-img margin-top padding-xs"></image>
								<view class="desc row-info padding-left-sm block">
									<view class="text-black margin-top-sm overflow-2">{{item.spuName}}</view>
									<view class="text-gray text-sm margin-top-xs cu-tag round specification" v-if="item.goodsSpu && item.goodsSpu.specType == '1'"
									 @tap.stop="changeSpecs(item)" :data-spuid="item.goodsSpu.id" :data-index="index">
										<text class="overflow-1">
											<text v-if="item.goodsSku" v-for="(item2, index2) in item.specs" :key="index2">{{item2.specValueName}}<text
												 v-if="item.specs.length != (index2+1)">;</text>
											</text>
											<text v-if="!item.goodsSku">{{item.specInfo}}</text>
										</text>
										<text class="cuIcon-unfold"></text>
									</view>
									<view class="text-red" v-if="item.goodsSpu && item.goodsSpu.shelf == '1' && !item.goodsSku">请重新选择规格</view>
									<view class="text-sm margin-top-lg text-red" v-if="!item.goodsSpu || item.goodsSpu.shelf == '0'">已下架</view>
								</view>
							</view>
						</navigator>
					</view>
				</view>
			</view>
			<view :class="'cu-load bg-gray ' + (loadmore?'loading':'')"></view>
			<view class="text-center" v-if="shoppingCartData.length <= 0 && !loadmore">
				<view class="text-xsl margin-top without">
					<image class="margin-top-sm" src="/static/public/img/shopping-cart.jpg"></image>
				</view>购物车空空如也~<navigator hover-class="none" url="/pages/goods/goods-list/index">
					<button class="cu-btn margin-top" :class="'bg-'+theme.themeColor">去逛逛</button>
				</navigator>
			</view>
			<view class="cu-bar justify-center bg-white margin-top-sm">
				<view class="action text-bold" :class="'text-'+theme.themeColor">
					<text class="cuIcon-move"></text> <text class="cuIcon-appreciate"></text>为您推荐<text class="cuIcon-move"></text>
				</view>
			</view>
			<goods-card :goodsList="goodsListRecom"></goods-card>
		</view>
		<view class="cu-bar bg-white tabbar border shop foot">
			<view class="flex align-center">
				<checkbox-group @change="checkboxAllChange"  >
					<checkbox :disabled="isAllDisabled" class="round margin-left"  :class="isAllSelect?theme.themeColor+' checked':''" value="all" :checked="isAllSelect"></checkbox>
				</checkbox-group>
				<view class="text-lg margin-left-xs" @click="checkboxAllClick">全选</view>
			</view>
			<view class="action bar-rt" v-if="operation">
				<text class="text-xs text-bold">合计：</text>
				<text class="text-xl text-bold text-price text-red">{{settlePrice}}</text>
				<button :disabled="showOrderConfirm" class="cu-btn round text-white shadow-blur lg margin-left-sm settle-bt" :class="'bg-'+theme.themeColor"
				 type="" @tap="orderConfirm">结算{{selectValue.length > 0 ? '('+selectValue.length+')' : ''}}</button>
			</view>
			<view class="action bar-rt" v-if="!operation">
				<button class="cu-btn round line-orange" :disabled="showUserCollectAdd" @tap="userCollectAdd">移入收藏夹</button>
				<button class="cu-btn round line-red margin-left-sm" :disabled="showShoppingCartDel" @tap="shoppingCartDel">删除</button>
			</view>
		</view>
		<goods-sku :goodsSpu="goodsSpu" :cartNum="shoppingCartSelect.quantity" @numChange="shoppingCartSelect.quantity = $event"
		 :goodsSku="goodsSku" @changeGoodsSku="goodsSku = $event" :goodsSpecData="goodsSpecData" @changeSpec="goodsSpecData = $event"
		 :shoppingCartId="shoppingCartSelect.id" :modalSku="modalSku" @changeModalSku="modalSku=$event" :modalSkuType="modalSkuType"
		 @operateCartEvent="operateCartEvent" ref="goodsSkuRef"></goods-sku>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import baseStepper from "components/base-stepper/index";
	import goodsSku from "components/goods-sku/index";
	import goodsCard from "components/goods-card/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					current: 1,
					size: 50,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				showOrderConfirm: true,
				showUserCollectAdd: true,
				showShoppingCartDel: true,
				loadmore: true,
				operation: true,
				shopInfoShoppingCartData: [], //将已有数据改为店铺格式数据存储到该属性上
				shoppingCartData: [],
				shoppingCartDataInvalid: [],
				isAllSelect: false,
				//全选
				selectValue: [],
				settlePrice: 0,
				//结算金额
				goodsSpu: {},
				goodsSpecData: [],
				modalSku: false,
				modalSkuType: '1',
				shoppingCartSelect: {
					quantity: 1
				},
				goodsSku: {},
				goodsListRecom: []
			};
		},

		components: {
			baseStepper,
			goodsSku,
			goodsCard
		},
		computed: {
			isAllDisabled:function(){
				if(!this.operation){
					return false;
				}
				let shoppingCartData = this.shoppingCartData;
				let selectValue = [];
				if (shoppingCartData.length > 0) {
					shoppingCartData.forEach(function (shoppingCart, index) {
						//如果是购买操作，需过滤不符商品
						if (!shoppingCart.goodsSku || shoppingCart.goodsSku.stock==0) {
							selectValue.push(shoppingCart.id);
						}
					});
				}
				if(shoppingCartData.length===selectValue.length){
					return true;
				} else {
					return false;
				}
			}
		},
		watch: {
			showOrderConfirm() {},
			selectValue(val) {
				if (this.selectValue.length > 0) {
					this.showOrderConfirm = false;
					this.showUserCollectAdd = false;
					this.showShoppingCartDel = false;
				} else {
					this.showOrderConfirm = true;
					this.showUserCollectAdd = true;
					this.showShoppingCartDel = true;
					this.isAllSelect = false;
				}
			}
		},
		props: {},

		onShow() {
			app.initPage().then(res => {
				this.shoppingCartPage();
			});
		},
		onLoad: function() {
			app.initPage().then(res => {
				this.goodsRecom();
			});
		},
		methods: {
			//管理按键事件
			operationFun() {
				this.operation = !this.operation;
				this.checkboxHandle(this.selectValue);
			},
			//加载数据
			shoppingCartPage() {
				api.shoppingCartPage(this.page).then(res => {
					//更新购物车数量
					app.globalData.shoppingCartCount = res.data.total + '';
					uni.setTabBarBadge({
						index: 3,
						text: app.globalData.shoppingCartCount + ''
					});
					let shoppingCartData = []; //过滤出失效后的商品
					let shoppingCartDataID = []; //过滤出失效后的商品ID
					let shoppingCartDataInvalid = []; //无效的商品

					let shopInfos = []; //调整好店铺格式后的数据
					let shopInfoIds = []; //店铺ID
					res.data.records.forEach(function(shoppingCart, index) {
						shoppingCart.checked = false;
						if (!shoppingCart.goodsSpu || shoppingCart.goodsSpu.shelf == '0' || !shoppingCart.goodsSku) {
							//下架或删除了
							shoppingCartDataInvalid.push(shoppingCart);
						} else {
							shoppingCartDataID.push(shoppingCart.id);
							shoppingCartData.push(shoppingCart);
							if (shopInfoIds.indexOf(shoppingCart.shopInfo.id) === -1) {
								shopInfoIds.push(shoppingCart.shopInfo.id); //保存店铺信息ID
								let shopInfoTemp = JSON.parse(JSON.stringify(shoppingCart.shopInfo));
								shopInfoTemp.shoppingCartData = []; //用来存储店铺的商品信息
								shopInfos.push(shopInfoTemp); //保存店铺信息
							}
						}
					});
					shopInfos.forEach(function(shopInfo) { //遍历店铺信息
						let quantityTemp = 0; //店铺购买总数量
						shoppingCartData.forEach(function(shoppingCart) { //所有商品信息
							if (shoppingCart.shopInfo && shopInfo.id === shoppingCart.shopInfo.id) {
								shopInfo.shoppingCartData.push(shoppingCart);
								quantityTemp += shoppingCart.quantity;
							}
						});
						shopInfo.checked = false;
						shopInfo.quantity = quantityTemp;
					});
					this.shopInfoShoppingCartData = shopInfos; //调整好店铺格式后的数据
					this.shoppingCartData = shoppingCartData;
					this.shoppingCartDataInvalid = shoppingCartDataInvalid;
					this.loadmore = false;
					if (this.selectValue.length > 0) {
						let tempId = [];
						this.selectValue.map((item, index) => {
							if (shoppingCartDataID.indexOf(item) != -1) {
								tempId.push(item);
							}
						});
						this.selectValue = tempId;
						this.checkboxHandle(this.selectValue);
					}
				});
			},
			//推荐商品
			goodsRecom() {
				api.goodsPage({
					searchCount: false,
					current: 1,
					size: 4,
					descs: 'create_time'
				}).then(res => {
					this.goodsListRecom = res.data.records;
				});
			},

			//数量变化
			cartNumChang(val, item) {
				let quantity = Number(val);
				item.quantity = quantity;
				this.shoppingCartUpdate({
					id: item.id,
					quantity: quantity
				});
				this.countSelect();
			},

			shoppingCartUpdate(parm) {
				api.shoppingCartUpdate(parm);
			},

			//收藏
			userCollectAdd() {
				let selectValue = this.selectValue;
				let shoppingCartData = this.shoppingCartData;
				let selectSpuValue = [];
				shoppingCartData.forEach(function(shoppingCart, index) {
					let selectValueIndex = selectValue.indexOf(shoppingCart.id);

					if (selectValueIndex > -1) {
						selectSpuValue.push(shoppingCart.spuId);
					}
				});
				api.userCollectAdd({
					type: '1',
					relationIds: selectSpuValue
				}).then(res => {
					uni.showToast({
						title: '收藏成功',
						icon: 'success',
						duration: 2000
					});
				});
			},

			shoppingCartDel() {
				let selectValue = this.selectValue;
				let that = this;
				if (selectValue.length > 0) {
					uni.showModal({
						content: '确认将这' + selectValue.length + '个宝贝删除',
						cancelText: '我再想想',
						confirmColor: '#ff0000',
						success(res) {
							if (res.confirm) {
								api.shoppingCartDel(selectValue).then(res => {
									that.selectValue = [];
									that.isAllSelect = false;
									that.settlePrice = 0;
									that.shoppingCartPage();
								});
							}
						}

					});
				}
			},

			clearInvalid() {
				let selectValue = [];
				let that = this;
				this.shoppingCartDataInvalid.forEach(function(shoppingCart, index) {
					selectValue.push(shoppingCart.id);
				});
				uni.showModal({
					content: '确认清空失效的宝贝吗',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							api.shoppingCartDel(selectValue).then(res => {
								that.shoppingCartDataInvalid = [];
							});
						}
					}

				});
			},

			isCanBuy(shoppingCart) { //是否购买操作
				return shoppingCart.goodsSku && shoppingCart.quantity <= shoppingCart.goodsSku.stock;
			},
			checkboxChange(item, shopInfo) { //单个商品勾选点击
				item.checked = !item.checked;
				let index = this.selectValue.indexOf(item.id);
				if (item.checked === true) {
					if (index === -1) {
						this.selectValue.push(item.id);
					}
				} else {
					if (index !== -1) {
						this.selectValue.splice(index, 1);
					}
				}
				this.checkboxHandle(this.selectValue);
			},
			shopInfoCheckboxChange(shopInfo) { //单个店铺全选点击
				shopInfo.checked = !shopInfo.checked;
				let that = this;
				let selectValue = this.selectValue;
				if (shopInfo.checked == true) { //店铺全选
					shopInfo.shoppingCartData.forEach(function(shoppingCart, index) {
						shoppingCart.checked = true;
						if (selectValue.indexOf(shoppingCart.id) === -1) {
							selectValue.push(shoppingCart.id);
						}
					});
				} else { //店铺去掉全选
					shopInfo.shoppingCartData.forEach(function(shoppingCart) {
						shoppingCart.checked = false;
						let index = selectValue.indexOf(shoppingCart.id);
						if (index !== -1) {
							selectValue.splice(index, 1);
						}
					});
				}
				this.selectValue = selectValue;
				this.checkboxHandle(this.selectValue);
			},
			checkboxHandle(selectValue) {
				let that = this;
				let shoppingCartData = this.shoppingCartData;
				let isAllSelect = false;
				if (shoppingCartData.length == selectValue.length) {
					isAllSelect = true;
				}
				let selectValueTemp = JSON.parse(JSON.stringify(selectValue)); //临时存取
				if (selectValue.length > 0) {
					let shoppingCartIds = [];
					shoppingCartData.forEach(function(shoppingCart, index) {
						shoppingCartIds.push(shoppingCart.id);
						let selectValueIndex = selectValue.indexOf(shoppingCart.id);
						if (selectValueIndex > -1) {
							if (!that.operation && shoppingCart != 0) {
								shoppingCart.checked = true;
							} else {
								//如果是购买操作，需过滤不符商品
								if (shoppingCart.goodsSku && shoppingCart.quantity != 0 && shoppingCart.quantity <= shoppingCart.goodsSku.stock) {
									shoppingCart.checked = true;
								} else {
									shoppingCart.checked = false;
									selectValue.splice(selectValueIndex, 1);
								}
							}
						} else {
							shoppingCart.checked = false;
						}
					});
					selectValue.forEach(function(obj, index) {
						if (shoppingCartIds.indexOf(obj) <= -1) {
							selectValue.splice(index, 1);
						}
					});
				} else {
					shoppingCartData.forEach(function(shoppingCart, index) {
						shoppingCart.checked = false;
					});
				}
				// 店铺勾选判断
				this.shopInfoShoppingCartData.forEach(function(shopInfo) {
					let dataLength = 0;
					shopInfo.shoppingCartData.forEach(function(val) {
						if (selectValueTemp.indexOf(val.id) > -1) {
							dataLength++;
						} else {
							return;
						}
					});
					if (shopInfo.shoppingCartData.length === dataLength) { //将店铺全选
						shopInfo.checked = true;
					} else {
						shopInfo.checked = false;
					}
				});
				this.shoppingCartData = shoppingCartData;
				this.isAllSelect = isAllSelect;
				this.selectValue = selectValue;
				this.countSelect();
			},
			checkboxAllClick() {
				if(this.isAllDisabled){
					this.isAllSelect = false
				}else{
					this.isAllSelect = !this.isAllSelect;
					this.setAllSelectValue(this.isAllSelect);
				}
			},
			checkboxAllChange(e) {
				var value = e.detail.value;
				if (value.length > 0) {
					this.isAllSelect = true;
					this.setAllSelectValue(true);
				} else {
					this.isAllSelect = false;
					this.setAllSelectValue(false);
				}
			},
			setAllSelectValue(status) {
				let shoppingCartData = this.shoppingCartData;
				let selectValue = [];
				let that = this;
				if (shoppingCartData.length > 0) {
					if (status) {
						shoppingCartData.forEach(function(shoppingCart, index) {
							if (!that.operation) {
								selectValue.push(shoppingCart.id);
							} else {
								//如果是购买操作，需过滤不符商品
								if (shoppingCart.goodsSku && shoppingCart.quantity <= shoppingCart.goodsSku.stock) {
									selectValue.push(shoppingCart.id);
								}
							}
						});
						this.shopInfoShoppingCartData.forEach(function(value) {
							value.checked = true;
						})
					} else {
						this.shopInfoShoppingCartData.forEach(function(value) {
							value.checked = false;
						})
					}
					this.checkboxHandle(selectValue);
				}
			},
			//计算结算值
			countSelect() {
				let selectValue = this.selectValue;
				let settlePrice = 0;
				if (selectValue.length <= 0) {
					this.settlePrice = settlePrice;
				} else {
					this.shoppingCartData.forEach(function(shoppingCart, index) {
						if (selectValue.indexOf(shoppingCart.id) > -1 && shoppingCart.goodsSku && shoppingCart.quantity <= shoppingCart
							.goodsSku.stock) {
							settlePrice = Number(settlePrice) + Number(shoppingCart.quantity) * Number(shoppingCart.goodsSku.salesPrice);
						}
					});
					this.settlePrice = settlePrice.toFixed(2)
				}
			},
			//更换规格
			changeSpecs(item) {
				this.goodsSpu = {};
				this.goodsSpecData = [];
				this.shoppingCartSelect = item;
				this.modalSku = true;
				this.goodsGet(item.spuId);
			},

			goodsGet(id) {
				api.goodsGet(id).then(res => {
					this.goodsSpu = res.data;
					this.goodsSpecGet(id);
				});
			},

			goodsSpecGet(spuId) {
				api.goodsSpecGet({
					spuId: spuId
				}).then(res => {
					let goodsSpecData = res.data;
					let shoppingCartSelect = this.shoppingCartSelect;
					if (shoppingCartSelect.goodsSku) {
						//回显
						shoppingCartSelect.specs.forEach(function(spec, index) {
							goodsSpecData.forEach(function(goodsSpec, index) {
								if (spec.specId == goodsSpec.id) {
									goodsSpec.checked = spec.specValueId;
								}
							});
						});
					}
					this.goodsSpecData = goodsSpecData;
					this.goodsSku = shoppingCartSelect.goodsSku ? shoppingCartSelect.goodsSku : {};
					this.$refs.goodsSkuRef.initSpecData(goodsSpecData);// 购物车这里加载商品信息加载完成后直接调用初始化规格方法
				});
			},

			operateCartEvent() {
				this.shoppingCartPage();
			},

			//结算
			orderConfirm() {
				let params = [];
				let shoppingCartData = this.shoppingCartData;
				shoppingCartData.forEach(function(shoppingCart, index) {
					if (shoppingCart.checked && shoppingCart.goodsSku && shoppingCart.goodsSku.enable == '1' && shoppingCart.goodsSpu &&
						shoppingCart.goodsSpu.shelf == '1' && shoppingCart.quantity <= shoppingCart.goodsSku.stock) {
						let param = {
							spuId: shoppingCart.spuId,
							skuId: shoppingCart.skuId,
							shopInfo: shoppingCart.shopInfo,
							quantity: shoppingCart.quantity,
							salesPrice: shoppingCart.goodsSku.salesPrice,
							spuName: shoppingCart.goodsSpu.name,
							specInfo: shoppingCart.specInfo,
							picUrl: shoppingCart.goodsSku.picUrl ? shoppingCart.goodsSku.picUrl : shoppingCart.goodsSpu.picUrls[0],
							pointsDeductSwitch: shoppingCart.goodsSpu.pointsDeductSwitch,
							pointsDeductScale: shoppingCart.goodsSpu.pointsDeductScale,
							pointsDeductAmount: shoppingCart.goodsSpu.pointsDeductAmount,
							pointsGiveSwitch: shoppingCart.goodsSpu.pointsGiveSwitch,
							pointsGiveNum: shoppingCart.goodsSpu.pointsGiveNum,
							freightTemplat: shoppingCart.goodsSpu.freightTemplat,
							weight: shoppingCart.goodsSku.weight,
							volume: shoppingCart.goodsSku.volume
						};
						params.push(param);
					}
				});
				/* 把参数信息异步存储到缓存当中 */
				uni.setStorage({
					key: 'param-orderConfirm',
					data: params
				});
				uni.navigateTo({
					url: '/pages/order/order-confirm/index'
				});
			}

		}
	};
</script>
<style >
	checkbox::before {
		right: 3px !important;
	}

	checkbox .wx-checkbox-input,
	checkbox .uni-checkbox-input {
		width: 20px;
		height: 20px;
	}

	.security{
		margin-top:80rpx;
		min-height: 80rpx;
		box-shadow:unset !important;
	}
	.shopcart-card {
		margin-top: 160rpx;
		margin-bottom: 100rpx
	}

	.foot {
		/*  #ifdef  H5 */
		bottom: calc(var(--window-bottom))
			/*  #endif  */
	}

	.without {
		margin: auto;
	}

	.without image {
		width: 300rpx;
		height: 305rpx;
	}

	.bar-rt {
		width: 540rpx !important;
		text-align: right !important;
		margin-right: 10rpx !important
	}

	.settle-bt {
		width: 200rpx
	}

	.row-img {
		width: 30% !important;
		border-radius: 10rpx;
		height: 6.4em;
	}

	.row-info {
		width: 60%;
	}

	.row-specs {
		width: 200px;
	}

	.specification {
		white-space: unset !important
	}

	.cu-bar {
		top: unset !important;
	}


</style>
