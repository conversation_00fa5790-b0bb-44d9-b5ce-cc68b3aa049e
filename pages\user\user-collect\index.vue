<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">我的收藏</block>
		</cu-custom>
		<scroll-view scroll-x class="bg-white nav fixed collection-types">
			<view class="flex text-center">
				<view :class="'cu-item flex-sub ' + (index==tabCur?'cur text-'+theme.themeColor:'')" v-for="(item, index) in collectType"
				 :key="index" @tap="tabSelect" :data-index="index" :data-key="item.key">{{item.value}}</view>
			</view>
		</scroll-view>
		<view class="cu-list menu-avatar goods-list">
			<navigator class="goods-item" hover-class="none" 
				:url="'/pages/goods/goods-detail/index?id=' + item.goodsSpu.id" v-if="tabCur == 0"
				:class="'cu-item ' + (modalName=='move-box-'+ index?'move-cur':'')" v-for="(item, index) in userCollect" :key="index"
				@touchstart="ListTouchStartFun" @touchmove="ListTouchMove" @touchend="ListTouchEnd" :data-target="'move-box-' + index">
				<view class="cu-avatar image-box" :style="'background-image:url(' + item.goodsSpu.picUrls[0] + ');'"></view>
				<view class="padding-right-sm goods-detail">
					<view class="text-df overflow-2">{{item.goodsSpu.name}}</view>
					<view class="text-gray text-sm overflow-1">{{item.goodsSpu.sellPoint}}</view>
					<view class="text-gray text-sm">已售{{item.goodsSpu.saleNum}}</view>
					<view class="flex justify-between align-center">
						<view class="text-price text-xl text-bold text-red">{{item.goodsSpu.priceDown}}</view>
						<view class="cu-btn round shadow-blur check-details margin-right-sm" :class="'bg-'+theme.themeColor">查看详情</view>
					</view>
				</view>
				<view class="move" @tap.stop>
					<view class="bg-red " @tap="userCollectDel" :data-index="index">删除</view>
				</view>
			</navigator>
			<navigator class="store-item" hover-class="none" :url="'/pages/shop/shop-detail/index?id=' + item.shopInfo.id" v-if="tabCur == 1"
			 :class="'cu-item ' + (modalName=='move-box-'+ index?'move-cur':'')" v-for="(item, index) in userCollect" :key="index"
			 @touchstart="ListTouchStartFun" @touchmove="ListTouchMove" @touchend="ListTouchEnd" :data-target="'move-box-' + index">
				<view class="cu-avatar image-box" :style="'background-image:url(' + item.shopInfo.imgUrl + ');'"></view>
				<view class="padding-right-sm store-detail">
					<view class="text-df overflow-2">{{item.shopInfo.name}}</view>
					<view class="text-sm text-gray overflow-2 margin-top-xs"><text class="cuIcon-locationfill"></text><text class="margin-left-xs">{{item.shopInfo.address}}</text></view>
					<view class="flex justify-between align-center margin-top-sm">
						<view class="text-sm text-gray margin-top-xs"><text class="cuIcon-mobilefill"></text><text class="margin-left-xs">{{item.shopInfo.phone}}</text></view>
						<view class="cu-btn round shadow-blur check-details margin-right-sm" :class="'bg-'+theme.themeColor">进入店铺</view>
					</view>
				</view>
				<view class="move" @tap.stop>
					<view class="bg-red" @tap="userCollectDel" :data-index="index">删除</view>
				</view>
			</navigator>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				collectType: [{
					value: '商品',
					key: '1'
				}, {
					value: '店铺',
					key: '2'
				}],
				tabCur: 0,
				page: {
					searchCount: false,
					current: 1,
					size: 15,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {
					type: '1'
				},
				loadmore: true,
				userCollect: [],
				ListTouchStart: "",
				ListTouchDirection: "",
				modalName: ""
			};
		},

		components: {},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			app.initPage().then(res => {
				this.userCollectPage();
			});
		},

		onShow(options) {},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.userCollectPage();
			}
		},

		methods: {
			tabSelect(e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.index != this.tabCur) {
					this.tabCur = dataset.index;
					this.parameter.type = dataset.key;
					this.refresh();
				}
			},
			refresh() {
				this.loadmore = true;
				this.userCollect = [];
				this.page.current = 1;
				this.userCollectPage();
			},
			userCollectPage() {
				api.userCollectPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let userCollect = res.data.records;
					this.userCollect = [...this.userCollect, ...userCollect];
					if (userCollect.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			// ListTouch触摸开始
			ListTouchStartFun(e) {
				this.ListTouchStart = e.touches[0].pageX;
			},

			// ListTouch计算方向
			ListTouchMove(e) {
				this.ListTouchDirection = e.touches[0].pageX - this.ListTouchStart > 0 ? 'right' : 'left';
			},

			// ListTouch计算滚动
			ListTouchEnd(e) {
				if (this.ListTouchDirection == 'left') {
					this.modalName = e.currentTarget.dataset.target;
				} else {
					this.modalName = null;
				}
				this.ListTouchDirection = null;
			},

			userCollectDel(e) {
				let that = this;
				let index = e.target.dataset.index;
				let userCollect = this.userCollect;
				uni.showModal({
					content: '确定删除收藏吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',

					success(res) {
						if (res.confirm) {
							api.userCollectDel(userCollect[index].id).then(res => {
								userCollect.splice(index, 1);
								that.userCollect = userCollect;
							});
						}
					}

				});
			}

		}
	};
</script>
<style>
	.collection-types {
		top: unset !important;
	}
	
	.goods-list{
		margin-top: 90rpx;
	}
	
	.goods-item{
		height: 260rpx !important;
	}
	
	.store-item{
		height: 260rpx !important;
	}
	
	.image-box{
		width: 200rpx;
		height: 200rpx;
	}
	
	.goods-detail{
		width: 480rpx;
	}
	
	.store-detail{
		width: 480rpx;
	}
</style>
