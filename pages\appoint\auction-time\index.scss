.container{
	view,text,image{
		box-sizing: border-box;
	}
	scroll-view{
		width: 100%;
		white-space: nowrap;
		height: 150rpx;
		background-color: #fff;
		position: relative;
		padding-top: 20rpx;

		// margin-top:20rpx;
		&::after{
			background: #e5e5e5;
			content: '';
			display:block;
			width: 100%;
			height: 2rpx;
			position: absolute;
			bottom: 0;
			left: 0;
			transform:scaleY(0.5);

		}
		.flex-box{
				display: inline-block;
				height: 120rpx;
				width: 25%;
				margin: 0 7rpx 0 7rpx;
				box-sizing: border-box;

			&.active{
				.date-box{
					 border: none;
					.days{
						font-weight: bold;
						color: #27bb25;
					}
					.date{
						font-weight: bold;
						color: #27bb25;
					}
				}
			}
			.date-box{
				border: none;
				display: flex;
				height: 100rpx;
				flex-direction: column;
				align-items: center;
				justify-content: space-around;
				font-size: 30rpx;
				color: #27bb25;
				.date{
					font-weight: bold;
					color: #27bb25;
					font-size: 30rpx;

				}
			}
		}

	}
.time-box{
	padding:28rpx 12rpx 26rpx;
	display: flex;
	flex-wrap: wrap;
	// margin-top:20rpx;
	background-color:#fff;
	.item{
		width: 25%;
		padding: 0 9rpx;
		margin:20rpx 0;
		&-box{
			width: 100%;
			height: 154rpx;
			padding:0 44rpx;
			background: #fff;
			color: #333;
			border: 2rpx solid #EEEEEE;
			font-size: 28rpx;
			border-radius: 10rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			/* 小程序兼容性优化 */
			box-sizing: border-box;
			/* 小程序不支持transition，使用条件编译 */
			/* #ifndef MP-WEIXIN */
			transition: all 0.3s ease;
			/* #endif */

			/* 小程序特殊处理 */
			/* #ifdef MP-WEIXIN */
			/* 小程序环境下的特殊样式 */
			/* #endif */

			&.disable{
				background: #F1F3F6 !important;
				color: #999 !important;
				// border: 2rpx solid #EEEEEE;
			}
			&.active{
				// background: #0094D7;
				border: 2rpx solid #27bb25;
				font-weight: bold;
			}
			.all{
				font-size: 22rpx;
				padding-top: 10rpx;
			}
		}
	}
}
	.shop_select{
		color: #333;
		padding: 20rpx 40rpx 15rpx 40rpx;
		margin-bottom: 2rpx;
		background-color: #fff;

		.shop-picker-btn {
			background: #fff;
			border: 2rpx solid #e5e5e5;
			border-radius: 8rpx;
			padding: 20rpx 30rpx;
			color: #333;
			font-size: 28rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			min-height: 80rpx;
			box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);

			/* 小程序触摸优化 */
			/* #ifdef MP-WEIXIN */
			min-height: 88rpx;
			/* #endif */

			.picker-arrow {
				color: #999;
				font-size: 24rpx;
				margin-left: 20rpx;
			}

			/* 点击效果 */
			&:active {
				background: #f5f5f5;
				border-color: #27bb25;
				transform: scale(0.98);
			}
		}
	}

	.date_select{
		color: #333;
		width: 100%;
		background-color: #fff;
		padding: 20rpx;

		.more-time-btn {
			background: #27bb25;
			color: white;
			padding: 10rpx 20rpx;
			border-radius: 8rpx;
			font-size: 24rpx;
			margin-left: 20rpx;
			display: inline-block;
			/* 小程序触摸优化 */
			/* #ifdef MP-WEIXIN */
			min-height: 60rpx;
			display: inline-flex;
			align-items: center;
			justify-content: center;
			/* #endif */

			/* 点击效果 */
			&:active {
				background: #1e9e1e;
				transform: scale(0.98);
			}
		}
	}

	.date_select_range{
		display: flex;
		width: 70%;
		line-height: 40rpx;
		align-items: center;
		.cuIcon-back{
			width: 10%;
			/* 小程序触摸优化 */
			/* #ifdef MP-WEIXIN */
			min-height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			/* #endif */
		}
		.cuIcon-center{
			width: 80%;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		.cuIcon-right{
			width: 10%;
			/* 小程序触摸优化 */
			/* #ifdef MP-WEIXIN */
			min-height: 88rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			/* #endif */
		}
	}
	
}