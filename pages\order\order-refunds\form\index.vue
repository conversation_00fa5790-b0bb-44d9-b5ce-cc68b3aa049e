<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<view class="cu-bar bg-white solid-bottom">
			<view class="action">
				<text class="cuIcon-titles" :class="'text-'+theme.themeColor"></text>退款信息</view>
		</view>
		<view class="cu-card no-card article margin-bottom-bar">
			<view class="cu-item">
				<view class="content response align-center">
					<image :src="orderItem.picUrl ? orderItem.picUrl : '/static/public/img/no_pic.png'" mode="aspectFill" class="row-img margin-top-xs"></image>
					<view class="desc row-info block">
						<view class="text-black margin-top-sm overflow-2">{{orderItem.spuName}}</view>
						<view class="text-gray text-sm margin-top-sm overflow-2" v-if="orderItem.specInfo">{{orderItem.specInfo}}</view>
						<view class="flex justify-between align-center">
							<view class="text-price text-gray text-sm margin-top-sm ">{{orderItem.salesPrice}}</view>
							<view class="text-gray text-sm margin-top-sm padding-lr-sm">x{{orderItem.quantity}}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-item solid-top" v-for="(item, index) in orderRefundsList" :key="index">
				<view class="padding-lr padding-tb-xs text-gray">发起时间：{{item.createTime}}</view>
				<view class="padding-lr padding-tb-xs">退款状态：<text class="cu-tag radius line-red">{{item.statusDesc.replace('同意','已')}}</text>
				</view>
				<view class="padding-lr padding-tb-xs">退款金额：<text class="text-price text-bold text-red">{{item.refundAmount}}</text>
				</view>
				<view class="padding-lr padding-tb-xs" v-if="orderItem.paymentPoints">退款积分：<text class="text-bold text-red">{{orderItem.paymentPoints}}</text>
				</view>
				<view class="padding-lr padding-tb-xs">退款数量：x{{orderItem.quantity}}</view>
				<view class="padding-lr padding-tb-xs" v-if="index == 0">是否到账：<text :class="'cu-tag radius line-' + (orderItem.isRefund == '1' ? 'green' : 'red')">{{orderItem.isRefund == '1' ? '是' : '否'}}</text>
				</view>
				<view class="padding-lr padding-tb-xs" v-if="item.refuseRefundReson">拒绝原因：{{item.refuseRefundReson}}</view>
				<view class="padding-lr padding-tb-xs">
					<view>退款原因：</view>
					<view>{{item.refundReson}}</view>
				</view>
			</view>
		</view>
		<view class="cu-bar bg-white tabbar foot justify-end">
<!--			<navigator class="cu-btn round margin-right shadow-blur" open-type="navigate"
				:url="'/pages/customer-service/customer-service-list/index?shopId='+orderItem.shopId">
				<view class="cuIcon-servicefill">联系客服</view>
			</navigator>-->
      <button class="cu-btn round line-blue margin-right cuIcon-servicefill"  @click="openCustomerService">客服
      </button>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderItem: {
					quantity: 0,
				},
				orderRefundsList: []
			};
		},

		components: {},
		props: {},

		onShow() {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.orderItemId = options.orderItemId;
			app.initPage().then(res => {
				this.orderItemGet(this.orderItemId);
			});
		},

		methods: {
			orderItemGet(id) {
				let that = this;
				api.orderItemGet(id).then(res => {
					let orderItem = res.data;
					this.orderItem = orderItem;
					this.orderRefundsList = orderItem.listOrderRefunds;
				});
			},
      openCustomerService() {
        wx.openCustomerServiceChat({
          extInfo: { url: 'https://work.weixin.qq.com/kfid/kfcfd2e702b9ffd3ba7' }, corpId: 'wwd396b79dd20220ba', success(res) {
            console.log(res);
          }, fail(err) {
            console.log(err);

          },
        })
      },

		}
	};
</script>
<style>
	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx
	}
</style>
