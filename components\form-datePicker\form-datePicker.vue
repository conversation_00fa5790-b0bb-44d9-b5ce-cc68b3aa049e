<template>
	<view class="flex justify-center">
	<view class="singleLineFontComponent">
		<view
			:style="{color: `${newData.titleColor}`, fontSize: `${newData.titleSize}px`,fontWeight:`${newData.titleWeight?'bold':'normal'}`}">
			{{newData.title}}<i v-show="newData.required" style="color: #FF0000">*</i>
		</view>
		<view
			class="margin-tb-xs"
			:style="{color: `${newData.describeColor}`, fontSize: `${newData.describeSize}px`,fontWeight:`${newData.describeWeight?'bold':'normal'}`}">
			{{newData.describe}}
		</view>

		<uni-datetime-picker placeholder="点击进行选择" 
		:clearIcon ="true"
		type="date" 
		:value="date" 
		:start="newData.dateLimitBegin"
		:end="newData.dateLimitEnd" @change="bindDateChange" />
	</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				date: '',
			};
		},

		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		methods: {
			bindDateChange(e) {
				console.log("改变时间", e)
				this.date = e;
			},
			checkValue(){
				console.log("检验值了uni-datetime-picker",)
				if(this.newData.required && !this.date){
					uni.showToast( {
						title: "请完善"+this.newData.title+"的内容",
						icon: 'none',
						duration: 2000
					});
					return false;
				}
				return {value:this.date};
			}
		}
	};
</script>
<style>
	.singleLineFontComponent {
		padding: 5px 15px;
		width: 90%;
		border-bottom:1px dashed rgba(0,0,0,.2);
	}
</style>
