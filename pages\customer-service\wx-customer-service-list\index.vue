<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">客服列表</block>
		</cu-custom>

		<view class="cu-list menu-avatar">
		    <navigator :url="'/pages/message/wx-chat/index?userId=' + item.id + params" hover-class="none" class="cu-item" v-for="(item, index) in customerServiceData" :key="index">
		      <view class="cu-avatar round lg" :style="'background-image:url('+item.avatar+');'">
				  <text v-if="!item.avatar" class="cuIcon-people"></text>
			  </view>
		      <view class="content">
		        <view>{{item.nickName?item.nickName:'客服'}}</view>
		      </view>
		    </navigator>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				loadmore: true,
				customerServiceData: [],
				params: '',// url 跳转时额外的参数
			};
		},

		components: {

		},
		props: {},

		onLoad(options) {
			// api.jokelLoginWxMp({}).then(res => {
			// 	let userInfo = res.data;
			// 	//同步执行
			// 	uni.setStorageSync('third_session', userInfo.thirdSession);
			// 	uni.setStorageSync('user_info', userInfo);
			
			// 	console.log("joke----公众号登录成功 ", res)
			// }).catch(res => {
			// 	console.log("joke----公众号登录失败 ", res)
			// });
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let shopId = options.shopId
			if(options.goodsSpuId){
				this.params = '&goodsSpuId=' + options.goodsSpuId;
				this.goodsSpuId = options.goodsSpuId;
			}
			if(options.orderInfoId){
				this.params = '&orderInfoId=' + options.orderInfoId;
			}
			app.initPage().then(res => {
				this.customerServiceList(shopId)
			});
		},

		methods: {
			customerServiceList(shopId){
				api.wxCustomerServiceList().then(res => {
					console.log("客服列表",res)
					let customerServiceData = res.data;
					this.customerServiceData = customerServiceData;
					this.loadmore = false;
				});
			}
		}
	};
</script>
<style>

</style>
