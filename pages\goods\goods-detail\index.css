.product-bg {
	width: 100%;
	position: relative;
}

.product-bg swiper {
	width: 100%;
	height: calc(100vw);
	position: relative;
}

.product-bg .page-index {
	position: absolute;
	right: 30rpx;
	bottom: 30rpx;
}

.cu-bar.tabbar.shop .action {
	width: unset;
}

.to-down {
	margin-bottom: 100rpx
}

.coupon {
	border-radius: 4rpx;
	padding: 8rpx 20rpx 8rpx 20rpx;
	background: radial-gradient(circle at bottom left, transparent 4px, #f9e7d4 0) top left,
		radial-gradient(circle at bottom right, transparent 4px, #f9e7d4 0) top right,
		radial-gradient(circle at top left, transparent 4px, #f9e7d4 0) bottom left,
		radial-gradient(circle at top right, transparent 4px, #f9e7d4 0) bottom right;
	background-repeat: no-repeat;
	background-size: 51% 51%;
}

.show-bg {
	height: 84%;
	margin-top: 120rpx;
}

.image-box {
	height: 90%;
}

.show-btn {
	margin-top: -130rpx;
}

page {
	height: 100%;
}

.index_page {
	height: 100%;
	background-attachment: local;
	background-repeat: no-repeat;
	background-size: 100%;
}
