<template>
	<!-- 滚动列表 -->
	<view class="rollingListComponent">
		<span>
			<span v-show="newData.enterFlag">已有{{newData.enterNum}}人关注 </span>
			<span v-show="newData.payFlag">已有{{newData.payNum}}购买</span>
		</span>
		<view v-for="(item,index) in userList" :key="index">
			<view class="content" :key="item.nickName+index">
				<view class="avatar-img cu-avatar round bg-img lg margin-xs "
					:style="{'background-image':`${'url('+item.headimgUrl+')'}`,height: '30px',width: '30px'}">
				</view>
				<p class="flex-sub flex solid-bottom">{{item.nickName}}</p>
				<p>10分钟前购买</p>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	const util = require("utils/util.js");
	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
			grouponInfo: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		watch: {
			grouponInfo(val, oldVal) {
				if (val != oldVal) {
					this.getList()
					this.getNum()
				}
			}
		},
		mounted() {
			this.getList();
			this.getNum();
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				userList: [], //显示的组员信息
				preUserList: [], //显示的组员信息
				time: '',

			};
		},
		methods: {
			getList() {
				let id = this.grouponInfo.id;
				// console.log("rolong ",this.grouponInfo)
				console.log("滚动列表请求", this.grouponInfo)
				if(!id){
					return
				}
				api.getOrderUser(id).then(res => {
					if(res){
						this.userList = res.data
						console.log("用户信息", res.data)
						if(this.userList.length>0){
							clearInterval(this.timer);
							this.timer = setInterval(() => {
								let obj = JSON.parse(JSON.stringify(this.userList[0]));
								this.userList.splice(0, 1)
								this.userList.push(obj)
								// console.log("结束", this.userList)
							}, 3000)
						}
					}
				}).catch(err=>{
					console.log("拿取订单信息错误", res.data)
				});
			},
			getNum() {
				console.log("getNum", this.grouponInfo)
				if(!this.grouponInfo.pageId){
					return
				}
				api.getUserNum(this.grouponInfo.pageId).then(res => {
					this.newData.enterNum = res.data.enterNum +this.newData.enterNum;
					this.newData.payNum = res.data.payNum +this.newData.payNum;
					console.log("getUserNum", res)
				});
			}
		},

	}
</script>

<style scoped lang="scss">
	.rollingListComponent {
		height: 100px;
		overflow: hidden;
	}

	.avatar-img {
		justify-content: flex-start
	}

	.content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		text-align: center;
		animation: fadein .2s;
		-webkit-animation: fadein 2s
	}

	@keyframes fadein {
		0% {
			transform: translate(0, 100%);
		}

		100% {
			transform: none;
		}
	}
</style>
