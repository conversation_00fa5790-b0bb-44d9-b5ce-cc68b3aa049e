<template>
	<!-- 组件中应用的常用组件  -->
	<!-- 跳转页面组件 -->
	<view @click="goPage()" :class="{'navigator-hover': hover}" :hover-class="hover ? 'navigator-hover' : ''" :hover-start-time="50" :hover-stay-time="100">
		<slot></slot>
	</view>
</template>

<script>
	// 移除对getApp()的依赖，因为该组件实际上并未使用app变量
	import { pageUrls } from '../div-base/div-page-urls.js'

    export default {
	    props: {
            pageUrl: {
                type: [String, null],
	            default: '',
            },
			params: {				//跳转参数
                type: String,
	            default: '',
            },
			isSystemUrl:{            //参数名
				type:Boolean,      //定义传值的类型
			},
			hover: {                 //是否有点击效果
				type: Boolean,
				default: false
			}
	    },
	    components: {},
	    watch: {
			pageUrl(){}
		},
		data() {
            return {
				pageUrls : pageUrls
			};
		},
		methods: {
			goPage() {
				console.log("跳转了",this.isSystemUrl,this.pageUrl)
				if (!this.pageUrl || this.pageUrl.indexOf('/')<0) {
					return;
				}
				if(this.isSystemUrl){
					console.log("检查",this.pageUrls.tabPages)
					if(this.pageUrls.tabPages.indexOf(this.pageUrl)!=-1){
						uni.switchTab({
							url: this.pageUrl
						});
					}else{
						uni.navigateTo({
							url: this.pageUrl
						});
					}
				}else{
					// #ifdef H5
					window.location.href = this.pageUrl;
					// #endif
					
					// #ifdef MP-WEIXIN || APP-PLUS
					// 小程序环境下，外部链接无法直接跳转，可以使用webview组件或复制链接
					if(this.pageUrl.startsWith('http')) {
						// 如果是http链接，可以复制到剪贴板
						uni.setClipboardData({
							data: this.pageUrl,
							success: () => {
								uni.showToast({
									title: '链接已复制，请在浏览器中打开',
									icon: 'none'
								});
							}
						});
					} else {
						// 如果不是http链接，尝试使用navigateTo
						uni.navigateTo({
							url: this.pageUrl,
							fail: (err) => {
								console.error('页面跳转失败:', err);
								uni.showToast({
									title: '页面跳转失败',
									icon: 'none'
								});
							}
						});
					}
					// #endif
				}
			}
		}
    }
</script>

<style scoped lang="scss">
.navigator-hover {
	opacity: 0.7;
	background-color: rgba(0, 0, 0, 0.1);
}
</style>
