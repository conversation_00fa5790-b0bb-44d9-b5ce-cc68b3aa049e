<template>
	<!-- 热点图片组件 -->

	<view  >
		<img id="hot_area_img" usemap="#planetmap" @load="imgLoad" :usemap="'#planetmap'"  style="width: 100%;" mode="widthFix" :src="newData.imageUrl"></img>
		<mapelement v-if="height && width":width="width" :height="height" :data="newData.zones"></mapelement>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	export default {
		components: {
			//render函数创建组件
			'mapelement':{
				props: {
					"data":{},
					"width":'',
					"height":'',
				},
				render: function(createElement){
					console.log("context",this.width)
					console.log("context",this.height)
					let list = [];
					let width =this.width;
					let height =this.height;
					let jumbUrl = function(obj){
						console.log("obj",obj)
							if (!obj.pageUrl || obj.pageUrl.indexOf('/') < 0) {
								// console.log("return")
								return;
							}
							if (obj.isSystemUrl) {
								// console.log("navigateTo",uni)
								uni.navigateTo({
									url: obj.pageUrl
								}).then(()=>{
									 location.reload() 
								});
							} else {
								// console.log("location")
								window.location.href = obj.pageUrl;
							}
						}
					for (var i = 0; i < this.data.length; i++) {
						let zone = this.data[i];
						// console.log('zone',zone)
						let pElem = createElement('area', {
							attrs: {
								//左上角的x1y1 = (width*leftPer,heigh*topPer ) 右下角的x2y2 =（width*(leftPer+widthPer),heigh*(heightPer+topPer)）
								shape: "rect",
								coords: zone.leftPer*width + "," + zone.topPer*height  + ","+
								(zone.leftPer+zone.widthPer)*width  + "," + (zone.heightPer+zone.topPer)*height,
							},
							on: {
								click: function(val){
									jumbUrl(zone);
								}
							}
						});
						list.push(pElem);
					}
					console.log(list)
					return createElement('map', {
						attrs: {
							name: "planetmap",
							id: "planetmap",
							// style:"width:"+width+"px;height:"+height+"px;position:relative"
						}
					}, list)
				},
			},
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			}
		},
		created() {
			
		},
		mounted() {
			console.log("执行1")
		
		},
		watch: {

		},
		data() {
			return {
				newData: this.value, //传入数据
				width:"",
				height:"",
			};
		},
		methods: {
			imgLoad(){
				let query = uni.createSelectorQuery().in(this);
				query.select('#hot_area_img').boundingClientRect(data => {
					 this.height = data.height
					 this.width = data.width
				}).exec();
			},
			jumbUrl(obj){
				if (!obj.pageUrl || obj.pageUrl.indexOf('/') < 0) {
					return;
				}
				if (obj.isSystemUrl) {
					uni.navigateTo({
						url: obj.pageUrl
					}).then(()=>{
						location.reload() 
					});
				} else {
					window.location.href = obj.pageUrl;
				}
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
