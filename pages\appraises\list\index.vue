<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">商品评价</block>
		</cu-custom>
		<view class="cu-list menu-avatar comment">
			<view class="cu-item solid-top" v-for="(item, index) in goodsAppraises" :key="index">
				<view class="cu-avatar round" :style="'background-image:url(' + item.headimgUrl + ')'">{{!item.headimgUrl ? '头' : ''}}</view>
				<view class="content">
					<view class="text-black flex">{{item.nickName ? item.nickName : '匿名'}}
						<view class="text-gray margin-left-sm text-sm">{{item.createTime}}</view>
					</view>
					<view class="text-gray text-sm" v-if="item.specInfo">规格：{{item.specInfo}}</view>
					<base-rade :value="item.goodsScore" size="lg"></base-rade>
					<view class="text-black text-content text-df">{{item.content ? item.content : '此人很懒没写评语'}}</view>
					<view class="bg-gray padding-sm radius margin-top-sm text-sm" v-if="item.sellerReply">
						<view class="flex text-sm cuIcon-mark">
							<view class="text-bold margin-left-xs">卖家回复：</view>{{item.replyTime?item.replyTime:""}}
						</view>
						<view class="text-content">{{item.sellerReply}}</view>
					</view>
				</view>
			</view>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import baseRade from "components/base-rade/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {},
				loadmore: true,
				goodsAppraises: []
			};
		},

		components: {
			baseRade
		},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.parameter.spuId = options.spuId;
			app.initPage().then(res => {
				this.goodsAppraisesPage();
			});
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.goodsAppraisesPage();
			}
		},

		methods: {
			goodsAppraisesPage() {
				api.goodsAppraisesPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let goodsAppraises = res.data.records;
					this.goodsAppraises = [...this.goodsAppraises, ...goodsAppraises];
					if (goodsAppraises.length < this.page.size) {
						this.loadmore = false;
					}
				});
			}

		}
	};
</script>
