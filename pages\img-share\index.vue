<template>
	<view class="index_page" :style="pageStyle" v-if="pageShowFlag">
		<!-- <view class="bg-white" v-if="showFlag" :style="pageDivData.basge"> -->
		<view  v-if="showFlag"  >
			<!-- {{pageDivData.pageBase}} -->
			<block v-for="(temp, index) in pageDivData.pageComponent.componentsList" :key="index">
				<!-- 首页导航 -->
				<template v-if="temp.componentName === 'navTitleComponent'">
					<view>
						<nav-title v-model="temp.data"></nav-title>
					</view>
				</template>
				<!-- 单列图片 -->
				<template v-else-if="temp.componentName === 'imageComponent'">
					<view>
						<div-image v-model="temp.data"></div-image>
					</view>
				</template>
				<!-- 双列图片 -->
				<template v-else-if="temp.componentName === 'doubleRowImageComponent'">
					<view>
						<double-row-image v-model="temp.data"></double-row-image>
					</view>
				</template>
				<!-- 富文本 -->
				<template v-else-if="temp.componentName === 'richTextComponent'">
					<view>
						<base-rich-text v-model="temp.data"></base-rich-text>
					</view>
				</template>
				<!-- 轮播图 -->
				<template v-else-if="temp.componentName === 'swiperComponent'">
					<view>
						<div-swiper v-model="temp.data"></div-swiper>
					</view>
				</template>
				<!-- 消息通知 -->
				<template v-else-if="temp.componentName === 'noticeComponent'">
					<view>
						<div-notice v-model="temp.data"></div-notice>
					</view>
				</template>
				<!-- 标题文字 -->
				<template v-else-if="temp.componentName === 'titleTextComponent'">
					<view>
						<div-title-text v-model="temp.data"></div-title-text>
					</view>
				</template>
				<!-- 导航按钮 -->
				<template v-else-if="temp.componentName === 'navButtonComponent'">
					<view>
						<div-nav-button v-model="temp.data"></div-nav-button>
					</view>
				</template>
				<!-- 悬浮按钮 -->
				<template v-else-if="temp.componentName === 'suspendBtnComponent'">
					<view>
						<suspend-btn v-model="temp.data"></suspend-btn>
					</view>
				</template>
				<!-- 商品分类封面   注意名字-->
				<template v-else-if="temp.componentName === 'categoryComponent'">
					<view>
						<goods-category v-model="temp.data"></goods-category>
					</view>
				</template>
				<!-- 分割线 -->
				<template v-else-if="temp.componentName === 'cuttingLineComponent'">
					<view>
						<cutting-line v-model="temp.data"></cutting-line>
					</view>
				</template>
				<!-- 底部导航 -->
				<template v-else-if="temp.componentName === 'tabBarComponent'">
					<view>
						<cus-tab-bar @bottomHeightShow="bottomHeightShow" v-model="temp.data"></cus-tab-bar>
					</view>
				</template>
				<!-- 多功能按钮 -->
				<template v-else-if="temp.componentName === 'functionButtonComponent'">
					<view v-if="showFunctionButtonComponent(temp.data)">
						<function-button @bottomHeightShow="bottomHeightShow" :order="buttonOrder" v-model="temp.data">
						</function-button>
					</view>
				</template>
				<!-- 地图 -->
				<template v-else-if="temp.componentName === 'mapComponent'">
					<view>
						<map-com v-model="temp.data"></map-com>
					</view>
				</template>
				<!-- 热点图片 -->
				<template v-else-if="temp.componentName === 'areaImageComponent'">
					<view>
						<area-image v-model="temp.data"></area-image>
					</view>
				</template>
				<!-- 倒计时 -->
				<template v-else-if="temp.componentName === 'countDownComponent'">
					<view>
						<countDown :ref="'countDownComponent'+index" v-model="temp.data" :imgShareData="imgShareData">
						</countDown>
					</view>
				</template>
				<!-- 图片展示 -->
				<template v-else-if="temp.componentName === 'imgShowComponent'">
					<view>
						<imgShow :ref="'imgShowComponent'+index" v-model="temp.data" :imgShareData="imgShareData">
						</imgShow>
					</view>
				</template>
				<!-- 热度值 -->
				<template v-else-if="temp.componentName === 'popularityComponent'">
					<view>
						<popularity :ref="'popularityComponent'+index" v-model="temp.data" :imgShareData="imgShareData">
						</popularity>
					</view>
				</template>
				<!-- 进度条 -->
				<template v-else-if="temp.componentName === 'progressComponent'">
					<view>
						<progressCom :ref="'progressComponent'+index" v-model="temp.data" :imgShareData="imgShareData">
						</progressCom>
					</view>
				</template>
				<!-- 分享人 -->
				<template v-else-if="temp.componentName === 'shareMasterComponent'">
					<view>
						<shareMaster :ref="'shareMasterComponent'+index" v-model="temp.data"
							:imgShareData="imgShareData"></shareMaster>
					</view>
				</template>
				<!-- 投票组件 -->
				<template v-else-if="temp.componentName === 'thumbsUpButtonComponent'">
					<view>
						<thumbsUpButton :ref="'thumbsUpButtonComponent'+index" v-model="temp.data"
							@shareVote="shareVote"
							:imgShareData="imgShareData"></thumbsUpButton>
					</view>
				</template>
				<!-- 投票列表 -->
				<template v-else-if="temp.componentName === 'voteListComponent'">
					<view>
						<voteList :ref="'voteListComponent'+index" v-model="temp.data"
							:imgShareData="imgShareData"></voteList>
					</view>
				</template>
			</block>
			<view :class="' bg-gray ' + (loadmore3?'loading':'over')"></view>
		</view>
		<uni-popup  :mask-click="false" ref="popup" type="dialog" style="width:100%"  @touchmove.stop.prevent="moveHandle">
			<view  style="width:680rpx" >
				<image style="width: 100%;display: block;"  mode="widthFix" :src="popupImgSrc"></image>
			</view>
		</uni-popup>
		<!-- <button @click="look">查看是否登录</button> -->
		<!-- 用户隐私政策授权弹框,小程序端首页不弹出 -->
		<!-- #ifdef APP-PLUS -->
		<privacy-policy v-if="showPrivacyPolicy"></privacy-policy>
		<!-- #endif -->
		<bottomRemind :pageDivData="pageDivData"></bottomRemind>
		<view v-if="cusTabBarShowFlag" :style="{height: `${bottomHeight}px`}"></view>
	</view>

</template>

<script>
	// #ifdef APP-PLUS
	import privacyPolicy from '@/components/privacy-policy/index';
	// #endif
	const app = getApp();
	import api from 'utils/api'
	import util from '@/utils/util'
	import __config from "@/config/env";
	//公共组件
	import divNavButton from "@/components/div-components/div-nav-button/div-nav-button.vue";
	import divNotice from "@/components/div-components/div-notice/div-notice.vue";
	import divTitleText from "@/components/div-components/div-title-text/div-title-text.vue";
	import divSwiper from "@/components/div-components/div-swiper/div-swiper.vue";
	import goodsCategory from "@/components/base/category.vue";
	import navTitle from "@/components/base/navTitle.vue";
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import suspendBtn from '@/components/base/suspendBtn.vue'
	import divImage from "@/components/div-components/div-image/div-image.vue";
	import doubleRowImage from "@/components/double-row-image/index.vue";
	import baseRichText from "@/components/rich-text/index.vue";
	import cuttingLine from "@/components/base/cuttingLine.vue";
	import cusTabBar from "@/components/base/cusTabBar.vue";
	import functionButton from "@/components/base/function-button/functionButton.vue";
	import mapCom from "@/components/base/mapCom.vue";	
	import areaImage from "@/components/base/areaImage.vue";
	import jweixin from '@/utils/jweixin'
	import App from '@/App';
	
	import bottomRemind from "@/components/bottom-remind/index.vue";
	export default {
		components: {
			// #ifdef APP-PLUS
			privacyPolicy,
			// #endif
			divImage,
			divSwiper,
			divNavButton,
			divNotice,
			divTitleText,
			uniNavBar,
			navTitle, //导航栏
			goodsCategory, //分类
			suspendBtn, //悬浮图标
			doubleRowImage, //双列图片
			baseRichText, //富文本
			cuttingLine, //分割线
			cusTabBar, //底部导航
			cusTabBar, //底部导航
			functionButton, //底部导航
			mapCom, //底部导航
			areaImage, //热点图片

			//客片分享组件
			countDown: () => import('@/components/img-share/countDown.vue'),
			imgShow: () => import('@/components/img-share/imgShow.vue'),
			popularity: () => import('@/components/img-share/popularity.vue'),
			progressCom: () => import('@/components/img-share/progress.vue'),
			shareMaster: () => import('@/components/img-share/shareMaster.vue'),
			thumbsUpButton: () => import('@/components/img-share/thumbsUpButton.vue'),
			voteList: () => import('@/components/img-share/voteList.vue'),
			bottomRemind, //底部提示
		},
		data() {
			return {
				// theme: app.globalData.theme, //全局颜色变量
				showPrivacyPolicy: __config.showPrivacyPolicy,
				CustomBar: this.CustomBar,
				loadmore: true,
				pageDivData: {
					pageComponent: {
						componentsList: []
					}
				}, //首页自定义配置组件的数据
				showFlag: false,
				popupImgSrc: "",
				scrollLeft: 0,
				cardCur: 0,
				TabCur: 0,
				loadmore3: true, // 自定义页面的加载状态
				imgShareData: {},
				buttonOrder: {},
				bottomHeight: '', // 底部菜单高度
				cusTabBarShowFlag: false, // 底部菜单高度
				pageStyle: {}, // 页面样式
				pageShowFlag: false, // 页面展示开关
			};
		},

		props: {},
		onPageScroll(res) {
			uni.$emit('onPageScroll', res.scrollTop); //传递参数
		},
		// mounted() {
		// 	this.popupImgSrc = "https://createone.oss-cn-hangzhou.aliyuncs.com/1468808703988994048/material/6f0cb35e-a875-4d45-9b42-f07b7ad8efb0.jpg"
		// 	this.$refs.popup.open('center')	
		// },
		onLoad(options) {
			// api.jokelLoginWxMp({}).then(res => {
			// 	let userInfo = res.data;
			// 	//同步执行
			// 	uni.setStorageSync('third_session', userInfo.thirdSession);
			// 	uni.setStorageSync('user_info', userInfo);
			
			// 	console.log("joke----公众号登录成功 ", res)
			// }).catch(res => {
			// 	console.log("joke----公众号登录失败 ", res)
			// });
			app.initPage().then(res => {
				this.loadData();
			});
		},
		onShareAppMessage: function() {
			let title = 'JooLun商城源码-小程序演示';
			let path = 'pages/home/<USER>'
			// let path = util.getCurPage(getCurrentPages());

			const userInfo = uni.getStorageSync('user_info')
			const userCode = userInfo ? '?sharer_user_code=' + userInfo.userCode : ''
			path = path + userCode;
			return {
				title: title,
				path: path,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log(res.errMsg);
					}
				},
				fail: function(res) { // 转发失败
				}
			};
		},
		onReachBottom() {

		},
		methods: {
			loadData() {
				let tenantId = util.getUrlParam(location.href, "tenant_id");
				let appid = util.getUrlParam(location.href, "app_id");
				let pageId = util.getUrlParam(location.href, "page_id");
				let params = {
					id: pageId,
					appId:  util.getUrlParam(location.href, "app_id"),
					tenantid: tenantId,
					shareFriendId: util.getUrlParam(location.href, "sharer_friend_id"),
				}
				this.pageDivData =  {
					pageComponent: {
						componentsList: []
					}
				}
				console.log("请求参数", params)
				api.pagedevise(params).then(res => {
					let pageDivData = res.data;
					console.log("活动页面的结果", this.pageDivData)
					if (pageDivData) {
						this.pageDivData = pageDivData;
						this.pageStyle={
							backgroundColor:this.pageDivData.pageBase.backgroundColor?this.pageDivData.pageBase.backgroundColor:'',
							backgroundImage:  "url("+this.pageDivData.pageBase.background+")",
						}
						//获取用户的数据
						this.initImgShare();
						this.getButtonOrderMsg();
						//判断用户页面限制
						this.checkJump()
						
						if (!this.pageDivData || !this.pageDivData.pageComponent || this.pageDivData.pageComponent.componentsList
							.length == 0) {
							// 如果没有设置自定义页面数据，那就显示默认原始页面
							// this.getDefaultPageData();
						} else {
							this.loadmore3 = false;
						}
					} else {
						// 如果没有设置自定义页面数据，那就显示默认原始页面
						// this.getDefaultPageData();
					}
				}).catch(err => {
					console.log(err)

				});
			},
			initImgShare() {
				let params ={
					id : util.getUrlParam(location.href, "data_id"),
					pageId : util.getUrlParam(location.href, "page_id")
				}
				api.loadImgShareData(params).then(res => {
					this.imgShareData = res.data;
					//发布了显示
					if(this.imgShareData.status && this.imgShareData.status != "0" && this.imgShareData.status != "1"){
						this.showFlag = true;
					}
					//没有发布展示的遮罩
					if(!this.imgShareData.status || this.imgShareData.status == "0" || this.imgShareData.status == "1"){
						this.popupImgSrc = this.imgShareData.unPubCoverImg;
						this.$refs.popup.open('center')
					}else if(this.imgShareData.pastFlag){
						this.popupImgSrc = this.imgShareData.expireCoverImg;
						this.$refs.popup.open('center')
					}
					//设置标题
					let title = this.pageDivData.pageBase.pageTitle?this.pageDivData.pageBase.pageTitle:'';
						title = this.imgShareData.title?this.imgShareData.title : title;
					uni.setNavigationBarTitle({
						title: title
					});

					this.initShareWx();
					console.log("客片分享数据", this.imgShareData)
				}).catch(err => {
					console.log("加载客片分享数据", err)
				});
			},
			setGoodsCategoryParam() {
				/* 把参数信息异步存储到缓存当中 */
				uni.setStorage({
					key: 'param-goods-category-index',
					data: this.TabCur - 1
				});
			},
			cardSwiper(e) {
				this.cardCur = e.detail.current
			},

			refresh() {
				this.loadmore = true;
				this.loadData();
			},
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},
			look() {
				uni.showModal({
					title: '登录信息',
					content: uni.getStorageSync('third_session').toString() + "-------" + JSON.stringify(uni
						.getStorageSync('user_info')),
					success(res) {}
				});
				return
			},
			scroll(event) {
				// 传入scrollTop值并触发所有easy-loadimage组件下的滚动监听事件
				this.scrollTop = event.detail.scrollTop; //这里的detail里有多个数据，可打印出来，根据需要使用
			},
			//判断底部导航栏出现并腾出位置
			bottomHeightShow(obj) {
				// console.log("开了", obj)
				if (obj) {
					this.bottomHeight = obj
					this.cusTabBarShowFlag = true
				}
			},
			//拿取当前页订单信息 用于多功能按钮的显示
			getButtonOrderMsg() {
				//判断有无 支付类型的多功能按钮
				for (let i = 0; i < this.pageDivData.pageComponent.componentsList.length; i++) {
					let obj = this.pageDivData.pageComponent.componentsList[i];
					if (obj.componentName == "functionButtonComponent") { //有无多功能菜单
						console.log("请求参数22", obj)
						for (let j = 0; j < obj.data.buttonList.length; j++) {
							if (obj.data.buttonList[j].button.type == 2) { //有无支付按钮
								let pageId = this.pageDivData.id;
								console.log("请求参数", pageId)
								api.getButtonOrder(pageId).then(res => {
									console.log("查询按钮订单", res)
									this.buttonOrder = res.data;
								});
								return
							}
						}
					}
				}
			},
			//判断是否显示多功能按钮
			showFunctionButtonComponent(obj) {
				if (obj.showRule == 1) {
					return true
				} else if (obj.showRule == 2 && this.buttonOrder.isPay == "0") {
					return true
				} else if (obj.showRule == 3 && this.buttonOrder.isPay == "1") {
					return true
				}
				return false
			},
			//初始化分享
			initShareWx() {
				// //重新设置新的url
				let query = this.$Route.query;
				delete query.code;
				delete query.state;
				if(!query.tenant_id ){
					query.tenant_id =  App.globalData.tenantId
				}
				if(!query.app_id){
					query.app_id =  App.globalData.appId
				}
				if(!query.component_appid){
					query.component_appid =  App.globalData.componentAppId
				}
				console.log("打算1",query)
				query.data_id = this.imgShareData.id;
				util.resetPageUrl(query);
				// h5页面加载时 会默认初始化分享
				// #ifdef H5
				if (util.isWeiXinBrowser()) {
					let shareObj = {
						title: this.pageDivData.pageBase.shareTitle ? this.pageDivData.pageBase.shareTitle : '',
						desc: this.pageDivData.pageBase.describe ? this.pageDivData.pageBase.describe : '',
						imgUrl: this.pageDivData.pageBase.shareImgUrl ? this.pageDivData.pageBase.shareImgUrl : ''
					};
					//更改成客片分享的
					shareObj = {
						title: this.imgShareData.shareTitle ? this.imgShareData.shareTitle : shareObj.title,
						desc: this.imgShareData.shareDescribe ? this.imgShareData.shareDescribe : shareObj.desc,
						imgUrl: this.imgShareData.titleImgUrl ? this.imgShareData.titleImgUrl : shareObj.imgUrl
					};

					// console.log("来分享的参数",shareObj)
					let url = util.setH5ShareUrl();
					// //重新设置新的url
					console.log("微信分享",query)
					query.sharer_friend_id = util.getUrlParam(url, "sharer_friend_id");
					util.resetPageUrl(query);
					// console.log("重选设置url",query)
					api.getJsSdkConfig({
						url: location.href
					}).then(res => {
						// history.replaceState(history.state, null, url);
						let wxConfig = res.data;
						let shareObjTemp = {
							title: shareObj.title ? shareObj.title : '',
							desc: shareObj.desc ? shareObj.desc : '',
							link: wxConfig.url,
							imgUrl: shareObj.imgUrl ? shareObj.imgUrl : '',
						};
						jweixin.shareWxFriend(wxConfig, shareObjTemp, function() {
							// that.showModal = true;
							// uni.hideLoading();
							// console.log("初始化微信分享成功", shareObjTemp)
						}, function(e) {
							// console.log("初始化微信分享成功error",e)
						}, );
					}).catch(res => {
						console.log('调用getJsSdkConfig失败：' + res)
					});
				}
				// #endif
			},
			//检查用户此页面限制
			checkJump(){
				if (this.pageDivData.forbidFlag) {
					if (!this.pageDivData.pageBase.forbidPageUrl || this.pageDivData.pageBase.forbidPageUrl.indexOf('/') <
						0) {
						return;
					}
					if (this.pageDivData.pageBase.forbidIsSystemUrl) {
						return uni.navigateTo({
							url: this.pageDivData.pageBase.forbidPageUrl
						});
					} else {
						return window.location.href = this.pageDivData.pageBase.forbidPageUrl
					}
				}
				if (this.pageDivData.permitFlag) {
					this.pageShowFlag = true;
				}else{
					if (!this.pageDivData.pageBase.permitPageUrl || this.pageDivData.pageBase.permitPageUrl.indexOf('/') <0) {
						return;
					}
					if (this.pageDivData.pageBase.permitIsSystemUrl) {
						return uni.navigateTo({
							url: this.pageDivData.pageBase.permitPageUrl
						});
					} else {
						return window.location.href = this.pageDivData.pageBase.permitPageUrl
					}
				}
			
			},
			//投票更新
			shareVote(){
				console.log("检查到投票")
				this.initImgShare();
			},
			

		}
	};
</script>
<style>
	@import "./index.css";

</style>
