<template>
  <view class="container">
    <text class="title">首页</text>
    <button class="cu-btn round line-blue margin-right cuIcon-servicefill" @click="openCustomerService">客服
    </button>
    <button @click="handleOrderList">dindan</button>
  </view>
</template>

<script>
export default {
  data() {
    return {

    };
  },
  onLoad() {
    console.log('首页加载完成');
  },
  methods: {
    handleOrderList() {
      uni.navigateTo({
        url: "/pages/order/order-list/index?tenant_id=1468808703988994048",
      })
    },
    openCustomerService() {

      wx.openCustomerServiceChat({
        extInfo: { url: 'https://work.weixin.qq.com/kfid/kfcfd2e702b9ffd3ba7' }, corpId: 'wwd396b79dd20220ba', success(res) {
          console.log(res);
        }, fail(err) {
          console.log(err);

        },
      })
    }

  }
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;

  .title {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    margin-top: 100px;
  }
}
</style>