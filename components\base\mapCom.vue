<template>
	<!-- 地图组件 -->
	<view :style="{paddingTop:`${newData.paddingTop}px`,paddingBottom:`${newData.paddingBottom}px`}">
		<view @click="goMap()">
			<view class="flex justify-center align-center padding-tb-sm">
				<view class="flex justify-center align-center" :style="{width:`${newData.width}%`}">
					<view class="flex-sub">
						<view class="text-cut ">
							{{newData.location.obj.title}}
						</view>
					</view>
					<view class="flex justify-end align-center">
						<text class=" cuIcon-location text-red justify-end" />
					</view>

				</view>
			</view>
			<view class="flex justify-center align-center" @touchmove.stop @touch.stop>
				<map :show-compass="true" :style="{width:`${newData.width}%`,height:`${newData.height}px`}" style=" height: 300px;"
					:latitude="latitude" :longitude="longitude" :markers="markerList">
				</map>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
					}
				}
			}
		},
		mounted() {
			this.initMap();
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				latitude: 39.909,
				longitude: 116.39742,
				markerList: [],
			};
		},
		methods: {
			initMap() {
				let marker = {
					id: this.value.location.obj.id, // 使用 marker点击事件 需要填写id
					width: 30,
					title: this.value.location.obj.title,
					latitude: this.value.location.obj.location.lat,
					longitude: this.value.location.obj.location.lng,
					iconPath: '/static/public/img/map/market.png'
				}
				this.markerList.push(marker);
				this.latitude = this.value.location.obj.location.lat;
				this.longitude = this.value.location.obj.location.lng;
			},
			goMap() {
				uni.openLocation({
					latitude: this.value.location.obj.location.lat,
					longitude: this.value.location.obj.location.lng,
					name: this.value.location.obj.title,
					address: this.value.location.obj.address,
				})
			}
		}
	}
</script>

<style scoped lang="scss">

</style>
