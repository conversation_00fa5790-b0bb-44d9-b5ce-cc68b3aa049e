<template>
	<view class="popularityComponent" :style="{marginBottom: `${newData.pageMarginBottom}px`,
       marginLeft: `${newData.pageMarginLeft}px`,
       marginRight: `${newData.pageMarginRight}px`,
       marginTop: `${newData.pageMarginTop}px`}">
		<view class="flex justify-center align-center" :style="{backgroundColor: newData.backColor,
          borderTopLeftRadius:`${newData.topBorderRadius}px`,
          borderTopRightRadius:`${newData.topBorderRadius}px`,
          borderBottomLeftRadius:`${newData.bottomBorderRadius}px`,
          borderBottomRightRadius:`${newData.bottomBorderRadius}px`}">
			<!--          <view class="content" >
            <view class="value" :style="{color: newData.fontColor}">{{imgShareData.shareNum}}</view>
            <view :style="{color: newData.iconColor}" class="cuIcon-appreciate"><p style="display: inline-block" :style="{color: newData.fontColor}">分享数</p></view>
          </view> -->
			<view class="content" :style="{borderLeftColor:newData.borderColor,borderRightColor:newData.borderColor}"
				style="border-right: 1px solid;">
				<view class="value" :style="{color: newData.fontColor}">{{imgShareData.voteNum?imgShareData.voteNum:0}}
				</view>
				<view :style="{color: newData.iconColor}" class="cuIcon-likefill">
					<p style="display: inline-block" :style="{color: newData.fontColor}">祝福数</p>
				</view>
			</view>
			<view class="content">
				<view class="value" :style="{color: newData.fontColor}">{{imgShareData.visitNum}}</view>
				<view :style="{color: newData.iconColor}" class="cuIcon-hotfill">
					<p style="display: inline-block" :style="{color: newData.fontColor}">人气数值</p>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value
			};
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
			imgShareData: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		computed: {},
		created() {},
		mounted() {},
		methods: {},
	};
</script>
<style>
	.popularityComponent {}

	.content {
		width: 100%;
		justify-content: center;
		align-items: center;
		text-align: center;
		padding: 15px;
	}

	.value {
		padding-bottom: 8px;
		font-size: 24px;
		font-weight: bolder;
	}
</style>
