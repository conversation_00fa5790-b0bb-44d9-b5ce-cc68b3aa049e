<template>
	<!-- 轮播图组件 -->
	<!-- <view :class="'bg-'+theme.backgroundColor"> -->
	<view class="bg-white">
		<view v-show="newData.swiperType=='card-swiper'" :class="'bg-'+theme.backgroundColor"
			:style="{width: `${newData.width}%`}"></view>
		<view :style="{
				padding: `${newData.swiperType=='card-swiper'?'0 14px 14px 14px':''}`, 
				marginTop: `${newData.swiperType=='card-swiper'? '-'+(newData.height/2)+'px':''}`}">
			<swiper class="screen-swiper" :class="newData.dotStyle=='square-dot' ? 'square-dot' : 'round-dot'"
				:indicator-dots="true" :circular="true" :autoplay="true"	 :interval="newData.interval" duration="500"
				@change="cardSwiper" indicator-color="#cccccc" indicator-active-color="#ffffff"
				:style="{width: `${newData.width}%`,height: `${swiperHeight}px`}">
				<swiper-item v-for="(item,index) in newData.swiperList" :key="index" :class="cardCur==index?'cur':''"
					@tap="jumpPage(item)" >
						<image :id="'content-wrap' + index" mode="widthFix" :src="item.imageUrl"
							:style="{width: `${newData.width}%`, borderRadius: `${newData.borderRadius==1?'6':'0'}px`}">
						</image>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
		pageUrls
	} from '../div-base/div-page-urls.js'
	export default {
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						swiperType: 'screen-swiper',
						height: 150,

						interval: 3000,
						borderRadius: 0,
						imageSpacing: 0,
						swiperList: []
					}
				}
			}
		},
		components: {},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				cardCur: 0,
				swiperHeight: 0,
				pageUrls: pageUrls
			};
		},
		created() {
			//动态设置swiper的高度
			this.$nextTick(() => {
				setTimeout(()=>{
					this.getContentHeight();
				},80)
			});
		},
		methods: {
			cardSwiper(e) {
				this.cardCur = e.detail.current
				this.getContentHeight()
			},
			jumpPage(item) {
				if(!item.pageUrl){
					return
				}
				if (item.isSystemUrl) {
					if (this.pageUrls.tabPages.indexOf(item.pageUrl) != -1) {
						uni.switchTab({
							url: item.pageUrl
						});
					} else {
						uni.navigateTo({
							url: item.pageUrl
						});
					}
				} else {
					window.location.href = item.pageUrl
				}
			},
			//拿取轮播内容高度 
			getContentHeight() {
				let element = "#content-wrap" + (this.cardCur);
				let query = uni.createSelectorQuery().in(this);
				query.select(element).boundingClientRect(res=>{
					this.swiperHeight = res.height
				}).exec();
			}
		}
	}
</script>

<style scoped lang="scss">
	.screen-swiper {
		// min-height: 90upx!important;
	}
</style>
