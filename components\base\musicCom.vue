<template>
	<!-- 背景音乐组件 -->
	<view>
		{{newData.url}}
		<button @click="createMusic()">asds </button>
	</view>
</template>

<script>
	const app = getApp();
	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
					}
				}
			}
		},
		mounted() {
			// this.createMusic()
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
			};
		},
		methods: {
			createMusic(){
				console.log("音乐",this.newData.url)
				let music =  uni.createInnerAudioContext();
				music.src = this.newData.url;
				music.play()
				
			}
		}
	}
</script>

<style scoped lang="scss">
	
</style>
