/* 首页全局共用样式 */
.wrapper {
	margin-top: -20rpx;
}

.margin-top-bar {
	background-color: #fff;
	margin-top: 190rpx;
}

.wrapper-list {
	white-space: nowrap;
	padding: 20rpx 30rpx;
	margin-top: -40upx;
}

.wrapper-list .item {
	display: inline-block;
	width: 260rpx;
	height: 380rpx;
	margin-top: 10px;
	margin-bottom: 10px;
	margin-left: 10rpx;
	margin-right: 10rpx;
	border: 1rpx solid #eee;
	background-color: #fff;
}

.wrapper-list .item:nth-last-child(1) {
	margin-right: 0;
}

.wrapper-list .item .img-box {
	width: 100%;
	height: 260rpx;

}

.wrapper-list .item .img-box image {
	width: 100%;
	height: 100%;
	border-radius: 5rpx 5rpx 0 0;
}

.top-home {
	top: unset !important;
	z-index: 10000;
}


/* 分类 */
.classification{
	margin-top:100rpx;
	min-height: 60rpx;
	box-shadow:unset !important;
}

.classification-width{
	width: 90%;
}

.img-category{
	width: 100rpx;
	height: 100rpx;
}

.img-category-banner{
	width: 100%;
	height: 180rpx;
}

.commodity{
	margin-top: 30rpx;
}

.more{
	margin-top: 85rpx;
}


/* 轮播 */
.banner-image{
	width: 96% !important;
	border-radius: 10rpx !important;
	position: absolute;
	left: 15rpx;
}

.banner{
	min-height: 100%;
	margin-top: -120rpx;
	width: 100%;
	height: 210rpx;
}

/* 店铺 */
.store-swiper {
	margin-top: -30rpx;
	height: 366rpx;
	padding-bottom: 64rpx;
}

.shop-selection{
	margin-left: 0rpx !important;
	color: #666666;
}

.shop-more{
	margin-right: 0rpx !important;
	color: #666666;
}

.shop-detail{
	margin-top: -30rpx !important;
}

.shop-image{
	width: 200rpx;
	height: 200rpx !important;
}

.shop-image image{
	height: 200rpx !important;
}

.shop-box{
	height: 200rpx !important;
	width: 200rpx !important;
	margin-right: 0rpx !important;
}

.shop-information{
	position: absolute;
	top: 140rpx;
	left: 50rpx;
}

.enter-bg{
	width: 100rpx;
	height: 40rpx;
	opacity: 0.3;
}

.shop-name{
	position: absolute;
	top: 0;
	line-height: 200rpx;
	padding: 0 10rpx 0 5rpx;
	width: 200rpx;
}

.enter-shop{
	position: absolute;
	left:20rpx;
}

/* 热销/新品/猜你喜欢 */
.goods-card{
	border: none !important;
}

.goods-name{
	padding: 0 10rpx !important;
	color: #333333 !important;
}

.hot-item{
	margin-top: -220rpx;
}

.hot-more{
	margin-top: -220rpx;
}

.hot-product{
	margin-top: -250rpx;
}

.bg-image{
	height: 300rpx;
}
page{
	height: 100%;
}
.index_page{
	height: auto;
	background-attachment:local;
	background-repeat: no-repeat;
	background-size: 100%;
}