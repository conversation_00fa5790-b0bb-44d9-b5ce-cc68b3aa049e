<!--
  - 微信快速授权登录页面
  - 符合最新微信小程序登录规范
  - 兼容小程序环境
-->
<template>
	<view class="auth-container">
		<view class="auth-content">
			<!-- 微信图标和标题 -->
			<view class="auth-header">
				<view class="wechat-icon-text">微信</view>
				<text class="auth-title">微信快速登录</text>
				<text class="auth-desc">使用微信账号快速登录，享受更好的服务体验</text>
			</view>

			<!-- 授权说明 -->
			<view class="auth-info">
				<text class="info-text">登录即表示您已阅读并同意</text>
			</view>

			<!-- 用户协议和隐私政策 -->
			<view class="agreement-section">
				<view class="checkbox-container">
					<checkbox-group @change="onAgreementChange">
						<label class="checkbox-label">
							<checkbox :checked="isAgreed" color="#07c160" />
							<text class="agreement-text">
								我已阅读并同意
								<text class="link-text" @click="openUserAgreement">《用户协议》</text>
								和
								<text class="link-text" @click="openPrivacyPolicy">《隐私政策》</text>
							</text>
						</label>
					</checkbox-group>
				</view>
			</view>

			<!-- 授权按钮 -->
			<view class="auth-button-container">
				<button class="auth-button" :class="{ 'disabled': !isAgreed || isLoading }"
					:disabled="!isAgreed || isLoading" @click="showLoginConfirmDialog">
					<text v-if="isLoading">授权中...</text>
					<text v-else>微信授权登录</text>
				</button>
			</view>

			<!-- 取消登录按钮 -->
			<view class="cancel-button-container">
				<button class="cancel-button" @click="cancelLogin">
					<text>取消登录</text>
				</button>
			</view>

		</view>
	</view>
</template>

<script>
const app = getApp();
const util = require("utils/util.js");
import api from 'utils/api';

export default {
	data() {
		return {
			isAgreed: false, // 是否同意协议
			isLoading: false, // 是否正在加载
			reUrl: '', // 登录成功后跳转的页面
			showConfirmDialog: false // 是否显示确认登录对话框
		};
	},

	onLoad(options) {
		// 保存别人分享来的 userCode
		util.saveSharerUserCode(options);
		this.reUrl = options.reUrl || '';
	},

	methods: {
		// 协议勾选状态改变
		onAgreementChange(e) {
			this.isAgreed = e.detail.value.length > 0;
		},

		// 显示登录确认对话框
		showLoginConfirmDialog() {
			if (!this.isAgreed) {
				uni.showToast({
					title: '请先同意用户协议和隐私政策',
					icon: 'none'
				});
				return;
			}

			if (this.isLoading) return;

			// 显示确认对话框
			uni.showModal({
				title: '确认登录',
				content: '是否同意使用微信账号登录？',
				confirmText: '同意',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						// 用户点击同意，发起授权
						this.handleWechatAuth();
					}
					// 用户点击取消，不做任何操作
				}
			});
		},

		// 取消登录，跳转到首页
		cancelLogin() {
			uni.reLaunch({
				url: '/pages/groupon/index'
			});
		},

		// 打开用户协议
		openUserAgreement() {
			uni.navigateTo({
				url: '/pages/login/user-agreement'
			});
		},

		// 打开隐私政策 - 优先使用微信官方隐私协议页面
		openPrivacyPolicy() {
			// #ifdef MP-WEIXIN
			// 微信小程序环境：使用官方隐私协议API
			wx.openPrivacyContract({
				success: () => {
					console.log('微信官方隐私协议页面打开成功');
				},
				fail: (err) => {
					console.error('微信官方隐私协议页面打开失败:', err);
					// 降级方案：跳转到自定义隐私政策页面
					uni.showToast({
						title: '正在为您跳转到隐私政策页面',
						icon: 'none',
						duration: 1500
					});
					setTimeout(() => {
						uni.navigateTo({
							url: '/pages/login/privacy-policy'
						});
					}, 1500);
				}
			});
			// #endif

			// #ifndef MP-WEIXIN
			// 非微信小程序环境：使用自定义隐私政策页面
			uni.navigateTo({
				url: '/pages/login/privacy-policy'
			});
			// #endif
		},

		// 微信授权登录
		async handleWechatAuth() {
			if (!this.isAgreed) {
				uni.showToast({
					title: '请先同意用户协议和隐私政策',
					icon: 'none'
				});
				return;
			}

			if (this.isLoading) return;

			this.isLoading = true;

			try {
				// 使用uni.login获取微信授权
				const loginRes = await this.getWechatLogin();
				console.log('微信登录结果:', loginRes);

				// 调用后端登录接口
				await this.callLoginApi(loginRes.code);

				// 登录成功处理
				this.handleLoginSuccess();

			} catch (error) {
				console.error('微信授权登录失败:', error);
				uni.showToast({
					title: error.message || '登录失败，请重试',
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},

		// 获取微信登录授权
		getWechatLogin() {
			return new Promise((resolve, reject) => {
				uni.login({
					provider: 'weixin',
					success: (res) => {
						if (res.code) {
							resolve(res);
						} else {
							reject(new Error('获取微信授权失败'));
						}
					},
					fail: (err) => {
						console.error('uni.login失败:', err);
						reject(new Error('微信授权失败'));
					}
				});
			});
		},

		// 调用后端登录API
		async callLoginApi(code) {
			try {
				let loginData = { jsCode: code };

				// 根据平台调用不同的登录接口
				let loginResult;
				// #ifdef MP-WEIXIN
				// 小程序登录
				loginResult = await api.loginWxMa({ jsCode: code });
				// #endif

				// #ifdef H5
				// H5微信登录
				if (util.isWeiXinBrowser()) {
					loginResult = await api.loginWxMp({ code });
				} else {
					throw new Error('请在微信中打开');
				}
				// #endif

				if (loginResult && loginResult.code === 0) {

					let userInfo = loginResult.data;
					uni.setStorageSync('third_session', userInfo.thirdSession);
					uni.setStorageSync('user_info', userInfo);
					// 获取购物车数量
					// that.shoppingCartCount();
					// resolve("微信小程序登录success");
					// 保存登录信息
					// if (loginResult.data && loginResult.data.thirdSession) {
					// 	uni.setStorageSync('third_session', loginResult.data.thirdSession);
					// }
					return loginResult;
				} else {
					throw new Error(loginResult.msg || '登录失败');
				}
			} catch (error) {
				console.error('调用登录API失败:', error);
				throw error;
			}
		},

		// 登录成功处理
		handleLoginSuccess() {
			uni.showToast({
				title: '登录成功',
				icon: 'success'
			});

			setTimeout(() => {
				if (this.reUrl) {
					// 有重定向地址，跳转到指定页面
					uni.reLaunch({
						url: decodeURIComponent(this.reUrl)
					});
				} else {
					// 没有重定向地址，返回上一页或首页
					const pages = getCurrentPages();
					if (pages.length > 1) {
						uni.navigateBack();
					} else {
						uni.reLaunch({
							url: '/pages/tabbar/home/<USER>'
						});
					}
				}
			}, 1500);
		},

		// 跳转到其他登录方式
		goToOtherLogin() {
			uni.navigateTo({
				url: `/pages/login/index${this.reUrl ? '?reUrl=' + encodeURIComponent(this.reUrl) : ''}`
			});
		}
	}
};
</script>

<style scoped>
.auth-container {
	min-height: 100vh;
	background-color: #f8f8f8;
}

.auth-content {
	padding: 60rpx 40rpx;
}

.auth-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.wechat-icon-text {
	width: 120rpx;
	height: 120rpx;
	margin: 0 auto 30rpx;
	background-color: #07c160;
	color: white;
	border-radius: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 36rpx;
	font-weight: bold;
}

.auth-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

.auth-desc {
	display: block;
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.auth-info {
	text-align: center;
	margin-bottom: 40rpx;
}

.info-text {
	font-size: 28rpx;
	color: #666;
}

.agreement-section {
	margin-bottom: 60rpx;
}

.checkbox-container {
	display: flex;
	justify-content: center;
}

.checkbox-label {
	display: flex;
	align-items: flex-start;
	max-width: 600rpx;
}

.agreement-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
	margin-left: 20rpx;
}

.link-text {
	color: #07c160;
	text-decoration: underline;
}

.auth-button-container {
	margin-bottom: 40rpx;
}

.auth-button {
	width: 100%;
	height: 88rpx;
	background-color: #07c160;
	color: white;
	border-radius: 44rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.auth-button.disabled {
	background-color: #ccc;
	color: #999;
}

.cancel-button-container {
	margin-bottom: 40rpx;
}

.cancel-button {
	width: 100%;
	height: 88rpx;
	background-color: transparent;
	color: #666;
	border: 2rpx solid #ddd;
	border-radius: 44rpx;
	font-size: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.cancel-button:active {
	background-color: #f5f5f5;
}

.other-login {
	text-align: center;
}

.other-text {
	font-size: 28rpx;
	color: #07c160;
	text-decoration: underline;
}
</style>
