<template>
	<!-- 团主头像组件 -->
	<view v-if="showFlag">
		<!-- <button @click="clear()">清除</button> -->
		<view class="bg-white"
			:style="{marginBottom: `${newData.pageMarginBottom}px`,paddingTop: `${newData.pageMarginTop}px`}">
			<!-- <view>
				{{loginfo}}
			</view> -->
			<view class="cu-item padding-tb" v-show="grouponInfo.status=='1'&& newData.startShowFlag=='0'">
				<view class="content  text-center">
					<view class="cu-avatar round bg-img lg text-yellow groupon-user"
						:style="{'background-image':`${'url('+headimgUrl+')'}`,height: '80px',width: '80px'}">
						 <span class="cu-tag badge bg-yellow">团长</span>
					</view>
					<p v-if="nickName" class="captain-nickname">{{nickName}}</p>
				</view>
				<view class="captain_description" :style="{color: `${newData.startDescriptionColor}`}">
					<p>{{newData.startDescription}}</p>
				</view>
<!--				<view class="captain_button">
					<button v-if="!grouponInfo.joinFlag" @tap="clickButton"
						:style="{background: `${newData.startButtonBackGround}`,color: `${newData.startButtonColor}`,fontSize: `${newData.startButtonFont}px`}"
						style="width: 60%;">{{ newData.startButtonTitle }} </button>
				</view>-->
			</view>
			<view class="cu-item padding-tb" v-show="grouponInfo.status=='2' && newData.proceedShowFlag=='0'">
				<view class="content  text-center">
					<view class="cu-avatar round bg-img lg text-yellow groupon-user"
						:style="{'background-image':`${'url('+headimgUrl+')'}`,height: '80px',width: '80px'}">
						 <span class="cu-tag badge bg-yellow">团长</span>
					</view>
					<p v-if="nickName" class="captain-nickname">{{nickName}}</p>
				</view>
				<view class="captain_description" :style="{color: `${newData.proceedDescriptionColor}`}">
					<p>{{newData.proceedDescription}}</p>
				</view>
<!--				<view class="captain_button">
					<button v-if="!grouponInfo.joinFlag" @tap="clickButton"
						:style="{background: `${newData.proceedButtonBackGround}`,color: `${newData.proceedButtonColor}`,fontSize: `${newData.proceedButtonFont}px`}"
						style="width: 60%;">{{ newData.proceedButtonTitle }}</button>
					<button v-if="grouponInfo.joinFlag" @tap="sharePage" style="width: 60%;">{{ newData.proceedButtonTitle }}&lt;!&ndash; 您在拼团中,点击分享 &ndash;&gt;</button>
				</view>-->
			</view>
			<view class="cu-item padding-tb" v-show="grouponInfo.status=='3' && newData.endShowFlag=='0'">
				<view class="content  text-center">
					<view class="cu-avatar round bg-img lg text-yellow groupon-user"
						:style="{'background-image':`${'url('+headimgUrl+')'}`,height: '80px',width: '80px'}">
						 <span class="cu-tag badge bg-yellow">团长</span>
					</view>
					<p v-if="nickName" class="captain-nickname">{{nickName}}</p>
				</view>
				<view class="captain_description" :style="{color: `${newData.endDescriptionColor}`}">
					<p>{{newData.endDescription}}</p>
				</view>
<!--				<view class="captain_button">
					<button v-if="!grouponInfo.joinFlag" @tap="clickButton"
						:style="{background: `${newData.endButtonBackGround}`,color: `${newData.endButtonColor}`,fontSize: `${newData.endButtonFont}px`}"
						style="width: 60%;">{{ newData.endButtonTitle }}</button>
					<button v-if="grouponInfo.joinFlag" @tap="clickButton" style="width: 60%;">{{newData.endButtonTitle}}&lt;!&ndash; 恭喜您拼团成功！！ &ndash;&gt;</button>
				</view>-->
				
			</view>
		</view>


		<!-- 分享组件 -->
			<share-component v-model="showShare" :shareParams="shareParams" :shareFriendsCustom="false" :showSharePoster="false"></share-component>
		
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	import shareFriends from '@/components/share-friends/index.vue'
	const util = require("utils/util.js");
	import jweixin from '@/utils/jweixin';
	import shareComponent from "@/components/share-component/index"
	
	export default {
		components: {shareComponent},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					
						
					}
				}
			},
			grouponInfo: {
				type: Object,
				default: function() {
					return {}
				}
			},

		},
		watch: {
			grouponInfo(val, oldVal) {
				if (val != oldVal) {
					this.completeInfo()
				}
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				couponInfoList: [],
				headimgUrl: '', //用户头像
				nickName: '', //用户昵称
				tabActiveName: '1', //拼团激活状态
				showFlag: true, //团主控件是否显示
				phone: '' ,//电话号码
				shareParams: {},
				showShare: false,
				loginfo:JSON.stringify(this.value)
			};
		},
		mounted() {
			this.completeInfo()
		},
		methods: {
			clickButton() {
				//todo 这里需要加入分情况提示
				console.log("d点击了");
				if (this.value.enablePayment == '1') {
					this.$emit("joinGroupnAndPay");
				} else {
					// 显示modal提示
					uni.showModal({
						title: '提示',
						content: '无效的拼团活动，此拼团可能已过期或已关闭',
						showCancel: false
					});
				}
			},
			sharePage() {
				console.log("分享")
				// 分享海报需要配置的参数
				let desc = '长按识别小程序码';
				let qrCode = ""; //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
				let shareImg = 'https://wwx-dev-gongjunqi.oss-cn-guangzhou.aliyuncs.com/1/material/fe2179e6-4d03-4435-b706-bda253cda4e1.jpg'; // 海报图片
				
				// #ifdef H5 || APP-PLUS
					desc = '长按识别二维码';
					// h5的海报分享的图片有的有跨域问题，所以统一转成base64的
					// 之所以不在组件里面转换是因为无法区分哪张image图片需要处理，一般处理主图
					shareImg = util.imgUrlToBase64(shareImg);
				// #endif
				 //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
				let posterConfig = {
					width: 750,
					height: 1280,
					backgroundColor: '#fff',
					debug: false,
					blocks: [
						{
							width: 690,
							height: 808,
							x: 30,
							y: 183,
							borderWidth: 2,
							borderColor: '#f0c2a0',
							borderRadius: 20
						}, {
							width: 634,
							height: 74,
							x: 59,
							y: 770,
							backgroundColor: '#fff',
							opacity: 0.5,
							zIndex: 100
						},
					],
					texts: [
						{
							x: 30,
							y: 113,
							baseLine: 'top',
							text: '发现一个好物，推荐给你呀',
							fontSize: 38,
							color: '#080808'
						}, {
							x: 92,
							y: 810,
							fontSize: 38,
							baseLine: 'middle',
							text: '名称',
							width: 570,
							lineNum: 1,
							color: '#080808',
							zIndex: 200
						}, {
							x: 59,
							y: 895,
							baseLine: 'middle',
							text: [{
								text: '只需',
								fontSize: 28,
								color: '#ec1731'
								}, {
									text: '¥价格',
									fontSize: 36,
									color: '#ec1731',
									marginLeft: 30
								},
							]
						}, {
							x: 522,
							y: 895,
							baseLine: 'middle',
							text: '已售数量' ,
							fontSize: 28,
							color: '#929292'
						}, {
							x: 59,
							y: 945,
							baseLine: 'middle',
							text: [{
								text: "大大大",
								fontSize: 28,
								color: '#929292',
								width: 570,
								lineNum: 1
							}]
						}, {
							x: 360,
							y: 1065,
							baseLine: 'top',
							text: desc,
							fontSize: 38,
							color: '#080808'
						}, {
							x: 360,
							y: 1123,
							baseLine: 'top',
							text: '超值好货快来购买',
							fontSize: 28,
							color: '#929292'
						},
					],
					images: [
						{
							width: 634,
							height: 634,
							x: 59,
							y: 210,
							url: shareImg
						}, {
							width: 220,
							height: 220,
							x: 92,
							y: 1020,
							url: null,
							qrCodeName: 'qrCodeName',// 二维码唯一区分标识
						},
					]
				};
				let userInfo = uni.getStorageSync('user_info');
				if (userInfo && userInfo.headimgUrl) {
					//如果有头像则显示
					posterConfig.images.push({
						width: 62,
						height: 62,
						x: 30,
						y: 30,
						borderRadius: 62,
						url: shareImg
					});
					posterConfig.texts.push({
						x: 113,
						y: 61,
						baseLine: 'middle',
						text: "明子",
						fontSize: 32,
						color: '#8d8d8d'
					});
				}
				this.shareParams = {
					title: '发现一个好物，推荐给你呀',
					desc: "商品米",
					imgUrl: shareImg,
					scene: '1211212' ,
					page: 'pages/goods/goods-detail/index',
					posterConfig: posterConfig
				}
				this.showShare =true;
			},
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},
			//完善组件信息
			completeInfo() {
        console.log(this.value)
				console.log("完善组件信息", this.grouponInfo)
				//头像显示逻辑的判断
				this.isShow()
				let list = this.grouponInfo.groupUserList;
				if (!list) {
					return;
				}
				if (list.length > 0) {
					for (let i = 0; i < list.length; i++) {
						if (list[i].isLeader == "1") {
							this.headimgUrl = list[i].headimgUrl;
              this.nickName = list[i].nickName;
							break;
						}
					}
				}
			},
			isShow() {
				if (this.tabActiveName === "1") {
					if (this.newData.startShowFlag === "0") {
						this.showFlag = true;
					} else {
						this.showFlag = false;
					}
				} else if (this.tabActiveName === "2") {
					if (this.newData.proceedShowFlag === "0") {
						this.showFlag = true;
					} else {
						this.showFlag = false;
					}
				} else if (this.tabActiveName === "3") {
					if (this.newData.endShowFlag === "0") {
						this.showFlag = true;
					} else {
						this.showFlag = false;
					}
				}
			},
			clear(){
				app.clearUser();
			}
		}
	}
</script>

<style scoped lang="scss">
	.captain_description {
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
	}

	.captain_button {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.captain-nickname {
		margin-top: 8px;
		font-size: 14px;
		color: #666;
		text-align: center;
	}
</style>
