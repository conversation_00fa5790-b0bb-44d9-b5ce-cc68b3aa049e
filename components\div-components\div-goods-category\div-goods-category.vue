<template>
	<!-- 商品分类组件 -->
	<view>
		<view class="cu-bar" :style="{backgroundColor: newData.background}" 
			:class="newData.background&&newData.background.indexOf('bg-')!=-1?newData.background:''"
			style="min-height: 60rpx;">
			<view class="" style="width: 90%;">
				<scroll-view scroll-x class="nav text-white text-sm" scroll-with-animation :scroll-left="scrollLeft">
					<view class="cu-item" :class="index==TabCur ? 'cur text-bold text-white text-lg' : ''" v-for="(item,index) in firstCategoryData"
					:key="index" @tap="tabSelect" :data-index="index">
						{{item.name}}
					</view>
				</scroll-view>
			</view>
			<view class="action">
				<navigator url="/pages/goods/goods-category/index" open-type="switchTab" hover-class="none" class="cuIcon-moreandroid text-white"></navigator>
			</view>
		</view>
		
	</view>
</template>

<script>
	
	const app = getApp();
	import api from '@/utils/api'
	
    export default {
		components: {
		},
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        background: ``,
	                }
	            }
            }
	    },
		mounted(){
			//
			this.goodsCategoryPage({
				searchCount: false,
				current: 1,
				size: 100,
				ascs: 'sort',
				parentId: '0',
				enable: '1'
			});
		},
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
				secondCategoryData: [],
				TabCur: 0,
				scrollLeft: 0,
				firstCategoryData: [{
					id: '-1',
					name: '全部'
				}],
				page2: {
					searchCount: false,
					current: 1,
					size: 10
				},
				loadmore2: true,
				goodsList2: []
			};
		},
		methods: {
			//商品分类
			goodsCategoryPage(data) {
				api.getGoodsTagType().then(res => {
					console.log("res",res)
					this.firstCategoryData = [...this.firstCategoryData, ...res.data];
					this.getGoodsCoverByType(this.firstCategoryData[0].id)
				});
				
				
				// api.goodsCategoryPage(data).then(res => {
				// 	this.firstCategoryData = [...this.firstCategoryData, ...res.data.records];
				// });
			},
			getGoodsCoverByType(id){
				let params = {id:id}
				//根据类别加载
				api.goodsCoverByType(Object.assign({
					total:10,
					current: 1,
					size: 10,
					descs: '',
					ascs: '',
				}, params)).then(res => {
					console.log("结果",res)
				});
			},
			
			tabSelect(e) {
				console.log("点击导航",e)
					this.getGoodsCoverByType(this.firstCategoryData[0].id)
				let TabCur = e.currentTarget.dataset.index;
				this.TabCur = e.currentTarget.dataset.index
				uni.setStorage({
					key: 'param-goods-category-index',
					data: TabCur - 1
				});
				if(TabCur>0){
					uni.switchTab({
						url:'/pages/goods/goods-category/index',
					})
				}
			},
		}
    }
</script>

<style scoped lang="scss">

.img-category{
	width: 100rpx;
	height: 100rpx;
}

.img-category-banner{
	width: 100%;
	height: 180rpx;
}
</style>
