<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="goods-container flex">
		<view class="goods-box" v-for="(item, index) in goodsList" :key="index">
			<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.id">
				<view class="img-box">
					<image :src="item.picUrls[0] ? item.picUrls[0] : '/static/public/img/no_pic.png'"></image>
				</view>
				<view class="text-black margin-top-xs padding-lr-sm overflow-2">{{item.name}}</view>
				<view class="flex justify-between margin-top-sm align-center">
					<view class="text-price text-bold text-red text-lg padding-lr-sm">{{item.priceDown}}</view>
					<view class="cu-tag bg-red radius sm" v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮</view>
					<view class="text-gray text-sm padding-lr-sm">已售{{item.saleNum}}</view>
				</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {};
		},

		components: {},
		props: {
			goodsList: {
				type: Array,
				default: () => []
			}
		},
		methods: {}
	};
</script>
<style>
	.goods-container {
		justify-content: space-between;
		flex-wrap: wrap;
		box-sizing: content-box;
		padding: 20rpx;
	}

	.goods-box {
		width: 349rpx;
		height: 530rpx;
		background-color: #fff;
		overflow: hidden;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
		box-shadow:0px 10px 10px #e5e5e5;
	}

	.goods-box .img-box {
		width: 100%;
		height: 349rpx;
		overflow: hidden;
	}

	.goods-box .img-box image {
		width: 100%;
		height: 349rpx;
	}
</style>
