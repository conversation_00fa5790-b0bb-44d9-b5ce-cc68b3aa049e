<template>
  <view class="container">
    <text class="title">我的</text>

    <button @click="handleOrderList">dindan</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    };
  },
  onLoad() {
    console.log('我的页面加载完成');
  },
  methods: {
    handleOrderList(){
      uni.navigateTo({
        url: "/pages/order/order-list/index?tenant_id=1468808703988994048",
      })
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  
  .title {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
    margin-top: 100px;
  }
}
</style> 