<template>
	
	<view class="bg-white">
		<image class="" :src="bindPageData.imgUrl" mode="widthFix" style="width: 100%;"></image>
		<!-- <rich-text :nodes="bindPageData.content"></rich-text> -->
		<view v-show="contentShowFlag" class="ql-editor">
			<jyf-parser :html="bindPageData.content"></jyf-parser>
		</view>
		<!-- <view style="height: 1px;border-bottom: solid #000000;"></view> -->
		<view v-if="bindPageData.status =='0'">
			<view v-show="contentShowFlag" class="flex-sub padding-sm shadow-blur radius text-center t2-l">
				<button class="cu-btn  shadow-blur lg btn-confirm" :style="{backgroundColor: bindPageData.theme}"
					:class="bindPageData.theme&&bindPageData.theme.indexOf('bg-') != -1 ? bindPageData.theme : ''"
					style="margin-right: 50rpx;" @tap="cancle">取消</button>
				<button class="cu-btn  shadow-blur lg btn-confirm" :disabled="buttonDisabled"
					:style="{backgroundColor: bindPageData.theme}"
					:class="bindPageData.theme&&bindPageData.theme.indexOf('bg-') != -1 ? bindPageData.theme : ''"
					@tap="confirmContent">{{buttonContent}}{{time==0?'':'('+time+'秒)'}}</button>
			</view>
			<view v-show="inputFlag">
			<!-- <view v-show="true"> -->
			<view class="flex justify-center align-center padding-sm">
				{{bindPageData.remind}}
			</view>
			<view class="flex justify-start align-start solid  padding-sm">
				<text class="margin-xs  cuIcon-phone"></text>
				<input class="flex-twice" v-model="phone" type="number" placeholder="请输入订单手机号码">
			</view>
			<view class="flex justify-start align-start solid  padding-sm">
				<text class="margin-xs cuIcon-safe"></text>
				<input class="flex-twice" v-model="code" type="text" placeholder="请输入验证码">
				<button type="default" class="cu-btn bg-gray" :disabled="msgKey" :class="'display:' + msgKey"
					size="mini" @click="getPhoneCode">{{msgText}}</button>
			</view>
			<view class="flex-sub padding-sm shadow-blur radius text-center t2-l">
				<button class="cu-btn  shadow-blur lg btn-confirm" :style="{backgroundColor: bindPageData.theme}"
					:class="bindPageData.theme&&bindPageData.theme.indexOf('bg-') != -1 ? bindPageData.theme : ''"
					@click="submit">确认</button>
			</view>
		</view>
		</view>
		<view  v-if="bindPageData.status =='1'" class="flex justify-center align-center   padding-sm ">
			<text class=" text-red text-bold">您已登记成功，请等待成片发布！！！</text>
			<button class=" cu-btn  shadow-blur lg btn-confirm" :style="{backgroundColor: bindPageData.theme}"
				:class="bindPageData.theme&&bindPageData.theme.indexOf('bg-') != -1 ? bindPageData.theme : ''"
				@click="jumpUrl">点击查看</button>
		</view>
		<view  v-if="bindPageData.status =='2'" class="flex justify-center align-center   padding-sm ">
			<text class=" text-red text-bold">您的客片已出炉～请点击查看</text>
			<view class="block">
				<button class=" cu-btn  shadow-blur lg btn-confirm" :style="{backgroundColor: bindPageData.theme}"
					:class="bindPageData.theme&&bindPageData.theme.indexOf('bg-') != -1 ? bindPageData.theme : ''"
					@click="jumpUrl">点击查看</button>
			</view>
		</view>
	</view>

</template>

<script>
	const app = getApp();
	import validate from 'utils/validate'
	import api from '@/utils/api'
	import __config from "@/config/env";
	const util = require("utils/util.js");
	const MSGINIT = "发送验证码",
		MSGSCUCCESS = "${time}秒后可重发",
		MSGTIME = 60;
	export default {
		components: {},
		data() {
			return {
				msgKey: false,
				msgText: MSGINIT,
				msgTime: MSGTIME,
				code: '',
				// theme: app.globalData.theme, //全局颜色变量
				bindPageData: {}, //绑定页面数据
				buttonContent: '请阅读并同意',
				time: 5,
				phone: null,
				buttonDisabled: true,
				inputFlag: false,//输入号码控制
				contentShowFlag: true,// 内容显示
				showDefaultAppPage: false,
				loadmore: true,
				firstCategoryData: [{
					id: '0',
					name: '首页'
				}],
				secondCategoryData: [],
				page2: {
					searchCount: false,
					current: 1,
					size: 10
				},
				loadmore2: true,
				goodsList2: [],
				loadmore3: true, // 自定义页面的加载状态
			};
		},
		props: {},
		onLoad(options) {
			app.initPage().then(res => {
				this.loadData();
				this.loadTime();
			});
		},
		onShow() {
			//更新购物车tabar角标数量
			// uni.setTabBarBadge({
			// 	index: 3,
			// 	text: app.globalData.shoppingCartCount + ''
			// });
		},
		onShareAppMessage: function() {
			let title = 'JooLun商城源码-小程序演示';
			let path = 'pages/home/<USER>'
			// let path = util.getCurPage(getCurrentPages());

			const userInfo = uni.getStorageSync('user_info')
			const userCode = userInfo ? '?sharer_user_code=' + userInfo.userCode : ''
			path = path + userCode;
			return {
				title: title,
				path: path,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log(res.errMsg);
					}
				},
				fail: function(res) { // 转发失败
				}
			};
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		onReachBottom() {
			if (this.showDefaultAppPage) {
				if (this.TabCur === 0 && this.loadmore) {
					this.page.current = this.page.current + 1;
					this.goodsPage();
				}
				if (this.TabCur == !0 && this.loadmore2) {
					this.page2.current = this.page2.current + 1;
					this.goodsPageByCateGory();
				}
			}
		},

		methods: {
			getUserProfile() {
				// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
				// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
				console.log("打算桑达大厦");
				wx.getUserProfile({
					desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (detail) => {
						console.log("success", detail);

					},
					fail(err) {
						console.log("success", err);
					}
				})
			},
			loadData() {
				// api.jokelLoginWxMp().then(res => {
				// 	let userInfo = res.data;
				// 	//同步执行
				// 	uni.setStorageSync('third_session', userInfo.thirdSession);
				// 	uni.setStorageSync('user_info', userInfo);
				
				// 	console.log("joke----公众号登录成功 ", res)
				// }).catch(res => {
				// 	console.log("joke----公众号登录失败 ", res)
				// });
				let tenantId = util.getUrlParam(location.href, "tenant_id");
				let appid = util.getUrlParam(location.href, "app_id");
				let bindId = util.getUrlParam(location.href, "bind_id");
				this.loadmore3 = true;
				let params = {
					id: bindId,
					appid: appid,
					tenantid: tenantId,
				}
				console.log("绑定页面的参数", bindId)
				api.imgShareBindPage(bindId).then(res => {
					this.bindPageData = res.data;
					
					console.log("绑定页面的结果", this.bindPageData)
					this.initShareWx()
				}).catch(err => {
					console.log(err)

				});
			},
			loadTime() {
				console.log("定时器加载")
				this.$nextTick(() => {
					// TODO 定时器保证动画完全执行，目前有些问题，后面会取消定时器
					let inter = setInterval(() => {
						--this.time;
						if (this.time == 0) {
							clearInterval(inter);
							this.buttonDisabled = false;
						}
					}, 1000)
				})
			},
			cancle() {
				window.close();
			},
			getPhoneCode() {
				if (this.msgKey) return
				if (!validate.validateMobile(this.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.msgKey = true
				api.getPhoneCode({
					type: '2',
					phone: this.phone,
					signName: this.bindPageData.signName,
					templateCode: this.bindPageData.templateCode,
					tenantId: util.getUrlParam(location.href, "tenant_id")
				}).then(res => {
					this.msgKey = false
					if (res.code == '0') {
						uni.showToast({
							title: '验证码发送成功',
							icon: 'none',
							duration: 3000
						});
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						this.msgKey = true
						const time = setInterval(() => {
							this.msgTime--
							this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
							if (this.msgTime == 0) {
								this.msgTime = MSGTIME
								this.msgText = MSGINIT
								this.msgKey = false
								clearInterval(time)
							}
						}, 1000)
					} else {

					}
				}).catch(() => {
					this.msgKey = false
				});
			},
			submit() {
				console.log("提交参数", {
					bindId: util.getUrlParam(location.href, "bind_id"),
					phone: this.phone,
					userRules: this.bindPageData.userRules,
					phoneCode: this.code
				})
				if (!validate.validateMobile(this.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if(!this.code || this.code.length != 4){
					uni.showToast({
						title: '请输入正确的验证码',
						icon: 'none',
						duration: 3000
					});
					return
				}

				api.becomeSignUser({
					bindId: util.getUrlParam(location.href, "bind_id"),
					phone: this.phone,
					userRules: this.bindPageData.userRules,
					phoneCode: this.code
				}).then(res => {
					let userInfo = res.data;
					if (res.ok) {
						uni.showToast({
							title: '绑定成功',
							icon: 'success',
							duration: 2000
						});
						this.loadData()
					}
				}).catch(res => {
					console.log("submit失败 ", res)
				});
			},
			confirmContent(){
				this.contentShowFlag = false;
				this.inputFlag = true;
			},
			//页面跳转
			jumpUrl() {
				let query = this.$Route.query;
				let url = "/pages/img-share/index?"
				if(query.tenant_id){
					url  = url +"tenant_id=" +query.tenant_id
				}
				if(query.app_id){
					url  = url +"&app_id=" +query.app_id
				}
				if(query.component_appid){
					url  = url +"&component_appid=" +query.component_appid
				}
				url = url +"&page_id="+this.bindPageData.pageId+"&data_id="+this.bindPageData.dateId
				uni.navigateTo({
					url: url
				});
			},
			refresh() {
				this.loadmore = true;
				this.loadData();
			},
			//初始化分享
			initShareWx() {
				uni.setNavigationBarTitle({
					title: this.bindPageData.pageBase.pageTitle
				});
				// h5页面加载时 会默认初始化分享
				// #ifdef H5
				if (util.isWeiXinBrowser()) {
					let shareObj = {
						title: this.bindPageData.pageBase.shareTitle ? this.bindPageData.pageBase.shareTitle : '',
						desc: this.bindPageData.pageBase.describe ? this.bindPageData.pageBase.describe : '',
						imgUrl: this.bindPageData.pageBase.shareImgUrl ? this.bindPageData.pageBase.shareImgUrl : ''
					};
					// console.log("来分享的参数",shareObj)
					let url = util.setH5ShareUrl();
					// //重新设置新的url
					let query = this.$Route.query;
					delete query.code;
					delete query.state;
					query.sharer_friend_id = util.getUrlParam(url, "sharer_friend_id");
					util.resetPageUrl(query);
					// console.log("重选设置url",query)
					api.getJsSdkConfig({
						url: location.href
					}).then(res => {
						// history.replaceState(history.state, null, url);
						let wxConfig = res.data;
						let shareObjTemp = {
							title: shareObj.title ? shareObj.title : '',
							desc: shareObj.desc ? shareObj.desc : '',
							link: wxConfig.url,
							imgUrl: shareObj.imgUrl ? shareObj.imgUrl : '',
						};
						jweixin.shareWxFriend(wxConfig, shareObjTemp, function() {
							// that.showModal = true;
							// uni.hideLoading();
							// console.log("初始化微信分享成功", shareObjTemp)
						}, function(e) {
							// console.log("初始化微信分享成功error",e)
						}, );
					}).catch(res => {
						console.log('调用getJsSdkConfig失败：' + res)
					});
				}
				// #endif
			},
			
		}
	};
</script>
<style>
</style>
