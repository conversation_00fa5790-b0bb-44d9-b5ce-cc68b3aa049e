<template>
	<view class="flex justify-center">
	<view class="dropDownSelectComponent">
		<view
			:style="{color: `${newData.titleColor}`, fontSize: `${newData.titleSize}px`,fontWeight:`${newData.titleWeight?'bold':'normal'}`}">
			{{newData.title}}<i v-show="newData.required" style="color: #FF0000">*</i>
		</view>
		<view
			class="margin-tb-xs"
			:style="{color: `${newData.describeColor}`, fontSize: `${newData.describeSize}px`,fontWeight:`${newData.describeWeight?'bold':'normal'}`}">
			{{newData.describe}}
		</view>
		<view class="form_comp_view flex justify-start align-start solid">
			<picker class="form_comp_view_input margin-sm" :range="newData.optionList"  @change="pickerChange">
				<input class="form_comp_view_input "  v-model="selected" type="text" placeholder="点击进行选择" disabled>
			</picker>
		</view>

	</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				selected:'',
			};
		},

		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		methods: {
			pickerChange(e) {
				console.log(e.target.value,"this.selected")
				let index =e.target.value;
				this.selected = this.newData.optionList[index] ;
			
			},
			checkValue(){
				console.log("检验值了dropDownSelectComponent",this.selected)
				if(this.newData.required && !this.selected){
					uni.showToast( {
						title: "请完善"+this.newData.title+"的内容",
						icon: 'none',
						duration: 2000
					});
					return false;
				}
				return {value:this.selected};
			}
		}
	};
</script>
<style>
	.dropDownSelectComponent {
		padding: 5px 15px;
		width: 90%;
		border-bottom:1px dashed  rgba(0,0,0,.2);
	}
	.form_comp_view{
		min-height: 20px;
		justify-content: center;
		align-items: center;
	}
	.form_comp_view_icon{
		font-size: 18px;
	}
	.form_comp_view_input{
		min-height: 20px;
		width: 100%;
	}
</style>
