<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="cu-card article no-card" >
		<view class="cu-item goods-item solid-bottom" v-for="(item, index) in goodsList" :key="index">
			<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.id">
				<view class="content">
					<image :src="item.picUrls[0] ? item.picUrls[0] : '/static/public/img/no_pic.png'" mode="aspectFill" class="row-img"></image>
					<view class="desc block padding-top-xs">
						<view class="text-black margin-top-sm overflow-2">{{item.name}}</view>
						<view class="text-gray text-sm margin-top-sm overflow-1">{{item.sellPoint}}</view>
						<view class="flex margin-top-sm">
							<view class="cu-tag bg-red radius sm" v-if="item.freightTemplat&&item.freightTemplat.type == '2'">包邮</view>
							<view class="cu-tag bg-orange radius sm" v-if="item.pointsGiveSwitch == '1'">积分</view>
							<view class="text-gray text-sm padding-lr-sm">已售{{item.saleNum?item.saleNum:'0'}}</view>
						</view>
						<view class="flex justify-between margin-tb-sm">
							<view class="text-price text-bold text-lg text-red">{{item.priceDown?item.priceDown:'0'}}</view>
							<view class="round buy text-sm shadow bg-red" :class="'bg-'+theme.themeColor"><text>立即购买</text></view>
						</view>
					</view>
				</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	const app = getApp();

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
			};
		},

		components: {},
		props: {
			goodsList: {
				type: Array,
				default: () => []
			}
		},
		methods: {}
	};
</script>
<style>
	.goods-item{
		width: 94% !important;
		margin: auto !important;
		margin-top: 20rpx !important;
		padding-left: 10rpx;
	}

	.row-img {
		margin-top: 26rpx;
		margin-left: -14rpx;
		width: 240rpx !important;
		height: 240rpx !important;
		border-radius: 10rpx
	}

	.buy{
		padding: 10rpx 20rpx 10rpx 20rpx;
	}
</style>
