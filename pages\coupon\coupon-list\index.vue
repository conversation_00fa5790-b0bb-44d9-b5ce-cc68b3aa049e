<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">领取优惠券</block>
		</cu-custom>
		<view class="cu-list bg-white padding-top-xs padding-bottom-sm">
			<view class="cu-item padding-tb-xs padding-lr margin-top-sm" v-for="(item, index) in couponInfoList" :key="index">
				<coupon-info :couponInfo="item" @receiveCoupon="receiveCouponChange($event,index)"></coupon-info>
			</view>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import couponInfo from "components/coupon-info/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: 'sort',
					//升序字段
					descs: ''
				},
				parameter: {},
				loadmore: true,
				couponInfoList: [],
				shopId: null
			};
		},

		components: {
			couponInfo
		},
		props: {},

		onShow() {},

		onLoad: function(options) {
			if(options.shopId){
				this.shopId = options.shopId;
			}
			app.initPage().then(res => {
				this.couponInfoPage();
			});
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.couponInfoPage();
			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框

			uni.hideNavigationBarLoading(); // 停止下拉动作

			uni.stopPullDownRefresh();
		},

		methods: {
			receiveCouponChange(item, index) { //更新单条数据
				this.couponInfoList[index] = item;
				this.couponInfoList.splice(); //确保页面刷新成功
			},
			couponInfoPage() {
				if(this.shopId){
					this.page.shopId = this.shopId
				}
				api.couponInfoPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let couponInfoList = res.data.records;
					this.couponInfoList = [...this.couponInfoList, ...couponInfoList];
					if (couponInfoList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			refresh() {
				this.loadmore = true;
				this.couponInfoList = [];
				this.page.current = 1;
				this.couponInfoPage();
			}

		}
	};
</script>
