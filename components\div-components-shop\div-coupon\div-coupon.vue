<template>
	<!-- 优惠券 -->
	<view>
		<view class="flex coupons"  :style="{backgroundColor: newData.background}" 
			:class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : '' ">
			<scroll-view class="scroll-view_x " scroll-x style="margin-right: -18px;padding-right: 15px;height: 65px;">
				<block v-for="(item, index) in couponInfoList" :key="index">
					<view class="item flex">
						<view class="cu-item radius" :style="{backgroundColor: newData.themeColor}" :class="newData.themeColor&&newData.themeColor.indexOf('bg-') != -1 ? newData.themeColor : ''"
							v-if="item.type == '1'">
							<view class="flex text-white electronic-coupons">
								<view class="flex-twice shadow-blur radius t1-r">
									<view class="margin-top-xs text-xs text-center overflow-1 coupons-shop-name" ><text class="cuIcon-shopfill"></text>{{item.shopInfo?item.shopInfo.name:''}}</view>
									<view class=" text-center">
										<text class="text-price text-xl"></text>
										<text class="number text-bold">{{item.reduceAmount}}</text>
									</view>
									<view class="text-center">
										<view class="text-xs">满{{item.premiseAmount}}元可用</view>
									</view>
								</view>
								<view class="flex-sub shadow-blur radius text-center t1-l">
									<view class="text-xs margin-top-sm">代金券</view>
									<view v-if="!item.couponUser" @tap="couponUserSave(item)" class="bg-white round sm margin-top-sm already">领取</view>
									<view v-if="item.couponUser" class=" bg-gray round sm margin-top-sm received">已领取</view>
								</view>
							</view>
						</view>
						<view v-else-if="item.type == '2'" class="cu-item radius" :style="{backgroundColor: newData.themeColor}"
							:class="newData.themeColor&&newData.themeColor.indexOf('bg-') != -1 ? newData.themeColor : ''">
							<view class="flex text-white electronic-coupons">
								<view class="flex-twice shadow-blur radius t1-r">
									<view class="margin-top-xs text-xs text-center overflow-1 coupons-shop-name"><text class="cuIcon-shopfill"></text>{{item.shopInfo?item.shopInfo.name:''}}</view>
									<view class=" text-center">
										<text class="number text-bold">{{item.discount}}折</text>
									</view>
									<view class="text-center">
										<view class="text-xs">满{{item.premiseAmount}}元可用</view>
									</view>
								</view>
								<view class="flex-sub shadow-blur radius text-center t1-l">
									<view class="text-xs margin-top-sm">折扣券</view>
									<view v-if="!item.couponUser" @tap="couponUserSave(item)" class=" bg-white round sm margin-top-sm already">领取</view>
									<view v-if="item.couponUser" class=" bg-gray round sm margin-top-sm received">已领取</view>
								</view>
							</view>
						</view>
					</view>
				</block>
			</scroll-view>
			<view class="get-more-p">
				<navigator class="get-more" :url="'/pages/coupon/coupon-list/index?shopId='+shopId">领取更多</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
						loadNumber: 3
					}
				}
			},
			shopId: {
				type: String
			}
		},
		mounted() {
			this.getData();
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				couponInfoList: [],
			};
		},
		methods: {
			// 获取数据
			getData() {
				api.couponInfoPage({
					current: 1,
					size: this.newData.loadNumber?this.newData.loadNumber:3,
					descs: 'create_time',
					shopId: this.shopId
				}).then(res => {
					this.couponInfoList = res.data.records;
				});
			},
			couponUserSave(item) {
				api.couponUserSave({
					couponId: item.id
				}).then(res => {
					uni.showToast({
						title: '领取成功',
						icon: 'success',
						duration: 2000
					});
					this.getData();
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.coupons {
		padding: 14px 10px;
		white-space: nowrap;
		width: 100%;
		overflow-x: scroll;
		height: auto;
	}
	.coupons .item {
		display: inline-block;
		width: 300upx;
		margin-right: 8upx;
		height: auto;
	}
	.coupons-shop-name{
		padding: 0 20upx;
		max-width: 190upx;
	}
	.electronic-coupons {
		height: 65px;
	}

	.t1-r {
		background-size: 100% 60%;
		background-repeat: no-repeat;
	}

	.t1-l {
		background-size: 100% 60%;
		background-repeat: no-repeat;
		border-left: 1px dashed rgba(255, 255, 255, .3);
	}

	.number {
		font-size: 18px;
	}

	.already {
		font-size: 10px;
		margin: 10px 5px;
		color: red;
	}

	.received {
		font-size: 10px;
		margin: 10px 5px;
	}

	.get-more-p {
		padding:0 4upx;
		background: #FFFFFF;
		z-index:1;
		width: 40px;
	}
	.get-more {
		writing-mode: vertical-rl;
		border: red solid 1px;
		text-align: center;
		color: red;
		border-radius: 4px;
		font-size: 12px;
		height:65px;
		margin-right: 5px;
	}
</style>
