<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<!--  #ifndef MP-WEIXIN -->
		<form @submit="loginSub">
			<view class="cu-form-group margin-top">
				<view class="title">号码</view>
				<input placeholder="请输入手机号" name="phone" v-model="form.phone"></input>
			</view>
			<view class="cu-form-group">
				<view class="title">验证码</view>
				<input placeholder="请输入验证码" name="code" maxlength=4 v-model="form.code"></input>
				<span @click="getPhoneCode"
						class="cu-btn bg-gray"
						:class="'display:' + msgKey">{{msgText}}</span>
			</view>
			<view class="padding flex flex-direction">
				<button class="cu-btn margin-tb-sm lg" :class="'bg-'+theme.backgroundColor" form-type="submit">立即登录</button>
			</view>
			<view class="margin-top flex justify-center text-sm" v-show="showPrivacyPolicy">
				<text>登录即代表您同意</text><navigator class="text-blue text-sm" :url="'/pages/public/webview/webview?title=用户协议&url='+protocolUrl">{{' 用户协议 '}}</navigator>和<navigator class="text-blue text-sm  " :url="'/pages/public/webview/webview?title=隐私政策&url='+privacyPolicyUrl">{{' 隐私政策'}}</navigator>
			</view>
		</form>
		<!--  #endif -->

		<!--  #ifdef MP-WEIXIN -->
		<view class="padding flex flex-direction">
			<button class="cu-btn margin-tb-sm lg" :class="'bg-'+theme.backgroundColor" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">一键登录</button>
		</view>
		<view class="margin-top flex justify-center text-sm" v-show="showPrivacyPolicy">
			<text>登录即代表您同意</text><navigator class="text-blue text-sm" :url="'/pages/public/webview/webview?title=用户协议&url='+protocolUrl">{{' 用户协议 '}}</navigator>和<navigator class="text-blue text-sm  " :url="'/pages/public/webview/webview?title=隐私政策&url='+privacyPolicyUrl">{{' 隐私政策'}}</navigator>
		</view>
		<!--  #endif -->
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import validate from 'utils/validate'
	import __config from '@/config/env';// 配置文件

	const MSGINIT = "发送验证码",
	          MSGSCUCCESS = "${time}秒后可重发",
	          MSGTIME = 60;
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				form: {},
				msgText: MSGINIT,
				msgTime: MSGTIME,
				msgKey: false,
				showPrivacyPolicy: __config.showPrivacyPolicy,
				privacyPolicyUrl: __config.privacyPolicyUrl,
				protocolUrl: __config.protocolUrl,
			};
		},

		components: {

		},
		props: {
			reUrl: {//重定向页面
				type: String,
				default: '/pages/home/<USER>'
			}
		},

		onLoad() {

		},

		onShow() {
		},

		methods: {
			getPhoneNumber(e) {
				if(e.detail.encryptedData){
					api.loginByPhoneMa(e.detail).then(res => {
						let userInfo = res.data;
						uni.setStorageSync('third_session', userInfo.thirdSession);
						uni.setStorageSync('user_info', userInfo);
						//登录完成跳到首页
						uni.switchTab({
						    url: '/pages/home/<USER>'
						});
						//获取购物车数量
						app.shoppingCartCount()
					});
				}
			},
			getPhoneCode(){
				if (this.msgKey) return
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.msgKey = true
				api.getPhoneCode({
					type: '1',
					phone: this.form.phone
				}).then(res => {
					this.msgKey = false
					if (res.code == '0') {
						uni.showToast({
							title: '验证码发送成功',
							icon: 'none',
							duration: 3000
						});
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						this.msgKey = true
						const time = setInterval(() => {
						  this.msgTime--
						  this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						  if (this.msgTime == 0) {
							this.msgTime = MSGTIME
							this.msgText = MSGINIT
							this.msgKey = false
							clearInterval(time)
						  }
						}, 1000)
					}else{

					}
				}).catch(() => {
				    this.msgKey = false
				});
			},
			loginSub(e){
				if(!validate.validateMobile(this.form.phone)){
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if(!this.form.code){
					uni.showToast({
						title: '请输入验证码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				api.loginByPhone(this.form).then(res => {
					let userInfo = res.data;
					uni.setStorageSync('third_session', userInfo.thirdSession);
					uni.setStorageSync('user_info', userInfo);
					//登录完成跳到首页
					uni.reLaunch({
					    url: decodeURIComponent(this.reUrl)
					});
					//获取购物车数量
					app.shoppingCartCount()
				});
			}
		}
	};
</script>
<style>

</style>
