<template>
	<!-- 砍价 -->
	<view class="bg-white">
		<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="bargain-bg">
			<image v-if="newData.backgroundImg" :src="newData.backgroundImg" class="bargain-image"></image>
			<view class="wrapper-list-goods radius" :style="{backgroundColor: newData.background,}" :class="newData.background? newData.background.indexOf('bg-') != -1 ? newData.background : '': '' "
			 v-if="newData.goodsList&&newData.goodsList.length > 0">
				<view class="cu-bar padding-left-sm padding-right-sm">
					<view class="goods-selection text-df margin-top-sm">
						<view class="margin-left-xs text-bold" :style="{color: `${newData.titleColor}`}">{{newData.title}}</view>
						<view class="margin-left-xs text-sm" :style="{color: `${newData.subtitleColor}`}">{{newData.subtitle}}</view>
					</view>
					<navigator :url="'/pages/bargain/bargain-list/index?shopId=' + shopId" class="bg-white round margin-top-sm text-sm bargain-more">更多</navigator>
				</view>
				<view class="bargain-list">
					<scroll-view class="scroll-view_x goods-detail margin-top-xs" scroll-x>
						<block v-for="(item, index) in newData.goodsList" :key="index">
							<navigator hover-class="none" :url="'/pages/bargain/bargain-detail/index?id=' + item.id" class="item shadow-warp flex goods-box radius">
								<view class="img-box">
									<image :src="item.picUrl ? item.picUrl : '/static/public/img/no_pic.png'"></image>
								</view>
								<view class="text-cut goods-name margin-top-sm text-sm padding-left-sm">{{item.name}}</view>
								<view class="cu-btn round sm bg-gradual-orange flex margin-left margin-right margin-top-xs">最低<text class="text-price">{{item.bargainPrice}}元</text></view>
							</navigator>
						</block>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
            shopId: {
                type: String
            }
		},
		mounted() {},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				couponInfoList: [],
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.bargain-bg {
		width: 96%;
		height: 498rpx;
		margin: auto;
	}

	.bargain-image {
		width: 96%;
		height: 498rpx;
		position: absolute;
	}

	.bargain-more {
		padding: 2px 10px;
		background: rgba(255, 255, 255, 0.71);
	}

	.bargain-list {
		padding-left: 20rpx;
		padding-right: 20rpx;
	}

	.goods-detail {
		width: auto;
		overflow: hidden;
	}

	.wrapper-list-goods {
		height: 498rpx;
		white-space: nowrap;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 200rpx;
		height: 336rpx;
		margin: 20rpx 20rpx 10rpx 10rpx;
		border: 1rpx solid #eee;
		background-color: #fff;
	}

	.wrapper-list-goods .item .img-box {
		width: 100%;
		height: 200rpx;
	}

	.wrapper-list-goods .item .img-box image {
		width: 100%;
		height: 100%;
		border-radius: 5rpx 5rpx 0 0;
	}
</style>
