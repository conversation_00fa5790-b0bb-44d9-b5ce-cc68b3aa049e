<template>
    <view>
		<cu-custom :bgColor="globalData.bgColor" :isBack="true"><block slot="content">{{title}}</block></cu-custom>
		<view class="margin-top-xl">
			<web-view :src="url" :webview-styles="webviewStyles"></web-view>
		</view>
	</view>
</template>
<script>
	const app = getApp();
	export default {
		data() {
			return {
				globalData: app.globalData,
				url:"",
				title:'浏览',
				webviewStyles: {
					progress: {
						color: '#FF3333'
					}
				}
			}
		},
		onShow() {
		},
		onLoad: function(option) {
			this.url = option.url;
			if(option.title){
				this.title = option.title;
				uni.setNavigationBarTitle({
					title:option.title
				})
			}
		},
		methods: {
		}
	}
</script>
<style>

</style>