<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">账号{{typeName}}</block>
		</cu-custom>
		<form @submit="regSub">
			<view class="cu-form-group margin-top">
				<view class="title">手机号</view>
				<input placeholder="请输入手机号" name="phone" v-model="form.phone"></input>
			</view>
			<view class="cu-form-group">
				<view class="title">验证码</view>
				<input placeholder="请输入验证码" name="code" maxlength=4 v-model="form.code"></input>
				<span @click="getPhoneCode"
						class="cu-btn bg-gray"
						:class="'display:' + msgKey">{{msgText}}</span>
			</view>
			<view class="cu-form-group">
				<view class="title">新密码</view>
				<input placeholder="请输入密码" password name="password" v-model="form.password"></input>
			</view>
			<view class="padding flex flex-direction">
				<button class="cu-btn margin-tb-sm lg" :class="'bg-'+theme.backgroundColor" form-type="submit">立即{{typeName}}</button>
			</view>
		</form>
		<view class="text-center">
			已有账号？<text class="text-red" @click="toLogin">立即登录</text>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api';
	import validate from 'utils/validate'
	
	const MSGINIT = "发送验证码",
	          MSGSCUCCESS = "${time}秒后可重发",
	          MSGTIME = 60;
			  
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				type: '2',
				typeName: '注册',
				form: {},
				msgText: MSGINIT,
				msgTime: MSGTIME,
				msgKey: false
			};
		},

		components: {
			
		},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let type = options.type
			if(type == 'reset'){
				this.typeName = '重置'
				this.type = '4'
			}
		},

		onShow() {
		},

		methods: {
			toLogin(){
				uni.reLaunch({
				    url: '/pages/login/index'
				});
			},
			regSub(e){
				let that = this
				if(!validate.validateMobile(this.form.phone)){
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if(!this.form.code){
					uni.showToast({
						title: '请输入验证码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				if(!this.form.password){
					uni.showToast({
						title: '请输入密码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				api.register(this.form).then(res => {
					uni.showModal({
						title: '提示',
						content: '恭喜您' + this.typeName + '成功，前去登录吧',
						success(res) {
							that.toLogin()
						}
					});
				});
			},
			getPhoneCode(){
				if (this.msgKey) return
				if (!validate.validateMobile(this.form.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.msgKey = true
				api.getPhoneCode({
					type: this.type,
					phone: this.form.phone
				}).then(res => {
					this.msgKey = false
					if (res.code == '0') {
						uni.showToast({
							title: '验证码发送成功',
							icon: 'none',
							duration: 3000
						});
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						this.msgKey = true
						const time = setInterval(() => {
						  this.msgTime--
						  this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						  if (this.msgTime == 0) {
							this.msgTime = MSGTIME
							this.msgText = MSGINIT
							this.msgKey = false
							clearInterval(time)
						  }
						}, 1000)
					}else{
						
					}
				}).catch(() => {
				    this.msgKey = false
				});
			}
		}
	};
</script>
<style>
	
</style>
