<template>
	<!-- 导航按钮组件 -->
	<!-- <view :class="'bg-'+theme.backgroundColor"> -->
	<view :style="{marginBottom: `${newData.pageSpacing}px`}" style="background: #fffff;">
		<view class="cu-list grid no-border navButton " :class="'col-'+newData.navButtons.length">
			<view class="cu-item" v-for="(item,index) in newData.navButtons" :key="index">
				<div-base-navigator :pageUrl="item.pageUrl" hover-class="none" >
					<image :src="item.imageUrl" class="nav_bt_img"></image>
					<text :style="{color: `${newData.textColor}`}">{{item.navName}}</text>
				</div-base-navigator>
			</view>
		</view>
	</view>
</template>

<script>
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	const app = getApp();
    export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        rowNum: 4,
					    textColor: '#333333',
					    pageSpacing: 0,
					    navButtons: []
	                }
	            }
            }
	    },
	    components: {
			divBaseNavigator
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
    }
</script>

<style scoped lang="scss">
	
	/* 导航 */
	.nav_bt_img{
		width: 80rpx !important;
		height: 80rpx !important;
	}
	
	.navButton{
		padding-top: 16rpx  !important;
		padding-bottom: 5rpx  !important;
	}
	
</style>
