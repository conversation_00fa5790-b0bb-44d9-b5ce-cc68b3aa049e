<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">商品列表</block>
		</cu-custom>

		<view class="cu-bar search fixed goods-search" :class="'bg-'+theme.backgroundColor">
			<view class="search-form round">
				<text class="cuIcon-search"></text>
				<navigator class="response" hover-class="none" :url="'/pages/base/search/index?shopId='+parameter.shopId">
					<input type="text" placeholder="请输入关键字" :value="parameter.name"></input>
				</navigator>
			</view>
			<view class="action">
				<view class="text-xxl">
					<text :class="'cuIcon-' + (viewType ? 'list' : 'cascades') + ' text-white'" @tap="viewTypeEdit"></text>
				</view>
			</view>
		</view>
		
		<view class="justify-center bg-white solid-bottom goods-nav">
			<view v-if="parameter.name && !parameter.shopId" class="bg-white nav">
				<view class="flex text-center">
					<view class="cu-item flex-sub cur" :class="'text-'+theme.themeColor">
						商品
					</view>
					<navigator class="cu-item flex-sub" hover-class="none" open-type="redirect" :url="'/pages/shop/shop-list/index?name='+parameter.name">
						店铺
					</navigator>
				</view>
			</view>
			<view class="grid response text-center align-start">
				<view class="flex-sub padding-sm margin-xs radius text-bold" :class="'text-'+theme.themeColor">{{title}}</view>
				<view class="flex-sub padding-sm margin-xs radius">
					<view class="grid text-center" @tap="sortHandle" data-type="price">价格<view class="margin-left-xs">
							<view :class="'cuIcon-triangleupfill ' + (price=='asc' ? 'text-'+theme.themeColor : '')" data-type="price"></view>
							<view class="basis-df"></view>
							<view :class="'cuIcon-triangledownfill ' + (price=='desc' ? 'text-'+theme.themeColor : '')" data-type="price"></view>
						</view>
					</view>
				</view>
				<view class="flex-sub padding-sm margin-xs radius">
					<view class="grid text-center" @tap="sortHandle" data-type="sales">销量<view class="margin-left-xs">
							<view :class="'cuIcon-triangleupfill ' + (sales=='asc' ? 'text-'+theme.themeColor : '')" data-type="sales"></view>
							<view class="basis-df"></view>
							<view :class="'cuIcon-triangledownfill ' + (sales=='desc' ? 'text-'+theme.themeColor : '')" data-type="sales"></view>
						</view>
					</view>
				</view>
				<view class="flex-sub padding-sm margin-xs radius">
					<view :class="createTime=='desc' ? 'text-bold text-'+theme.themeColor : ''" @tap="sortHandle" data-type="createTime">新上架</view>
				</view>
			</view>
		</view>
		<view >
			<view v-if="viewType&&goodsList.length>0" class="bg-white">
				<goods-card :goodsList="goodsList"></goods-card>
			</view>
			<view v-if="!viewType&&goodsList.length>0" class="bg-white">
				<goods-row :goodsList="goodsList"></goods-row>
			</view>
			<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import goodsCard from "components/goods-card/index";
	import goodsRow from "components/goods-row/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: ''
				},
				parameter: {
					shopId: ''
				},
				loadmore: true,
				goodsList: [],
				viewType: true,
				price: '',
				sales: '',
				createTime: '',
				title: ''
			};
		},

		components: {
			goodsCard,
			goodsRow
		},
		props: {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let title = options.title ? decodeURI(options.title) : '默认';

			this.title = title;

			if (options.categorySecond) {
				this.parameter.categorySecond = options.categorySecond;
			}
			
			if (options.categoryShopFirst) {
				this.parameter.categoryShopFirst = options.categoryShopFirst;
			}
			
			if (options.categoryShopSecond) {
				this.parameter.categoryShopSecond = options.categoryShopSecond;
			}

			if (options.name) {
				this.parameter.name = options.name;
			}

			if (options.type) {
				if (options.type == '1') {
					this.title = '新品首发';
					this.page.descs = 'create_time';
				}

				if (options.type == '2') {
					this.title = '热销单品';
					this.page.descs = 'sale_num';
				}
			}

			if (options.couponUserId) {
				this.parameter.couponUserId = options.couponUserId;
			}
			
			if(options.shopId){
				this.parameter.shopId = options.shopId
			}

			app.initPage().then(res => {
				this.goodsPage();
			});
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.goodsPage();
			}
		},

		methods: {
			goodsPage() {
				api.goodsPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let goodsList = res.data.records;
					this.goodsList = [...this.goodsList, ...goodsList];
					if (goodsList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			viewTypeEdit() {
				this.viewType = !this.viewType;
			},

			sortHandle(e) {
				let type = e.target.dataset.type;

				switch (type) {
					case 'price':
						if (this.price == '') {
							this.price = 'asc';
							this.page.descs = '';
							this.page.ascs = 'price_down';
						} else if (this.price == 'asc') {

							this.price = 'desc';
							this.page.descs = 'price_down';
							this.page.ascs = '';
						} else if (this.price == 'desc') {
							this.price = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.sales = '';
						this.createTime = '';
						break;

					case 'sales':
						if (this.sales == '') {
							this.sales = 'desc';
							this.page.descs = 'sale_num';
							this.page.ascs = '';
						} else if (this.sales == 'desc') {
							this.sales = 'asc';
							this.page.descs = '';
							this.page.ascs = 'sale_num';

						} else if (this.sales == 'asc') {
							this.sales = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.price = '';
						this.createTime = '';
						break;

					case 'createTime':
						if (this.createTime == '') {
							this.createTime = 'desc';
							this.page.descs = 'create_time';
							this.page.ascs = '';
						} else if (this.createTime == 'desc') {
							this.createTime = '';
							this.page.descs = '';
							this.page.ascs = '';
						}
						this.price = '';
						this.sales = '';
						break;
				}

				this.relod();
			},

			relod() {
				this.loadmore = true;
				this.goodsList = [];
				this.page.current = 1;
				this.goodsPage();
			}

		}
	};
</script>
<style>
	.goods-search {
		top: unset !important;
		min-height: 100rpx !important
	}
	
	.goods-nav {
		top: unset !important;
		margin-top: 100rpx;
	}

	.cuIcon-triangledownfill {
		margin-top: -22rpx
	}
</style>
