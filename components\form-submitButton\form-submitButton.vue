<template>
	
	<view class="submitButtonComponent flex"  :style="{'justify-content':`${newData.location}`,background: `${newData.backgroundColor}`, marginBottom: `${newData.pageSpacing}px`}">
		<view :style="{width:`${newData.size=='100%'?'100%':''}`}" > 
			<button
				:style="{color: `${newData.fontColor}`, background: `${newData.buttonColor}`}" @click="submit"
				:size="newData.size">{{newData.content}}</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				rateValue: 0,
				fromDate:[],
			};
		},

		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		created() {
			this.rateValue = this.newData.initRate
		},
		methods: {
			onChange() {

			},
			formateRate() {
				if (this.newData.isHalf) {
					let step = 0.5;
					return this.newData.textList[(this.rateValue / step) - 1];
				} else {
					return this.newData.textList[(this.rateValue) - 1];
				}
			},
			submit(){
				this.$emit("submit",this.newData)
			},
			checkValue(){
				console.log("检验值了submitButtonComponent")
				return true;
			}
		}
	};
</script>
<style>
	.submitButtonComponent {
		padding: 5px 15px;
	}
</style>
