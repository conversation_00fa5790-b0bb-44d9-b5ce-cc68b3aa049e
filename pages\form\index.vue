<template>
	<view class="index_page" :style="pageStyle" v-if="pageShowFlag">
		<view  v-if="!showDefaultAppPage">
			<!-- <view v-if="false"> -->
			<!-- 自定义页面组件 -->
			<block v-for="(temp, index) in pageDivData.pageComponent.componentsList" :key="index">
				<!-- 弹窗组件 -->
				<template v-if="temp.componentName === 'popupComponent'">
					<view>
						<div-popup :ref="'formComponent'+index" v-model="temp.data"></div-popup>
					</view>
				</template>
				<!-- 首页导航 -->
				<template v-else-if="temp.componentName === 'navTitleComponent'">
					<view>
						<nav-title :ref="'formComponent'+index" v-model="temp.data"></nav-title>
					</view>
				</template>
				<!-- 单列图片 -->
				<template v-else-if="temp.componentName === 'imageComponent'">
					<view>
						<div-image :ref="'formComponent'+index" v-model="temp.data"></div-image>
					</view>
				</template>
				<!-- 双列图片 -->
				<template v-else-if="temp.componentName === 'doubleRowImageComponent'">
					<view>
						<double-row-image :ref="'formComponent'+index" v-model="temp.data"></double-row-image>
					</view>
				</template>
				<!-- 富文本 -->
				<template v-else-if="temp.componentName === 'richTextComponent'">
					<view>
						<base-rich-text :ref="'formComponent'+index" v-model="temp.data"></base-rich-text>
					</view>
				</template>
				<!-- 轮播图 -->
				<template v-else-if="temp.componentName === 'swiperComponent'">
					<view>
						<div-swiper :ref="'formComponent'+index" v-model="temp.data"></div-swiper>
					</view>
				</template>
				<!-- 消息通知 -->
				<template v-else-if="temp.componentName === 'noticeComponent'">
					<view>
						<div-notice :ref="'formComponent'+index" v-model="temp.data"></div-notice>
					</view>
				</template>
				<!-- 标题文字 -->
				<template v-else-if="temp.componentName === 'titleTextComponent'">
					<view>
						<div-title-text :ref="'formComponent'+index" v-model="temp.data"></div-title-text>
					</view>
				</template>
				<!-- 导航按钮 -->
				<template v-else-if="temp.componentName === 'navButtonComponent'">
					<view>
						<div-nav-button :ref="'formComponent'+index" v-model="temp.data"></div-nav-button>
					</view>
				</template>
				<!-- 悬浮按钮 -->
				<template v-else-if="temp.componentName === 'suspendBtnComponent'">
					<view>
						<suspend-btn :ref="'formComponent'+index" v-model="temp.data"></suspend-btn>
					</view>
				</template>
				<!-- 商品封面   注意名字-->
				<template v-else-if="temp.componentName === 'categoryComponent'">
					<view>
						<goods-category :ref="'formComponent'+index" v-model="temp.data"></goods-category>
					</view>
				</template>
				<!-- 分割线 -->
				<template v-else-if="temp.componentName === 'cuttingLineComponent'">
					<view>
						<cutting-line :ref="'formComponent'+index" v-model="temp.data"></cutting-line>
					</view>
				</template>
				<!-- 底部导航 -->
				<template v-else-if="temp.componentName === 'tabBarComponent'">
					<view>
						<cus-tab-bar @bottomHeightShow="bottomHeightShow" v-model="temp.data"></cus-tab-bar>
					</view>
				</template>
				<!-- 多功能按钮 -->
				<template v-else-if="temp.componentName === 'functionButtonComponent'">
					<view v-if="showFunctionButtonComponent(temp.data)">
						<function-button @bottomHeightShow="bottomHeightShow" :order="buttonOrder" v-model="temp.data">
						</function-button>
					</view>
				</template>
				<!-- 地图 -->
				<template v-else-if="temp.componentName === 'mapComponent'">
					<view>
						<map-com v-model="temp.data"></map-com>
					</view>
				</template>
				<!-- 热点图片 -->
				<template v-else-if="temp.componentName === 'areaImageComponent'">
					<view>
						<area-image v-model="temp.data"></area-image>
					</view>
				</template>
				<!-- 视频 -->
				<template v-else-if="temp.componentName === 'videoComponent'">
					<view>
						<div-video :ref="'formComponent'+index" v-model="temp.data"></div-video>
					</view>
				</template>


				<template v-else-if="temp.componentName === 'singleLineInput'">
					<view>
						<form-singleLineInput :ref="'formComponent'+index" v-model="temp.data"></form-singleLineInput>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'dropDownSelect'">
					<view>
						<form-dropDownSelect :ref="'formComponent'+index" v-model="temp.data"></form-dropDownSelect>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'radioSelect'">
					<view>
						<form-radioSelect :ref="'formComponent'+index" v-model="temp.data"></form-radioSelect>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'datePicker'">
					<view>
						<form-datePicker :ref="'formComponent'+index" v-model="temp.data"></form-datePicker>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'phoneCheck'">
					<view>
						<form-phoneCheck :ref="'formComponent'+index" v-model="temp.data"></form-phoneCheck>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'imgUpload'">
					<view>
						<form-imgUpload :ref="'formComponent'+index" v-model="temp.data"></form-imgUpload>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'submitButton'">
					<view>
						<form-submitButton :ref="'formComponent'+index" v-on:submit="fromSubmit" v-model="temp.data">
						</form-submitButton>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'rateControl'">
					<view>
						<form-rateControl :ref="'formComponent'+index" v-model="temp.data"></form-rateControl>
					</view>
				</template>
			</block>
			<!-- <view :class="'cu-load bg-gray ' + (loadmore3?'loading':'over')"></view> -->
		</view>
		<!-- 用户隐私政策授权弹框,小程序端首页不弹出 -->
		<!-- #ifdef APP-PLUS -->
		<privacy-policy v-if="showPrivacyPolicy"></privacy-policy>
		<!-- #endif -->
		<bottomRemind :pageDivData="pageDivData"></bottomRemind>
		<view v-if="cusTabBarShowFlag" :style="{height: `${bottomHeight}px`}"></view>
	</view>

</template>

<script>
	// #ifdef APP-PLUS
	import privacyPolicy from '@/components/privacy-policy/index';
	// #endif
	const app = getApp();
	import api from '@/utils/api'
	import goodsCard from "components/goods-card/index";
	import goodsRow from "components/goods-row/index";
	import shopInfo from "components/shop-info/index";
	import __config from "@/config/env";


	import divGoods from "@/components/div-components/div-goods/div-goods.vue";
	import divGoodsRow from "@/components/div-components/div-goods-row/div-goods-row.vue";


	import divNavButton from "@/components/div-components/div-nav-button/div-nav-button.vue";
	import divNotice from "@/components/div-components/div-notice/div-notice.vue";
	import divTitleText from "@/components/div-components/div-title-text/div-title-text.vue";
	import divSwiper from "@/components/div-components/div-swiper/div-swiper.vue";
	import goodsCategory from "@/components/base/category.vue";
	import navTitle from "@/components/base/navTitle.vue";
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import goodsCover from '@/components/goods-cover/goods-cover.vue'
	import suspendBtn from '@/components/base/suspendBtn.vue'
	import divImage from "@/components/div-components/div-image/div-image.vue";
	import doubleRowImage from "@/components/double-row-image/index.vue";
	import baseRichText from "@/components/rich-text/index.vue";
	import cuttingLine from "@/components/base/cuttingLine.vue";
	import cusTabBar from "@/components/base/cusTabBar.vue";
	import functionButton from "@/components/base/function-button/functionButton.vue";
	import mapCom from "@/components/base/mapCom.vue";
	import areaImage from "@/components/base/areaImage.vue";
	import divVideo from "@/components/div-components/div-video/div-video.vue";
	import divPopup from "@/components/div-components/div-popup/div-popup.vue";

	import formSingleLineInput from '@/components/form-singleLineInput/form-singleLineInput.vue';
	import formDropDownSelect from '@/components/form-dropDownSelect/form-dropDownSelect.vue';
	import formRadioSelect from '@/components/form-radioSelect/form-radioSelect.vue';
	import formDatePicker from '@/components/form-datePicker/form-datePicker.vue';
	import formPhoneCheck from '@/components/form-phoneCheck/form-phoneCheck.vue';
	import formSubmitButton from '@/components/form-submitButton/form-submitButton.vue';
	import formImgUpload from '@/components/form-imgUpload/form-imgUpload.vue';
	import formRateControl from '@/components/form-rateControl/form-rateControl.vue';

	import jweixin from '@/utils/jweixin'
	const util = require("utils/util.js");

	import bottomRemind from "@/components/bottom-remind/index.vue";
	export default {
		components: {
			// #ifdef APP-PLUS
			privacyPolicy,
			// #endif
			goodsCard,
			goodsRow,
			divImage,
			divSwiper,
			divNavButton,
			divNotice,
			divTitleText,
			divGoods,
			divGoodsRow,
			uniNavBar,
			goodsCover,
			navTitle, //导航栏
			goodsCategory, //分类
			suspendBtn, //悬浮图标
			doubleRowImage, //双列图片
			baseRichText, //富文本
			cuttingLine, //分割线
			cusTabBar, //底部导航
			functionButton, //多功能按钮
			mapCom,//地图
			areaImage, //热点图片
			divVideo, //视频组件
			divPopup, //弹窗组件

			formSingleLineInput,
			formDropDownSelect,
			formRadioSelect,
			formDatePicker,
			formPhoneCheck,
			formSubmitButton,
			formImgUpload,
			formRateControl,
			bottomRemind, //底部提示
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				showPrivacyPolicy: __config.showPrivacyPolicy,
				CustomBar: this.CustomBar,
				page: {
					searchCount: false,
					current: 1,
					size: 10
				},
				showDefaultAppPage: false,
				loadmore: true,
				pageDivData: {
					pageComponent: {
						componentsList: []
					}
				}, //首页自定义配置组件的数据
				scrollLeft: 0,
				cardCur: 0,
				shopInfoData: [],
				TabCur: 0,
				firstCategoryData: [{
					id: '0',
					name: '首页'
				}],
				secondCategoryData: [],
				page2: {
					searchCount: false,
					current: 1,
					size: 10
				},
				loadmore2: true,
				goodsList2: [],
				loadmore3: true, // 自定义页面的加载状态
				bottomHeight: '', // 底部菜单高度
				cusTabBarShowFlag: false, // 底部菜单高度
				buttonOrder: {}, // 底部菜单高度
				pageStyle: {}, // 页面样式
				pageShowFlag: false, // 页面是否展示
        isLogin:true
			};
		},

		props: {},
		onLoad(options) {
      if(!uni.getStorageSync('third_session')){
        this.isLogin=false
      }
			console.log("图文表单", options)
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let that = this
			app.initPage().then(res => {
				console.log("初始化页面", res)
				this.loadData();
			});


			// api.jokelLoginWxMp({
			// 	jsCode: '1111'
			// }).then(res => {
			// 	//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
			// 	let userInfo = res.data;
			// 	uni.setStorageSync('third_session', userInfo.thirdSession);
			// 	uni.setStorageSync('user_info', userInfo);
			// 	//获取购物车数量
			// 	// that.shoppingCartCount();
			// 	console.log("公众号登录成功",res)
			// }).catch(res=>{
			// 	console.log("公众号登录失败 ",res)
			// });
		},
		onShow() {
			//更新购物车tabar角标数量
			// uni.setTabBarBadge({
			// 	index: 3,
			// 	text: app.globalData.shoppingCartCount + ''
			// });
		},

		// 小程序页面准备完成时调用
		onReady() {
			// #ifdef MP-WEIXIN
			// 设置页面标题
			if (this.pageDivData && this.pageDivData.pageBase && this.pageDivData.pageBase.pageTitle) {
				wx.setNavigationBarTitle({
					title: this.pageDivData.pageBase.pageTitle
				});
			}
			// #endif
		},

		onShareAppMessage: function() {
			// 默认标题
			let title = 'JooLun商城源码-小程序演示';
			let path = 'pages/form/index';

			// 如果有自定义分享标题和图片，则使用自定义的
			if (this.pageDivData && this.pageDivData.pageBase) {
				if (this.pageDivData.pageBase.shareTitle) {
					title = this.pageDivData.pageBase.shareTitle;
				}

				// 获取当前页面参数
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const options = currentPage.options || {};

				// 构建分享路径，保留当前页面参数
				path = 'pages/form/index';
				let queryParams = [];
				for (let key in options) {
					queryParams.push(key + '=' + options[key]);
				}

				if (queryParams.length > 0) {
					path += '?' + queryParams.join('&');
				}
			}

			// 添加分享者用户编码
			const userInfo = uni.getStorageSync('user_info');
			if (userInfo && userInfo.userCode) {
				if (path.indexOf('?') > -1) {
					path += '&sharer_user_code=' + userInfo.userCode;
				} else {
					path += '?sharer_user_code=' + userInfo.userCode;
				}
			}

			// 分享图片
			let imageUrl = '';
			if (this.pageDivData && this.pageDivData.pageBase && this.pageDivData.pageBase.shareImgUrl) {
				imageUrl = this.pageDivData.pageBase.shareImgUrl;
			}

			return {
				title: title,
				path: path,
				imageUrl: imageUrl,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log('分享成功:', res.errMsg);
					}
				},
				fail: function(res) {
					console.log('分享失败:', res);
				}
			};
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},

		onReachBottom() {
			if (this.showDefaultAppPage) {
				if (this.TabCur === 0 && this.loadmore) {
					this.page.current = this.page.current + 1;
					this.goodsPage();
				}
				if (this.TabCur == !0 && this.loadmore2) {
					this.page2.current = this.page2.current + 1;
					this.goodsPageByCateGory();
				}
			}
		},

		methods: {
			loadData() {
				// #ifdef H5
				let tenantId = util.getUrlParam(location.href, "tenant_id");
				let appid = util.getUrlParam(location.href, "app_id");
				let pageId = util.getUrlParam(location.href, "page_id");
				let shareFriendId = util.getUrlParam(location.href, "sharer_friend_id");
				// #endif
				// #ifndef H5
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const options = currentPage.options || {};
				let tenantId = options.tenant_id || '';
				let appid = options.app_id || '';
				let pageId = options.page_id || '';
				let shareFriendId = options.sharer_friend_id || '';
				// #endif

				this.loadmore3 = true;
				let params = {
					id: pageId,
					appid: appid,
					tenantid: tenantId,
					shareFriendId: shareFriendId,
				}

				// api.getButtonOrder(pageId).then(res => {
				// 	console.log("查询按钮订单", res)
				// 	this.buttonOrder = res.data;
					// console.log("请求参数", params)
        let promise
        if (this.isLogin){
          promise=api.pagedevise(params)
        }else{
          promise=api.pageDeviseFreedom(params)
        }
        promise.then(res => {
						console.log("组件", res)
						let pageDivData = res.data;
						if (pageDivData) {
							this.pageDivData = pageDivData;
							this.pageStyle={
								backgroundColor:this.pageDivData.pageBase.backgroundColor?this.pageDivData.pageBase.backgroundColor:'',
								backgroundImage:  "url("+this.pageDivData.pageBase.background+")",
							}
							uni.setNavigationBarTitle({
								title: this.pageDivData.pageBase.pageTitle
							});
							// this.getButtonOrderMsg();
              if (this.isLogin){

                //判断用户页面限制
                this.checkJump()
              }else{
                this.pageShowFlag=true
              }
							// #ifdef H5
							this.initShareWx();
							// #endif
							if (!pageDivData || !pageDivData.pageComponent || pageDivData.pageComponent.componentsList
								.length == 0) {
								// 如果没有设置自定义页面数据，那就显示默认原始页面
								// this.getDefaultPageData();
							} else {
								this.showDefaultAppPage = false;
								this.loadmore3 = false;
							}
						} else {
							// 如果没有设置自定义页面数据，那就显示默认原始页面
							// this.getDefaultPageData();
						}
					}).catch(err => {
						console.log(err)
					});
				// });
			},
			getDefaultPageData() {
				this.showDefaultAppPage = true;
			},
			jumpPage(url) {
				uni.navigateTo({
					url: url
				});
			},
			tabSelect(e) {
				let index = e.currentTarget.dataset.index
				let TabCur = this.TabCur
				let firstCategory = this.firstCategoryData[index]
				this.TabCur = index;
				this.scrollLeft = (index - 1) * 60
				if (index != 0 && TabCur != index) {
					api.goodsCategoryPage({
						searchCount: false,
						current: 1,
						size: 9,
						ascs: 'sort',
						parentId: firstCategory.id,
						enable: '1'
					}).then(res => {
						this.secondCategoryData = res.data.records;
					});
					this.loadmore2 = true;
					this.goodsList2 = [];
					this.page2.current = 1;
					this.goodsPageByCateGory();
				}
			},
			setGoodsCategoryParam() {
				/* 把参数信息异步存储到缓存当中 */
				uni.setStorage({
					key: 'param-goods-category-index',
					data: this.TabCur - 1
				});
			},
			cardSwiper(e) {
				this.cardCur = e.detail.current
			},

			refresh() {
				this.loadmore = true;
				this.page.current = 1;
				this.goodsList = [];
				this.goodsListNew = [];
				this.goodsListHot = [];
				this.loadData();
			},
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},
			userInfoUpdateByMp(parm) {
				let that = this;
				console.log("去授权了")
				api.userInfoUpdateByMp(parm).then(res => {
					// #ifdef H5
					//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
					let query = that.$Route.query;
					delete query.code;
					delete query.state;
					util.resetPageUrl(query);
					// #endif

					console.log("授权更新用户信息",res)
					if(res && res.data){
						let userInfo =  uni.getStorageSync('user_info');
						userInfo.headimgUrl = res.data.headimgUrl;
						userInfo.nickName = res.data.nickName;
						uni.setStorageSync('user_info', userInfo);
						this.userInfoGet();
					}
				}).catch(res => {

				});
			},
			//子组件触发的提交方法   会再次触发子组件的校验，并取值。
			fromSubmit(obj) {
				console.log("提交表单", obj)
				console.log(this.$refs)
				let submitArray = [];
				let array = this.pageDivData.pageComponent.componentsList;
				let phone = "";
				let phoneCode = "";
				for (let i = 0; i < array.length; i++) {
					console.log(array[i].config)
					if (array[i].config.type == 4 && array[i].config.ableSubmit) {
						let conmpont = 'formComponent' + i;
						let result = this.$refs[conmpont][0].checkValue();
						if (!result) {
							return;
						} else {
							if (result instanceof Object) {
								result.id = array[i].id;
								if (result.hasOwnProperty("checkCode")) {
									phone = result.value;
									phoneCode = result.checkCode;
									result = {
										value: result.value,
										id: result.id
									}
								}
								submitArray.push(result);
							}
						}
					}
				}
				console.log("提交的参数", submitArray)

				// #ifdef H5
				let tenantId = util.getUrlParam(location.href, "tenant_id");
				let appId = util.getUrlParam(location.href, "app_id");
				let pageId = util.getUrlParam(location.href, "page_id");
				// #endif
				// #ifndef H5
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const options = currentPage.options || {};
				let tenantId = options.tenant_id || '';
				let appId = options.app_id || '';
				let pageId = options.page_id || '';
				// #endif

				let userInfo = uni.getStorageSync('user_info');
				api.submitFormData({
					tenantId: tenantId,
					appId: appId,
					pageId: pageId,
					phone: phone,
					phoneCode: phoneCode,
					data: JSON.stringify(submitArray),
					openId: userInfo.openId ? userInfo.openId : '',
					tagId:obj.submitTag.id,
				}).then(res => {
					uni.showModal({
						title: '提示',
						content: obj.submitMsg,
						success: (res) => {
							if (res.confirm) {
								this.jumpUrl(obj.jumpObj)
							} else if (res.cancel) {
								this.jumpUrl(obj.jumpObj)
							}
						}
					});
				});
			},
			//判断底部导航栏出现并腾出位置
			bottomHeightShow(obj) {
				// console.log("开了", obj)
				if (obj) {
					this.bottomHeight = obj
					this.cusTabBarShowFlag = true
				}
			},
			//拿取当前页订单信息 用于多功能按钮的显示
			getButtonOrderMsg() {
				//判断有无 支付类型的多功能按钮
				for (let i = 0; i < this.pageDivData.pageComponent.componentsList.length; i++) {
					let obj = this.pageDivData.pageComponent.componentsList[i];
					if (obj.componentName == "functionButtonComponent") { //有无多功能菜单
						console.log("请求参数22", obj)
						for (let j = 0; j < obj.data.buttonList.length; j++) {
							if (obj.data.buttonList[j].button.type == 2) { //有无支付按钮
								let pageId = this.pageDivData.id;
								console.log("请求参数", pageId)
								api.getButtonOrder(pageId).then(res => {
									console.log("查询按钮订单", res)
									this.buttonOrder = res.data;
								});
								return
							}
						}
					}
				}
			},
			//判断是否显示多功能按钮
			showFunctionButtonComponent(obj) {
				console.log("对象",obj,this.buttonOrder)
				if(obj.showRule == 1){
					return true
				}if(obj.showRule == 2 ){
					if(!this.buttonOrder){ //没有订单时显示
						return true
					}else{//有订单且未支付显示
						if(this.buttonOrder.isPay == "1"){
							return false;
						}else{
							return true
						}
					}
				}
				if(obj.showRule == 3 &&  this.buttonOrder && this.buttonOrder.isPay == "1"){ //支付后 有订单且 确认支付了
					return true
				}
				return false ;
			},
			//初始化分享
			initShareWx() {
				// h5页面加载时 会默认初始化分享
				// #ifdef H5
				if (util.isWeiXinBrowser()) {
					let shareObj = {
						title: this.pageDivData.pageBase.shareTitle ? this.pageDivData.pageBase.shareTitle : '',
						desc: this.pageDivData.pageBase.describe ? this.pageDivData.pageBase.describe : '',
						imgUrl: this.pageDivData.pageBase.shareImgUrl ? this.pageDivData.pageBase.shareImgUrl : ''
					};
					// console.log("来分享的参数",shareObj)
					let url = util.setH5ShareUrl();
					// //重新设置新的url
					let query = this.$Route.query;
					delete query.code;
					delete query.state;
					query.sharer_friend_id = util.getUrlParam(url, "sharer_friend_id");
					util.resetPageUrl(query);
					// console.log("重选设置url",query)
					api.getJsSdkConfig({
						url: location.href
					}).then(res => {
						// history.replaceState(history.state, null, url);
						let wxConfig = res.data;
						let shareObjTemp = {
							title: shareObj.title ? shareObj.title : '',
							desc: shareObj.desc ? shareObj.desc : '',
							link: wxConfig.url,
							imgUrl: shareObj.imgUrl ? shareObj.imgUrl : '',
						};
						jweixin.shareWxFriend(wxConfig, shareObjTemp, function() {
							// that.showModal = true;
							// uni.hideLoading();
							// console.log("初始化微信分享成功", shareObjTemp)
						}, function(e) {
							// console.log("初始化微信分享成功error",e)
						}, );
					}).catch(res => {
						console.log('调用getJsSdkConfig失败：' + res)
					});
				}
				// #endif

				// #ifdef MP-WEIXIN
				// 小程序分享设置在onShareAppMessage中已经实现
				// #endif
			},
			//检查用户此页面限制
			checkJump(){
				if (this.pageDivData.forbidFlag) {
					if (!this.pageDivData.pageBase.forbidPageUrl || this.pageDivData.pageBase.forbidPageUrl.indexOf('/') <
						0) {
						return;
					}
					if (this.pageDivData.pageBase.forbidIsSystemUrl) {
						return uni.navigateTo({
							url: this.pageDivData.pageBase.forbidPageUrl
						});
					} else {
						// #ifdef H5
						return window.location.href = this.pageDivData.pageBase.forbidPageUrl
						// #endif
						// #ifndef H5
						return uni.navigateTo({
							url: this.pageDivData.pageBase.forbidPageUrl
						});
						// #endif
					}
				}
				if (this.pageDivData.permitFlag) {
					this.pageShowFlag = true;
				}else{
					if (!this.pageDivData.pageBase.permitPageUrl || this.pageDivData.pageBase.permitPageUrl.indexOf('/') <0) {
						return;
					}
					if (this.pageDivData.pageBase.permitIsSystemUrl) {
						return uni.navigateTo({
							url: this.pageDivData.pageBase.permitPageUrl
						});
					} else {
						// #ifdef H5
						return window.location.href = this.pageDivData.pageBase.permitPageUrl
						// #endif
						// #ifndef H5
						return uni.navigateTo({
							url: this.pageDivData.pageBase.permitPageUrl
						});
						// #endif
					}
				}

			},
			//页面跳转
			jumpUrl(obj) {
				console.log("页面跳转", obj)
				if (!obj.pageUrl || obj.pageUrl.indexOf('/') < 0) {
					return;
				}
				if (obj.isSystemUrl) {
					uni.navigateTo({
						url: obj.pageUrl
					});
				} else {
					// #ifdef H5
					window.location.href = obj.pageUrl;
					// #endif
					// #ifndef H5
					uni.navigateTo({
						url: obj.pageUrl
					});
					// #endif
				}
			},

		}
	};
</script>
<style>
	@import "./index.css";
</style>
