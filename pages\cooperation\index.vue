
<template>
		<view>
			<image class="cooperation_img" mode="widthFix" src="../../static/public/img/cooperation/img.jpg"></image>
		</view>
</template>

<script>
	export default {
		components: {
		},
		data() {
			return {
			
			};
		},
		props: {},
		mounted() {
		},
		onLoad(options) {
			
		},
		onPageScroll(e){
		},
		onShow() {
			
		},
		onShareAppMessage() {
			
		},

		onPullDownRefresh() {
		},
		onReachBottom() {
		},
		methods: {
			
		}
	};
</script>
<style>
	.cooperation_img{
		width: 100%;
	}
</style>
