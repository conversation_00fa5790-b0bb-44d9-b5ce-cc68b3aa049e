<template>
	<!-- 搜索组件 -->
	<view class="cu-bar search  " :style="{backgroundColor: newData.background}" 
		:class="newData.background&&newData.background.indexOf('bg-')!=-1?newData.background:''">
		<view class="search-form round" :style="{backgroundColor: newData.color, borderRadius: `${newData.radius}rpx`,color: `${newData.textColor}`}">
			<text class="cuIcon-search" ></text>
			<navigator class="response" hover-class="none" url="/pages/base/search/index">
				<view :style="{color: newData.textColor, 'text-align':newData.textPosition == 'center'?'center':'left',marginLeft: newData.textPosition == 'center'?'-25px':'0px'}"  >{{newData.placeholder}}</view>
			</navigator>
		</view>
	</view>
</template>

<script>
	const app = getApp();
    export default {
        name: 'basic-search',
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        background: `#efeff4`,
                        color: '#ffffff',
                        placeholder: '请输入关键字',
                        radius: 38,
                        textColor: '#999999',
                        textPosition: `center`,
	                }
	            }
            }
	    },
	    components: {
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value
			};
		},
		methods: {
		}
    }
</script>

<style scoped lang="scss">

</style>
