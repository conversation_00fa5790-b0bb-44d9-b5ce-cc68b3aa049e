<!-- 作品封面组件 -->
<template>
	<view style="text-align: center;">
		<view v-for="(item, rowIndex) in newData.rowNumber" :key="rowIndex">
			<view v-for="(item, colIndex) in newData.colNumber" :key="colIndex" style="display: inline-block"
				:style="{width:`${(1/newData.colNumber)*100}%`}">
				<div-base-navigator v-if="imgObjecyt[rowIndex+''+colIndex]" :isSystemUrl="newData.isSystemUrl" :pageUrl="imgObjecyt[rowIndex+''+colIndex]?newData.pageUrl+'&goods_id='+imgObjecyt[rowIndex+''+colIndex].id:''">
					<image mode="widthFix"
						:style="{width:`${newData.width}%`}"
						:src='imgObjecyt[rowIndex+""+colIndex]?imgObjecyt[rowIndex+""+colIndex].url:""'>
					</image>
					<h4 style="white-space: nowrap; overflow: hidden;" v-if="newData.nameFlag">《{{imgObjecyt[rowIndex+""+colIndex]?imgObjecyt[rowIndex+""+colIndex].name:''}}》</h4>
				</div-base-navigator>
			</view>
		</view>
		<view class="page_button">
			<!-- 分页组件 -->
			<uni-pagination ref="pagination1" title="标题文字"  :total="page.total" :pageSize="page.pageSize" v-model="page.currentPage" @change="change"
				:showAround="true">
			</uni-pagination>
		</view>
	</view>
</template>

<script>
	import api from '@/utils/api'
	const util = require("utils/util.js");
	const app = getApp();
	import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
	export default {
		components: {divBaseNavigator},
		data() {
			return {
				newData: this.value,
				page: {
					total: 1, // 总页数
					currentPage: 1, // 当前页数
					pageSize: 1, // 每页显示多少条
					ascs: [], //升序字段
					descs: [] //降序字段
				},
				list: [], //产品list
				imgObjecyt: {},
			};
		},
		computed: {
			// 计算属性的 getter
			// page.currentPage() {
			//   return  newData.rowNumber*newData.colNumber
			// }
		},

		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		created(options) {
			// console.log('newData', this.newData)
			
			this.loadData();
			
		},
		methods: {
			loadData() {
				let tenantId = util.getUrlParam(location.href, "tenant_id");
				let appid = util.getUrlParam(location.href, "app_id");
				let pageId = util.getUrlParam(location.href, "page_id");
				this.loadmore3 = true;
				let tagIdList =[];
				for (var i = 0; i < this.newData.tagList.length; i++) {
					tagIdList.push(this.newData.tagList[i].id)
				}
				let params = {
					tagIdList: tagIdList,
					searchType: this.newData.searchType,
				}
				this.page.pageSize= this.newData.rowNumber * this.newData.colNumber;
				let obj = Object.assign({
					total: this.page.total,
					current: this.page.currentPage,
					size: this.page.pageSize,
					descs: this.page.descs,
					ascs: this.page.ascs,
				}, params)
				
				// console.log("加载商品信息",obj)
				api.goodsByTag(obj).then(res => {
					// console.log("加载商品res",res )
					this.formatCoverDate(res);
				}).catch(err => {
					console.log("错误"+err)
				});
			},
			change(obj) {
				// this.$set(this.page, 'currentPage', obj.current);
				this.loadData()
			},
			formatCoverDate(res) {
				if(!res.data.records || res.data.records .length==0){
					return;
				}
				this.imgObjecyt = {};
				for (let i = 0; i < res.data.records.length; i++) {
					res.data.records[i].coverUrls = JSON.parse(res.data.records[i].coverUrls)
				}
				let index = 0;
				for (let i = 0; i < this.newData.rowNumber; i++) {
					for (let j = 0; j < this.newData.colNumber; j++) {
						let obj = {
							id:'',
							url:'',
							name:'',
						}
						if(!res.data.records[index]){
							continue;
						}
						//设置id
						if(res.data.records[index].id){
							obj.id = res.data.records[index].id;
						}
						//设置标题
						if(res.data.records[index].name){
							obj.name = res.data.records[index].name;
						}
						// 设置url
						for (let z = 0; i < res.data.records[index].coverUrls.length; z++) {
							if(!res.data.records[index].coverUrls[z] || !res.data.records[index].coverUrls[z].sizeType ){
								continue;
							}
							if (this.newData.imgShowSize == res.data.records[index].coverUrls[z].sizeType) {
										obj.url= res.data.records[index].coverUrls[z].url;
								break;
							}
						}
						this.$set(this.imgObjecyt, (i + '' + j),obj)
						index++;
					}
				}
				this.list = res.data.records
				this.page.total=res.data.total
				this.page.currentPage=res.data.current
				this.page.pageSize=res.data.size
			},
			getUrl(rowIndex,colIndex){
				if(!this.imgObjecyt.hasOwnProperty(rowIndex+''+colIndex))	{
					return;
				}
				return  this.newData.pageUrl+'&id='+this.imgObjecyt[rowIndex+''+colIndex].id;
			}
		}
	};
</script>
<style>
.page_button{
	
}
</style>
