
// #ifdef H5
const jweixin = require('public/jweixin-module/jweixin-module.js')
const debug = false; // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
/**
 * 微信公众号h5支持方法封装
 */
const payRequest = (payData, callback, failCallback) => {
	
	jweixin.config({
		debug: debug, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		appId: payData.appId, // 必填，公众号的唯一标识
		timestamp: payData.timeStamp, // 必填，生成签名的时间戳
		nonceStr: payData.nonceStr, // 必填，生成签名的随机串
		signature: payData.paySign, // 必填，签名，见附录1
		jsApiList: ['chooseWXPay'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
	});
	jweixin.ready(function() {
		jweixin.chooseWXPay({
			timestamp: payData.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
			nonceStr: payData.nonceStr, // 支付签名随机串，不长于 32 位
			package: payData.packageValue, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
			signType: payData.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
			paySign: payData.paySign, // 支付签名
			success: function(res) {
				// 支付成功后的回调函数
				callback(res);
			},
			cancel: function(res) {
				// cancelCallback(res);
			},
			fail: function(res) {
				failCallback(res);
			}
		});
	});

	jweixin.error(function(res) {
		console.log('error')
		console.log(res)
		uni.showToast({
			icon: 'none',
			title: '支付失败了',
			duration: 4000
		});
		// config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
		/*alert("config信息验证失败");*/
	});
}

// 分享链接给微信朋友,
const shareWxFriend = (configObj, shareObj, callback, failCallback)=>{
	console.log("config",configObj)
	//注意：config 用当前url和签名的url 比较 需要保持一致
	//1.config之前 让当前url和签名url保持一致
	//2.重新config 
	jweixin.config({
		debug: debug, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
		appId: configObj.appId, // 必填，公众号的唯一标识
		timestamp: configObj.timestamp, // 必填，生成签名的时间戳
		nonceStr: configObj.nonceStr, // 必填，生成签名的随机串
		signature: configObj.signature, // 必填，签名，见附录1
		jsApiList: ['updateTimelineShareData','updateAppMessageShareData'], // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
	});
	jweixin.error(function(res){
	    // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
		console.log("config,error",res)
	});
	// 检测客户端能否使用接口
	// jweixin.checkJsApi({
	//   jsApiList: [
	//   'updateAppMessageShareData',
	//   'updateTimelineShareData',
	//   'onMenuShareTimeline',
	//   'onMenuShareAppMessage',
	//   'onMenuShareQQ',
	//   'onMenuShareWeibo',
	//   'onMenuShareQZone',
	//   'startRecord',
	//   'stopRecord',
	//   'onVoiceRecordEnd',
	//   'playVoice',
	//   'pauseVoice',
	//   'onVoicePlayEnd',
	//   'uploadVoice',
	//   'downloadVoice',
	//   'uploadImage',
	//   'downloadImage',
	//   'translateVoice',
	//   'getNetworkType',
	//   'getLocation',
	//   'hideOptionMenu',
	//   'showOptionMenu',
	//   'hideMenuItems',
	//   'showMenuItems',
	//   'hideAllNonBaseMenuItem',
	//   'showAllNonBaseMenuItem',
	//   'closeWindow',
	//   'scanQRCode',
	//   'chooseWXPay',
	//   'openProductSpecificView',
	//   'addCard',
	//   'chooseCard',
	//   'openCard'], // 需要检测的JS接口列表，所有JS接口列表见附录2,
	//   success: function(res) {
	// 	console.log("checkJsApi",res)
	//   }
	// });
	
	// console.log("分享链接给微信朋友",configObj,shareObj)
	jweixin.ready(function(res) {
		//自定义“分享给朋友”及“分享到QQ”按钮的分享内容（1.4.0）
		jweixin.updateAppMessageShareData({
			title: shareObj.title, // 分享标题
			desc: shareObj.desc, // 分享描述
			link: shareObj.link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
			imgUrl: shareObj.imgUrl, // 分享图标
			success: function (res) {
				// console.log("updateAppMessageShareData更新分享信息",res)
				callback();
			},
			fail: function (res) {
				// console.log("updateAppMessageShareData,fail",res)
			}
		})
		//自定义“分享到朋友圈”及“分享到QQ空间”按钮的分享内容（1.4.0）
		jweixin.updateTimelineShareData({
			title: shareObj.title, // 分享标题
			desc: shareObj.desc, // 分享描述
			link: shareObj.link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
			imgUrl: shareObj.imgUrl, // 分享图标
			success: function (res) {
				// console.log("updateTimelineShareData更新分享信息",res)
				callback();
			},
			fail: function (res) {
				// console.log("updateTimelineShareData,fail",res)
			}
		})
	});

	


}

module.exports = {
	payRequest: payRequest,
	shareWxFriend: shareWxFriend,
};
// #endif
