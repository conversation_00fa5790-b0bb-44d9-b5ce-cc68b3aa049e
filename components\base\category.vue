<template>
	<!-- 商品分类组件 -->
	<view class="categoryComponent">
		<!-- 横向分类导航栏 -->
		<view class="cu-bar" :style="{backgroundColor: newData.background}"
			:class="newData.background&&newData.background.indexOf('bg-')!=-1?newData.background:''"
			style="min-height: 60rpx;" v-if="newData.navStyle != 1">
			<view style="width: 90%;">
				<scroll-view :style="{fontSize:`${newData.fontSize}px`,color:newData.fontColor}" scroll-x
					class="nav text-white text-sm" scroll-with-animation :scroll-left="scrollLeft">
					<view v-if="newData.showType==0" class="cu-item"
						:style="index==TabCur ? {fontSize:`${newData.selectedFontSize}px`,color:newData.selectedFontColor}:''"
						:class="index==TabCur ? 'cur text-bold text-white text-lg' : ''"
						v-for="(item,index) in newData.initCategoryList" :key="index" @tap="tabSelect(item,index)"
						:data-index="index">
						{{item.name}}
					</view>
					<view v-if="newData.showType==1" class="cu-item"
						:style="index==TabCur ? {fontSize:`${newData.selectedFontSize}px`,color:newData.selectedFontColor}:''"
						:class="index==TabCur ? 'cur text-bold text-white text-lg' : ''"
						v-for="(item,index) in newData.categoryList" :key="index" @tap="tabSelect(item,index)"
						:data-index="index">
						{{item.name}}
					</view>
				</scroll-view>
			</view>
			<view class="action">
				<view :class="newData.titleIcon" :style="{fontSize:`${newData.fontSize}px`,color:newData.fontColor}">
				</view>
			</view>
		</view>
		
		<!-- 竖向分类导航布局 -->
		<view class="flex category-vertical-layout" v-if="newData.navStyle ==1">
			<!-- 左侧垂直导航栏 -->
			<view class="vertical-nav-wrapper" :style="{width: `${newData.verticalNavWidth || 180}rpx`}">
				<view class="vertical-nav" :style="{backgroundColor: newData.background, width: `${newData.verticalNavWidth || 180}rpx`}">
					<scroll-view scroll-y style="height: 100%;">
						<view v-if="newData.showType==0" 
							class="vertical-nav-item" 
							:style="[
								index==TabCur ? 
									{fontSize:`${newData.selectedFontSize}px`,color:newData.selectedFontColor, backgroundColor: newData.selectedBackgroundColor || 'rgba(255,255,255,0.2)'} : 
									{fontSize:`${newData.fontSize}px`,color:newData.fontColor},
								(index==TabCur && item.selectedImgUrl) ? {
									backgroundImage: `url(${item.selectedImgUrl})`,
									backgroundSize: item.backgroundSize || 'cover',
									backgroundPosition: item.backgroundPosition || 'center',
									backgroundRepeat: item.backgroundRepeat || 'no-repeat'
								} : (item.imgUrl ? {
									backgroundImage: `url(${item.imgUrl})`,
									backgroundSize: item.backgroundSize || 'cover',
									backgroundPosition: item.backgroundPosition || 'center',
									backgroundRepeat: item.backgroundRepeat || 'no-repeat'
								} : {})
							]"
							:class="index==TabCur ? 'vertical-active' : ''"
							v-for="(item,index) in newData.initCategoryList" 
							:key="index" 
							@tap="tabSelect(item,index)"
							:data-index="index">
							{{item.name}}
						</view>
						<view v-if="newData.showType==1" 
							class="vertical-nav-item"
							:style="[
								index==TabCur ? 
									{fontSize:`${newData.selectedFontSize}px`,color:newData.selectedFontColor, backgroundColor: newData.selectedBackgroundColor || 'rgba(255,255,255,0.2)'} : 
									{fontSize:`${newData.fontSize}px`,color:newData.fontColor},
								(index==TabCur && item.selectedImgUrl) ? {
									backgroundImage: `url(${item.selectedImgUrl})`,
									backgroundSize: item.backgroundSize || 'cover',
									backgroundPosition: item.backgroundPosition || 'center',
									backgroundRepeat: item.backgroundRepeat || 'no-repeat'
								} : (item.imgUrl ? {
									backgroundImage: `url(${item.imgUrl})`,
									backgroundSize: item.backgroundSize || 'cover',
									backgroundPosition: item.backgroundPosition || 'center',
									backgroundRepeat: item.backgroundRepeat || 'no-repeat'
								} : {})
							]"
							:class="index==TabCur ? 'vertical-active' : ''"
							v-for="(item,index) in newData.categoryList" 
							:key="index" 
							@tap="tabSelect(item,index)"
							:data-index="index">
							{{item.name}}
						</view>
					</scroll-view>
				</view>
			</view>
			
			<!-- 右侧内容区域 -->
			<view class="vertical-content" style="flex: 1;" >
				<scroll-view scroll-y style="height: auto; min-height: 300rpx;">
					<!--内容图片单列-->
					<view v-if="newData.rowRules ==0 " v-for="(goods,index) in goodsList">
						<view v-for="item in goods.coverUrls" class="goods_cover">
							<view v-if="item.sizeType==newData.imgShowSize"
								:style="{paddingTop:`${newData.topSpacing}px`,paddingBottom:`${newData.bottomSpacing}px`,paddingLeft:`${newData.leftSpacing}px`,paddingRight:`${newData.rightSpacing}px`}">
								<div-base-navigator :isSystemUrl="getIsSystemUrl()" :pageUrl="getPageUrl(goods)">
									<view @touchstart="startRun(index)" @touchend="endRun(index)">
										<view style="position: relative;">
											<uni-transition v-if="newData.animation==1" class="image_transition" ref="ani" :styles="{
													'width':'100%',
													'height':'100%',
													'backgroundColor': 'rgba(0, 0, 0, 0.5)'
												}" :show="true">
												<P v-if="newData.nameFlag" class="animation_font">{{goods.name}}</P>
											</uni-transition>
											<image :src="item.url" style="width: 100%;display:block" mode="widthFix">
											</image>
										</view>
										<view v-if="newData.animation==0 && newData.nameFlag " class="text-center text-cut">
											{{goods.name}}</view>
									</view>
								</div-base-navigator>
							</view>
						</view>
					</view>
					
					<!--内容图片  双列-->
					<view class="flex align-center justify-center " :style="{paddingTop:`${newData.topSpacing}px`,
						paddingBottom:`${newData.bottomSpacing}px`,
						paddingLeft:`${newData.leftSpacing}px`,
						paddingRight:`${newData.rightSpacing}px`}" v-if="newData.rowRules==1" v-for="(rowItem,rowIndex) in rowList">
						<view v-for="(colItem,colIndex) in rowItem" class="goods_cover" :style="intervalStyle[colIndex]"
							style="width: 50%;">
							<view v-if="item" v-for=" item in colItem.coverUrls">
								<view v-if="item && item.sizeType==newData.imgShowSize">
									<div-base-navigator :isSystemUrl="getIsSystemUrl()" :pageUrl="getPageUrl(colItem)">
										<view @touchstart="startRun(rowIndex,colIndex)" @touchend="endRun(rowIndex,colIndex)">
											<view style="position: relative;">
												<uni-transition :key="getTransitionKey(rowIndex, colIndex)" v-if="newData.animation==1"
													class="image_transition" :ref="getTransitionKey(rowIndex, colIndex)" :styles="{
													'width':'100%',
													'height':'100%',
													'backgroundColor': 'rgba(0, 0, 0, 0.5)'
													}" :show="true">
													<P v-if=" newData.nameFlag " style="color:white;opacity: 1;"
														class="animation_font">{{colItem.name}}</P>
												</uni-transition>
												<image :src="item.url" style="width: 100%;display: block;" mode="widthFix">
												</image>
											</view>
											<view v-if="newData.animation==0 && newData.nameFlag " class="text-center text-cut">
												{{colItem.name}}</view>
										</view>
									</div-base-navigator>
								</view>
							</view>
						</view>
					</view>
					
					<!-- 加载状态指示器（垂直布局） -->
					<view v-if="newData.loadingType == 2 && ((loading || scrollLoading) || (page.currentPage < page.pages && page.pages > 1))" class="loading-container padding-vertical">
						<view class="cu-load loading"></view>
					</view>
					
					<!-- 全部加载完毕提示（垂直布局） -->
					<view v-if="newData.loadingType == 2 && !loading && !scrollLoading && goodsList.length > 0 && page.currentPage >= page.pages" class="loading-container padding-vertical">
						<view class="loading-text">— 已加载全部内容 —</view>
					</view>
					
					<!-- 分页组件（垂直布局） -->
					<view v-if="this.newData.loadingType == 1" class="bottom_page padding-top-sm padding-bottom-sm text-bold">
						<uni-pagination ref="pagination" title="标题文字" :total="page.total" :pageSize="page.pageSize"
							v-model="page.currentPage" @change="change" :showAround="true">
						</uni-pagination>
					</view>
				</scroll-view>
			</view>
		</view>
		
		<!-- 非竖直布局的内容区域 -->
		<view v-if="newData.navStyle != 1"  class="content-container">
			<scroll-view scroll-y style="height: auto; min-height: 300rpx;">
				<!--内容图片单列-->
				<view v-if="newData.rowRules ==0 " v-for="(goods,index) in goodsList">
					<view v-for="item in goods.coverUrls" class="goods_cover">
						<view v-if="item.sizeType==newData.imgShowSize"
							:style="{paddingTop:`${newData.topSpacing}px`,paddingBottom:`${newData.bottomSpacing}px`,paddingLeft:`${newData.leftSpacing}px`,paddingRight:`${newData.rightSpacing}px`}">
							<div-base-navigator :isSystemUrl="getIsSystemUrl()" :pageUrl="getPageUrl(goods)">
								<view @touchstart="startRun(index)" @touchend="endRun(index)">
									<view style="position: relative;">
										<uni-transition v-if="newData.animation==1" class="image_transition" ref="ani" :styles="{
												'width':'100%',
												'height':'100%',
												'backgroundColor': 'rgba(0, 0, 0, 0.5)'
											}" :show="true">
											<P v-if="newData.nameFlag" class="animation_font">{{goods.name}}</P>
										</uni-transition>
										<image :src="item.url" style="width: 100%;display:block" mode="widthFix">
										</image>
									</view>
									<view v-if="newData.animation==0 && newData.nameFlag " class="text-center text-cut">
										{{goods.name}}</view>
								</view>
							</div-base-navigator>
						</view>
					</view>
				</view>
				<!--内容图片  双列-->
				<view class="flex align-center justify-center " :style="{paddingTop:`${newData.topSpacing}px`,
					paddingBottom:`${newData.bottomSpacing}px`,
					paddingLeft:`${newData.leftSpacing}px`,
					paddingRight:`${newData.rightSpacing}px`}" v-if="newData.rowRules==1" v-for="(rowItem,rowIndex) in rowList">
					<view v-for="(colItem,colIndex) in rowItem" class="goods_cover" :style="intervalStyle[colIndex]"
						style="width: 50%;">
						<view v-if="item" v-for=" item in colItem.coverUrls">
							<view v-if="item && item.sizeType==newData.imgShowSize">
								<div-base-navigator :isSystemUrl="getIsSystemUrl()" :pageUrl="getPageUrl(colItem)">
									<view @touchstart="startRun(rowIndex,colIndex)" @touchend="endRun(rowIndex,colIndex)">
										<view style="position: relative;">
											<uni-transition :key="getTransitionKey(rowIndex, colIndex)" v-if="newData.animation==1"
												class="image_transition" :ref="getTransitionKey(rowIndex, colIndex)" :styles="{
												'width':'100%',
												'height':'100%',
												'backgroundColor': 'rgba(0, 0, 0, 0.5)'
												}" :show="true">
												<P v-if=" newData.nameFlag " style="color:white;opacity: 1;"
													class="animation_font">{{colItem.name}}</P>
											</uni-transition>
											<image :src="item.url" style="width: 100%;display: block;" mode="widthFix">
											</image>
										</view>
										<view v-if="newData.animation==0 && newData.nameFlag " class="text-center text-cut">
											{{colItem.name}}</view>
									</view>
								</div-base-navigator>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 加载状态指示器（水平布局） -->
				<view v-if="newData.loadingType == 2 && ((loading || scrollLoading) || (page.currentPage < page.pages && page.pages > 1))" class="loading-container padding-vertical">
					<view class="cu-load loading"></view>
				</view>
				
				<!-- 全部加载完毕提示（水平布局） -->
				<view v-if="newData.loadingType == 2 && !loading && !scrollLoading && goodsList.length > 0 && page.currentPage >= page.pages" class="loading-container padding-vertical">
					<view class="loading-text">— 已加载全部内容 —</view>
				</view>
				
				<!-- 分页组件（水平布局） -->
				<view v-if="this.newData.loadingType == 1" class="bottom_page padding-top-sm padding-bottom-sm text-bold">
					<uni-pagination ref="pagination" title="标题文字" :total="page.total" :pageSize="page.pageSize"
						v-model="page.currentPage" @change="change" :showAround="true">
					</uni-pagination>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
	import baseMask from '@/components/base-mask/index.vue'
	export default {
		components: {
			divBaseNavigator,
			baseMask,
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						verticalNavWidth: 180, // 纵向导航的宽度，默认180rpx
						containerHeight: 500, // 内容区域高度，默认500rpx
					}
				}
			},
			scrollTop:{
				type: Number,
			}
		},
		watch: {
			scrollTop(newVal,oldVal){
				// console.log("1111",this.scrollTop)
				this.scrollLoad(newVal);
			},
			'newData.interval': {
				handler(newVal) {
					this.updateIntervalStyle();
				},
				immediate: true
			}
		},
		created() {
			//页面加载时取得屏幕高度
			this.screenHeight = uni.getSystemInfoSync().screenHeight;
			// 监听窗口大小变化
			uni.onWindowResize(this.handleResize);
		},
		mounted() {
			this.loadPage()
			// 初始化间隔样式
			this.updateIntervalStyle();
			setTimeout(()=> {
				let query = uni.createSelectorQuery().in(this);
				query.select('.categoryComponent').boundingClientRect(data => {
					// console.log("得到布局位置信息" + JSON.stringify(data));
					// console.log("节点离页面顶部的距离为" + data.top);
					// console.log("节点高度为",data.height)
					this.componentTop = data.top
				}).exec();
			}, 500);
		},
		beforeDestroy() {
			// 移除窗口大小变化监听
			uni.offWindowResize(this.handleResize);
		},
		data() {
			return {
				newData: this.value, //传入数据
				TabCur: 0,
				selectTab: {}, //选中的类别
				scrollTopTemp: '', //父级页面传入的滚动高度
				className: 'goods_cover',
				scrollLeft: 0,
				queryParams:{},//查询参数
				intervalStyle: [
					{ paddingRight: '0px' },
					{ paddingLeft: '0px' }
				],
				page: {
					total: 1, // 总条数
					pages: 1, // 总页数
					currentPage: 1, // 当前页数
					pageSize: 10, // 每页显示多少条
					ascs: [], //升序字段
					descs: [] //降序字段
				},
				goodsList: [], //产品
				rowList: [], //行数
				categoryList: [], //类别list
				componentTop: 0, //组件距离顶部高度
				componentHeight: 0, //组件高度
				screenHeight: 0, //屏幕高度
				scrollLoading: false,  //防止下滑加载频繁触发
				bottomDistinct: 200, //距离底部多少像素时触发，增加为200，让加载更早触发
				loading: false, // 初始加载状态
        isLogin:true
			};
		},
		methods: {
			// 更新间隔样式
			updateIntervalStyle() {
				this.intervalStyle = [
					{ paddingRight: this.newData.interval + 'px' },
					{ paddingLeft: this.newData.interval + 'px' }
				];
			},
			//初始化
			loadPage() {
        if(!uni.getStorageSync('third_session')){
          this.isLogin=false
        }
				if (this.newData.showType == 0) { //分类拿取
					this.selectTab = this.newData.initCategoryList[0];
					this.getGoodsByTagType(this.newData.initCategoryList[0],this.isLogin);
				} else if (this.newData.showType == 1) { //标签拿取
					this.selectTab = this.newData.categoryList[0];
					this.getGoodsByTag(this.newData.categoryList[0],this.isLogin);
				}
			},
			tabSelect(item, index) {
				if (this.TabCur == index) {
					return;
				}
				this.selectTab = item;
				this.page.currentPage = 1;
				// this.$refs.pagination.currentPage = 1;
				this.loading = true; // 切换分类时显示加载状态
				if (this.newData.showType == 0) {
					this.getGoodsByTagType(item,this.isLogin);
				} else if (this.newData.showType == 1) {
					this.getGoodsByTag(item,this.isLogin);
				}
				this.TabCur = index;
				this.scrollLoading = false;
			},
			//根据大分类拿取商品
			getGoodsByTagType(item,isLogin=true) {
				if (this.newData.showType != 0) {
					return;
				}
				if (!item) {
					return
				}
				
				this.loading = true; // 显示加载状态
				
				let sortId = ""
				if (item.sortRule && item.sortRule.id) {
					sortId = item.sortRule.id
				} else if (this.newData.sortRule && this.newData.sortRule.id) {
					sortId = this.newData.sortRule.id
				}
				this.queryParams = {
					total: this.page.total,
					current: this.page.currentPage,
					size: this.page.pageSize,
					descs: this.page.descs,
					ascs: this.page.ascs,
					appId: app.globalData.appId,
					sortId: sortId,
					tagTypeId: item.id,
				}

				// console.log("请求参数", obj)

        // console.log("参数", this.queryParams)
        let getGoodsByTagPromise
        if (isLogin){
          getGoodsByTagPromise=api.goodsCoverByType(this.queryParams)
        }else{
          getGoodsByTagPromise=api.goodsCoverByTypeFreedom(this.queryParams)

        }
        getGoodsByTagPromise.then(res => {
					// console.log("goodsList",res)
					this.page.total = res.data.total
					this.page.currentPage = res.data.current
					this.page.pageSize = res.data.size
					this.goodsList = res.data.records;
					this.page.pages = res.data.pages
					this.rowList = [];
					for (var i = 0; i < this.goodsList.length; i++) {
						this.goodsList[i].coverUrls = JSON.parse(this.goodsList[i].coverUrls)
					}
					if (this.goodsList.length > 0 && this.newData.rowRules == 1) {
						this.rowList = this.calcuRow(this.goodsList);
					}
					
					this.loading = false; // 加载完成，隐藏加载状态
	
				}).catch(err => {
					console.log(err);
					this.loading = false; // 加载出错也要隐藏加载状态
				});
			},
			/**
			 * 根据标签拿取商品
			 * @param {Object} item 传入的选中下标
			 */
			getGoodsByTag(item,isLogin=true) {
				if (this.newData.showType != 1) {
					return;
				}
				if (!item) {
					return
				}
				
				this.loading = true; // 显示加载状态
				
				let sortId = ""
				if (item.sortRule && item.sortRule.id) {
					sortId = item.sortRule.id
				} else if (this.newData.sortRule && this.newData.sortRule.id) {
					sortId = this.newData.sortRule.id
				}
				let tagIdList = [];
				for (let i = 0; i < item.tagList.length; i++) {
					tagIdList.push(item.tagList[i].id)
				}
				
				// 创建参数对象
				let params = {
					searchType: "2",
				}
				
				// 如果tagIdList有值，则转换为逗号分隔的字符串
				if (tagIdList.length > 0) {
					params.tagIdList = tagIdList.join(',');
				}
				
				// 只有当descs不是空数组时才添加到params
				if (this.page.descs && this.page.descs.length > 0) {
					params.descs = this.page.descs;
				}else{
				}
				
				// 只有当ascs不是空数组时才添加到params
				if (this.page.ascs && this.page.ascs.length > 0) {
					params.ascs = this.page.ascs;
				}
				
				this.rowList = [];
				console.log(this.page.descs, this.page.ascs, tagIdList.length > 0 ? tagIdList.join(',') : null);
				
				this.queryParams = Object.assign({
					total: this.page.total,
					current: this.page.currentPage,
					size: this.page.pageSize,
					sortId: sortId,
				}, params);
				
				// console.log("参数", this.queryParams)
        let getGoodsByTagPromise
        if (isLogin){
          getGoodsByTagPromise=api.goodsByTag(this.queryParams)
        }else{
          getGoodsByTagPromise=api.goodsByTagFreedom(this.queryParams)

        }
        getGoodsByTagPromise.then(res => {
					// console.log("res", res)
					this.page.total = res.data.total
					this.page.currentPage = res.data.current
					this.page.pageSize = res.data.size
					this.goodsList = res.data.records;
					this.page.pages = res.data.pages
					// console.log("goodsList", this.goodsList)
					//转换json格式
					for (var i = 0; i < this.goodsList.length; i++) {
						this.goodsList[i].coverUrls = JSON.parse(this.goodsList[i].coverUrls)
					}
					//计算行数
					if (this.goodsList.length > 0 && this.newData.rowRules == 1) {
						this.rowList = this.calcuRow(this.goodsList);
					}
					
					this.loading = false; // 加载完成，隐藏加载状态
					
				}).catch(err => {
					console.log(err);
					this.loading = false; // 加载出错也要隐藏加载状态
				});
			},
			//双列图片时计算行数
			calcuRow(list) {
				if (!list || list.length < 1) {
					return
				}
				let row = [];
				let col = []
				// console.log("长度",list.length)
				for (let i = 0; i < list.length; i++) {
					col.push(list[i])

					if ((i != 0 && i % 2 != 0) || i == list.length - 1) {
						row.push(col)
						col = [];
					}

				}
				//查询数为奇数   让尾部添加一个
				if (list.length % 2 != 0) {
					row[row.length - 1].push({});
				}
				return row;
			},
			//开始动画
			startRun(index1, index2) {
				// console.log("动画开始",index1,index2)
				if (this.newData.animation == 0) {
					return;
				}
				if (this.newData.rowRules == 0) {
					this.$refs.ani[index1].step({
						'top': '0',
						'opacity': '0',
						'backgroundColor': 'rgba(0, 0, 0, 0)'
					}, {
						timingFunction: 'linear',
					})
					// 开始执行动画
					this.$nextTick(() => {
						this.$refs.ani[index1].run(() => {})
					});
				} else if (this.newData.rowRules == 1) {
					const refKey = this.getTransitionKey(index1, index2);
					this.$refs[refKey][0].step({
						'top': '0',
						'opacity': '0',
						'backgroundColor': 'rgba(0, 0, 0, 0)'
					}, {
						timingFunction: 'linear',
					})
					this.$nextTick(() => {
						this.$refs[refKey][0].run(() => {})
					});
				}
			},
			//结束动画
			endRun(index1, index2) {
				if (this.newData.animation == 0) {
					return;
				}
				// 开始执行动画
				if (this.newData.rowRules == 0) {
					this.$refs.ani[index1].step({
						'width': '100%',
						'height': '100%',
						'opacity': '1',
						'backgroundColor': 'rgba(0, 0, 0, 0.5)'
					})
					// 开始执行动画
					this.$nextTick(() => {
						this.$refs.ani[index1].run(() => {})
					});
				} else if (this.newData.rowRules == 1) {
					const refKey = this.getTransitionKey(index1, index2);
					this.$refs[refKey][0].step({
						'width': '100%',
						'height': '100%',
						'opacity': '1',
						'backgroundColor': 'rgba(0, 0, 0, 0.5)'
					})
					this.$nextTick(() => {
						this.$refs[refKey][0].run(() => {})
					});
				}
			},
			//双列图片得到间隔
			getInterval(index) {
				if (index == 0) {
					return {
						paddingRight: this.newData.interval + 'px'
					}
				} else {
					return {
						paddingLeft: this.newData.interval + 'px'
					}
				}
			},
			// 添加新的兼容小程序的方法
			getTransitionKey(rowIndex, colIndex) {
				return 'transition-' + rowIndex + '-' + colIndex;
			},
			//得到正确 跳转类型
			getIsSystemUrl(item) {
				if (this.newData.showType == 0) {
					let obj = this.newData.initCategoryList[this.TabCur]
					return obj.isSystemUrl;
				} else if (this.newData.showType == 1) {
					let obj = this.newData.categoryList[this.TabCur]
					return obj.isSystemUrl;
				}
			},
			//得到正确的url和跳转
			getPageUrl(item) {
				if (this.newData.showType == 0) {
					let obj = this.newData.initCategoryList[this.TabCur]
					return obj.pageUrl + '&goods_id=' + item.id;
				} else if (this.newData.showType == 1) {
					let obj = this.newData.categoryList[this.TabCur]
					return obj.pageUrl + '&goods_id=' + item.id;
				}
			},
			change(obj) {
				// console.log("change",obj)
				// console.log("当前的",this.selectTab)
				this.page.currentPage = obj.current
				if (this.newData.showType == 0) {
					this.getGoodsByTagType(this.selectTab);
				} else if (this.newData.showType == 1) {
					this.getGoodsByTag(this.selectTab);
				}
			},
			getQueryParams(obj){
				obj.total =  this.page.total;
				obj.current =  this.page.currentPage;
				obj.size =  this.page.pageSize;
				
				// 只有当descs不是空数组时才添加到参数
				if (this.page.descs && this.page.descs.length > 0) {
					obj.descs = this.page.descs;
				} else if (obj.descs && obj.descs.length === 0) {
					// 如果descs是空数组，则从对象中删除该属性
					delete obj.descs;
				}
				
				// 只有当ascs不是空数组时才添加到参数
				if (this.page.ascs && this.page.ascs.length > 0) {
					obj.ascs = this.page.ascs;
				} else if (obj.ascs && obj.ascs.length === 0) {
					// 如果ascs是空数组，则从对象中删除该属性
					delete obj.ascs;
				}
				
				// 处理tagIdList，如果存在且长度大于0，则转换为逗号分隔的字符串
				if (obj.tagIdList && Array.isArray(obj.tagIdList) && obj.tagIdList.length > 0) {
					obj.tagIdList = obj.tagIdList.join(',');
				}
				
				return obj;
			},
			/**
			 *  页面滑动事件
			 */
			scrollLoad(e) {
				// console.log("this.page",this.page)
				if(this.newData.loadingType != 2){
					return;
				}
				if(this.scrollLoading){
					return
				}
				if(this.page.currentPage >= this.page.pages){
					return
				}
				
				// 预先设置scrollLoading为true，以便立即显示加载状态
				// 在进行触底检测前先设置加载状态，这样用户体验会更好
				
				let query = uni.createSelectorQuery().in(this);
				query.select('.categoryComponent').boundingClientRect(data => {
					this.componentHeight = data.height
					
					//如果设置的事件触发距离 大于等于 (节点的高度-屏幕高度-滚动条到顶部的距离)
					if (this.bottomDistinct >= (this.componentTop+this.componentHeight) - this.screenHeight - this.scrollTop) {
						// 立即设置加载状态为true
						this.scrollLoading = true;
						// 添加一个小延迟，确保UI更新后再加载数据
						setTimeout(() => {
							this.loadMore();
						}, 50);
					}
				}).exec();
			},
			loadMore(){
				this.page.currentPage = this.page.currentPage+1;
				let obj =  this.getQueryParams(this.queryParams)
				// console.log("this.queryParams",obj)

				if (this.newData.showType == 0) { //分类拿取
          let goodsByTagPromise

          if (this.isLogin){
            goodsByTagPromise=api.goodsCoverByType(obj)
          }else{
            goodsByTagPromise=api.goodsCoverByTypeFreedom(obj)
          }
          goodsByTagPromise.then(res => {
						// console.log("this.res",res)
						this.page.total = res.data.total
						this.page.pages = res.data.pages
						this.page.pageSize = res.data.size
						let goodsList = res.data.records;
						for (let i = 0; i < goodsList.length; i++) {
							goodsList[i].coverUrls = JSON.parse(goodsList[i].coverUrls)
						}
						this.goodsList = this.goodsList.concat(goodsList);
						if (goodsList.length > 0 && this.newData.rowRules == 1) {
							this.rowList = this.rowList.concat(this.calcuRow(goodsList));
						}
						if(this.page.currentPage<this.page.pages){
							this.scrollLoading = false;
						} else {
							// 已加载全部内容
							this.scrollLoading = false;
						}
						
					}).catch(err => {
						console.log(err);
						this.scrollLoading = false; // 加载出错也要重置加载状态
					});
				} else if (this.newData.showType == 1) { //标签拿取
          let goodsByTagPromise

          if (this.isLogin){
            goodsByTagPromise=api.goodsByTag(obj)
          }else{
            goodsByTagPromise=api.goodsByTagFreedom(obj)
          }
          goodsByTagPromise.then(res => {
						this.page.total = res.data.total
						this.page.pageSize = res.data.size
						this.page.pages = res.data.pages
						let goodsList = res.data.records;
						//转换json格式
						for (let i = 0; i < goodsList.length; i++) {
							goodsList[i].coverUrls = JSON.parse(goodsList[i].coverUrls)
						}
						this.goodsList = this.goodsList.concat(goodsList);
						//计算行数
						if (goodsList.length > 0 && this.newData.rowRules == 1) {
							this.rowList = this.rowList.concat(this.calcuRow(goodsList));
						}
						if(this.page.currentPage<this.page.pages){
							this.scrollLoading = false;
						} else {
							// 已加载全部内容
							this.scrollLoading = false;
						}
					}).catch(err => {
						console.log(err);
						this.scrollLoading = false; // 加载出错也要重置加载状态
					});
				}
			},
			handleResize() {
				// 处理窗口大小变化的逻辑
				this.screenHeight = uni.getSystemInfoSync().screenHeight;
				// 重新计算组件位置
				let query = uni.createSelectorQuery().in(this);
				query.select('.categoryComponent').boundingClientRect(data => {
					if (data) {
						this.componentTop = data.top;
					}
				}).exec();
			}
		}
	}
</script>

<style scoped lang="scss">
	.img-category {
		width: 100rpx;
		height: 100rpx;
	}

	.img-category-banner {
		width: 100%;
		height: 180rpx;

	}

	.goods_cover {
		animation: hide 1.5s;
	}

	@keyframes hide {
		from {
			opacity: 0;
			top: 100px;
			transform: translateY(10%)
		}

		to {
			opacity: 1;
			top: -100px;
			transform: translateY(00%)
		}
	}

	.image_transition {
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		color: white;
		top: 0;
		z-index: 1;
	}

	.bottom_page {
		font-size: 16px;
	}
	
	/* 垂直导航布局容器 */
	.category-vertical-layout {
		position: relative;
		width: 100%;
		height: auto;
		display: flex;
	}
	
	/* 垂直导航外层容器 */
	.vertical-nav-wrapper {
		position: relative;
	}
	
	/* 垂直导航样式 */
	.vertical-nav {
		position: sticky;
		top: 0;
		min-height: 100vh; /* 最大高度为视口高度 */
		box-sizing: border-box;
		overflow-y: auto;
		z-index: 10;
	}
	
	.vertical-nav-item {
		padding: 20rpx 10rpx;
		text-align: center;
		border-bottom: 1px solid rgba(255,255,255,0.1);
		position: relative;  /* 添加相对定位，支持背景图片定位 */
		background-size: cover;
		background-position: center;
		background-repeat: no-repeat;
		transition: all 0.3s ease; /* 添加过渡效果 */
	}
	
	.vertical-active {
		position: relative;
		font-weight: 500;
		box-shadow: 0 0 6rpx rgba(0,0,0,0.1); /* 添加轻微阴影效果 */
		z-index: 1; /* 确保活动项在顶层 */
	}
	
	.vertical-content {
		padding-left: 10rpx;
		overflow: hidden;
		flex: 1;
	}
	
	.content-container {
		overflow: hidden;
	}
	
	/* 加载状态样式 */
	.loading-container {
		width: 100%;
		text-align: center;
		padding: 20rpx 0;
		background-color: rgba(255, 255, 255, 0.8);  /* 添加半透明背景 */
		border-radius: 10rpx;                        /* 圆角边框 */
		margin-top: 10rpx;                           /* 顶部间距 */
		margin-bottom: 10rpx;                        /* 底部间距 */
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1); /* 添加轻微阴影 */
		transition: all 0.3s ease;                   /* 平滑过渡效果 */
	}
	
	.loading-text {
		font-size: 24rpx;
		color: #666;                                /* 更深的颜色 */
		margin-top: 10rpx;
		font-weight: 500;                           /* 稍微加粗 */
	}
	
	.cu-load.loading::before {
		margin-right: 10rpx;
		animation: loading-rotate 1s linear infinite; /* 更明显的动画 */
	}
	
	@keyframes loading-rotate {
		from { transform: rotate(0deg); }
		to { transform: rotate(360deg); }
	}
</style>
