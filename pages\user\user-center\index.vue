<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="false">
			<block slot="backText">返回</block>
			<block slot="content">个人中心</block>
		</cu-custom>

		<view class="cu-list menu-avatar" :class="'bg-'+theme.backgroundColor">
			<view class="message">
				<view class="setting flex justify-end margin-right">
					<navigator url="/pages/user/user-info/index" hover-class="none" class="text-xl margin-top">
						<text class="cuIcon-settingsfill text-white"></text>
					</navigator>
				</view>
				<view class="cu-item personal-information">
					<view class="cu-avatar round xl head flex"
						:style="userInfo.headimgUrl?'background-image:url(' + userInfo.headimgUrl + ')':''">
						{{!userInfo.headimgUrl ? '头' : ''}}</view>
					<view class="content text-center margin-top-xs">
						<view class="text-white text-bold text-xl margin-top-xs" v-if="userInfo.nickName">
							{{userInfo.nickName}}</view>
						<!--  #ifdef  MP-WEIXIN -->
						<button class="cu-btn round sm line-white update margin-top-xs" v-if="canIUseGetUserProfile"
							@click="getUserProfile">更新昵称</button>
						<button class="cu-btn round sm line-white update margin-top-xs" v-else open-type="getUserInfo"
							@getuserinfo="agreeGetUser" lang="zh_CN">更新昵称</button>
						<!--  #endif -->
						<!--  #ifdef  H5 -->
						<button class="cu-btn round sm line-white update margin-top-xs"
							@click="updateUserInfo">更新昵称</button>
						<!--  #endif -->
						<view class="text-white sm margin-top-xs" v-if="userInfo.phone">{{userInfo.phone}}</view>
					</view>
				</view>
			</view>
			<view class="account">
				<view class="padding flex text-center text-white" v-if="userInfo">
					<view class="flex flex-sub flex-direction">
						<view class="text-xxl text-white text-bold">{{userInfo.userCode}}</view>
						<view class="text-sm text-white">
							<text class="cuIcon-barcode"></text>
							<text class="margin-left-xs">{{userInfo.userGrade == '0' ? '用户' : '会员'}}编号</text>
						</view>
					</view>
					<navigator class="flex flex-sub flex-direction" url="/pages/user/user-points-record/index"
						hover-class="none">
						<view class="text-xxl text-white text-bold">{{userInfo.pointsCurrent}}</view>
						<view class="text-sm text-white">
							<text class="cuIcon-medal"></text>
							<text class="margin-left-xs">当前积分</text>
						</view>
					</navigator>
					<navigator class="flex flex-sub flex-direction" url="/pages/coupon/coupon-user-list/index"
						hover-class="none">
						<view class="text-xxl text-white text-bold">{{userInfo.couponNum?userInfo.couponNum:0}}</view>
						<view class="text-sm text-white">
							<text class="cuIcon-ticket"></text>
							<text class="margin-left-xs">优惠券</text>
						</view>
					</navigator>
				</view>
			</view>
		</view>

		<view class="cu-list menu card-menu margin-top-xs all-orders">
			<view class="cu-bar bg-white solid-bottom">
				<view class="action">
					<text class="cuIcon-titles text-df" :class="'text-'+theme.themeColor"></text>我的订单
				</view>
				<navigator class="action text-df" url="/pages/order/order-list/index" hover-class="none">全部订单<text
						class="cuIcon-right"></text>
				</navigator>
			</view>
			<view class="cu-list grid col-4 no-border">
				<view class="cu-item">
					<navigator url="/pages/order/order-list/index?status=0" hover-class="none">
						<view class="cuIcon-pay" :class="'text-'+theme.themeColor">
							<view v-if="orderCountAll[0]>0" class="cu-tag badge">{{orderCountAll[0]}}</view>
						</view>
						<text class="text-df">待付款</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pages/order/order-list/index?status=1" hover-class="none">
						<view class="cuIcon-send" :class="'text-'+theme.themeColor">
							<view v-if="orderCountAll[1]>0" class="cu-tag badge">{{orderCountAll[1]}}</view>
						</view>
						<text class="text-df">待发货</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pages/order/order-list/index?status=2" hover-class="none">
						<view class="cuIcon-deliver" :class="'text-'+theme.themeColor">
							<view v-if="orderCountAll[2]>0" class="cu-tag badge">{{orderCountAll[2]}}</view>
						</view>
						<text class="text-df">待收货</text>
					</navigator>
				</view>
				<view class="cu-item">
					<navigator url="/pages/order/order-list/index?status=4" hover-class="none">
						<view class="cuIcon-evaluate" :class="'text-'+theme.themeColor">
							<view v-if="orderCountAll[3]>0" class="cu-tag badge">{{orderCountAll[3]}}</view>
						</view>
						<text class="text-df">待评价</text>
					</navigator>
				</view>
			</view>
		</view>
		<view class="cu-list menu card-menu margin-top-xs shadow-lg radius mine">
			<navigator class="cu-item arrow" url="/pages/signrecord/signrecord-info/index" hover-class="none">
				<view class="content">
					<text class="cuIcon-write text-red"></text>
					<text class="text-grey text-df">我的签到</text>
				</view>
			</navigator>
			<navigator class="cu-item arrow" url="/pages/user/user-footprint/index" hover-class="none">
				<view class="content">
					<text class="cuIcon-footprint text-green"></text>
					<text class="text-grey text-df">我的足迹</text>
				</view>
			</navigator>
			<navigator class="cu-item arrow" url="/pages/coupon/coupon-user-list/index" hover-class="none">
				<view class="content">
					<text class="cuIcon-ticket text-orange"></text>
					<text class="text-grey text-df">我的卡券</text>
				</view>
			</navigator>
			<navigator class="cu-item arrow" url="/pages/groupon/groupon-user-list/index" hover-class="none">
				<view class="content">
					<text class="cuIcon-group text-blue"></text>
					<text class="text-grey text-df">我的拼团</text>
				</view>
			</navigator>
			<navigator class="cu-item arrow" url="/pages/bargain/bargain-user-list/index" hover-class="none">
				<view class="content">
					<text class="cuIcon-cardboardforbid text-orange"></text>
					<text class="text-grey text-df">我的砍价</text>
				</view>
			</navigator>
			<navigator v-if="distributionConfig.enable=='1'" class="cu-item arrow" url="/pages/distribution/distribution-center/index" hover-class="none">
				<view class="content">
					<text class="cuIcon-moneybag text-red"></text>
					<text class="text-grey text-df">分销中心</text>
				</view>
			</navigator>
			<navigator class="cu-item arrow" url="/pages/user/user-collect/index" hover-class="none">
				<view class="content">
					<text class="cuIcon-like text-pink"></text>
					<text class="text-grey text-df">我的收藏</text>
				</view>
			</navigator>
			<navigator class="cu-item arrow" url="/pages/user/user-address/list/index" hover-class="none">
				<view class="content">
					<text class="cuIcon-location text-green"></text>
					<text class="text-grey text-df">收货地址</text>
				</view>
			</navigator>
		</view>
		<view class="text-red bg-white exit" @click="logout"><text class="exit-text">退出登录</text></view>
		<view class="margin-top flex justify-center text-sm" v-show="showPrivacyPolicy">
			<navigator class="text-blue text-sm" :url="'/pages/public/webview/webview?title=用户协议&url='+protocolUrl">
				{{' 用户协议 '}}</navigator>和<navigator class="text-blue text-sm  "
				:url="'/pages/public/webview/webview?title=隐私政策&url='+privacyPolicyUrl">{{' 隐私政策'}}</navigator>
		</view>
		<view class="text-gray text-sm text-center margin-sm">www.joolun.com提供技术支持（{{version}}）</view>

	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import util from 'utils/util'
	import __config from '@/config/env'; // 配置文件Ω
	import packagejson from '@/package.json'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				userInfo: {},
				distributionConfig: {
					enable: '1'
				},
				orderCountAll: [],
				showPrivacyPolicy: __config.showPrivacyPolicy,
				privacyPolicyUrl: __config.privacyPolicyUrl,
				protocolUrl: __config.protocolUrl,
				canIUseGetUserProfile: false,
				version: packagejson.version
			};
		},

		components: {},
		props: {},

		onShow() {
			app.initPage().then(res => {
				this.userInfoGet();
				this.orderCountAllFun();
			});
			//更新购物车tabar角标数量
			uni.setTabBarBadge({
				index: 3,
				text: app.globalData.shoppingCartCount + ''
			});
		},

		onLoad(option) {
			// #ifdef MP-WEIXIN
			if (uni.getUserProfile) {
				this.canIUseGetUserProfile = true
			}
			// #endif
			// #ifdef H5
			let code = option.code;
			let state = option.state;
			//授权code获取用户信息
			if (code && state == 'snsapi_userinfo') { //有code
				this.userInfoUpdateByMp({
					jsCode: code,
					scope: state
				});
			}
			// #endif
		},

		methods: {
			// #ifdef MP-WEIXIN
			agreeGetUser(e) {
				if (e.detail.errMsg == 'getUserInfo:ok') {
					api.userInfoUpdateByMa(e.detail).then(res => {
						this.userInfo = res.data;
						uni.setStorageSync('user_info', this.userInfo);
						this.userInfoGet();
					});
				}
			},

			getUserProfile(e) {
				// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
				// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
				wx.getUserProfile({
					desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (detail) => {
						api.userInfoUpdateByMa(detail).then(res => {
							this.userInfo = res.data;
							uni.setStorageSync('user_info', this.userInfo);
							this.userInfoGet();
						});
					}
				})
			},
			// #endif
			// #ifdef H5
			updateUserInfo() {
				if (util.isWeiXinBrowser()) {
					//微信公众号H5，页面授权获取用户详情信息
					let appId = app.globalData.appId;
					let pages = getCurrentPages();
					let currentPage = pages[pages.length - 1];
					let route = currentPage.route;
					let redirectUri = location.href;
					let componentAppId_str = app.globalData.componentAppId ? '&component_appid=' + app.globalData
						.componentAppId : '';
					redirectUri = encodeURIComponent(redirectUri);
					let wx_url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + appId +
						'&redirect_uri=' + redirectUri + componentAppId_str +
						'&response_type=code&scope=snsapi_userinfo&state=snsapi_userinfo#wechat_redirect';
					location.href = wx_url;
				}
			},
			//通过微信公众号网页授权更新用户信息
			userInfoUpdateByMp(parm) {
				let that = this;
				api.userInfoUpdateByMp(parm).then(res => {
					//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
					let query = that.$Route.query;
					delete query.code;
					delete query.state;
					util.resetPageUrl(query);
					this.userInfo = res.data;
					uni.setStorageSync('user_info', this.userInfo);
					this.userInfoGet();
				}).catch(res => {

				});
			},
			// #endif
			//获取商城用户信息
			userInfoGet() {
				api.userInfoGet().then(res => {
					this.userInfo = res.data;
				});
				//分销设置
				api.distributionConfig().then(res => {
					if(res.data) {
						this.distributionConfig = res.data
					}
				});
			},

			orderCountAllFun() {
				api.orderCountAll().then(res => {
					this.orderCountAll = res.data;
				});
			},

			logout() {
				uni.showModal({
					content: '确定退出登录吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',
					success(res) {
						if (res.confirm) {
							api.logout().then(res => {
								let userInfo = res.data;
								uni.setStorageSync('user_info', userInfo);
								if (userInfo) {
									uni.setStorageSync('third_session', userInfo.thirdSession);
								}
								//登出极光
								app.globalData.JIM.loginOut()
								//退出登录完成跳到首页
								uni.reLaunch({
									url: '/pages/home/<USER>'
								});
								//清空购物车数量
								uni.setTabBarBadge({
									index: 3,
									text: '0'
								});
							});
						}
					}
				});
			}

		}
	};
</script>
<style>
	.personal-information {
		margin-top: -60rpx;
	}

	.head {
		margin: auto;
		border: #FFFFFF 2rpx solid;
		/*  #ifdef  H5 || APP-PLUS */
		margin-top: 70rpx;
		/*  #endif  */
	}

	.all-orders {
		width: 94% !important;
		margin: auto !important;
		margin-top: 20rpx !important;
		border-radius: 10rpx !important;
	}

	.mine {
		width: 94% !important;
		margin: auto !important;
		margin-top: 20rpx !important;
		border-radius: 10rpx !important;
	}


	.exit {
		width: 94%;
		border-radius: 10rpx;
		height: 100rpx;
		margin: 20rpx auto;
	}

	.exit-text {
		margin-left: 300rpx;
		line-height: 100rpx;
	}
</style>
