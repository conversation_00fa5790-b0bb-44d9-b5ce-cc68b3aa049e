<template>
	<!-- 商品显示组件-横向排列方式显示 -->
	<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="bg-white">
		<view class="wrapper-list-goods">
			<view class="cu-bar " style="min-height: 80upx">
				<view class="goods-selection text-df margin-left" :style="{color: `${newData.titleColor}`}">
					<text class="text-bold" :class="newData.titleIcon"></text>
					<text class="margin-left-xs">{{newData.title}}</text>
				</view>
				<div-base-navigator class="goods-more text-sm margin-right-xs" :pageUrl="newData.morePageUrl"
					:isSystemUrl="newData.moreIsSystemUrl">
					更多<text class="cuIcon-right"></text>
				</div-base-navigator>
			</view>
			<view>
					<!-- class="item shadow-warp flex  radius " -->
				<scroll-view class="scroll-view_x " scroll-x style="width:auto;overflow:hidden;">
					<block v-for="(item, index) in goodsList" :key="index">
						<block v-for="(cover, index2) in item.coverUrls">
							<div-base-navigator v-if="cover.sizeType==newData.imgShowSize" hover-class="none"
							    class="item shadow-warp flex  radius"
								:pageUrl="newData.contentPageUrl+'&goods_id=' + item.id"
								:isSystemUrl="newData.contentIsSystemUrl">
								<view>
									<image  style="width: 100%;display: block;"  mode="widthFix" :src="cover.url? cover.url: '/static/public/img/no_pic.png'">
									</image>
								</view>
								<view style="display: block;"  v-if="newData.nameFlag" class="text-cut text-center margin-top-sm  text-sm ">{{item.name}}
								</view>
							</div-base-navigator>
						</block>
					</block>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
	export default {
		components: {
			divBaseNavigator
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						title: '商品甄选',
						titleColor: 'red',
						titleIcon: 'cuIcon-message',
						pageSpacing: 0,
            isLogin:true
					}
				}
			}
		},
		mounted() {
      if(!uni.getStorageSync('third_session')){
        this.isLogin=false
      }
			this.getGoods()
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				goodsList: []
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			},
			getGoods() {
				console.log("条",{
					appId: app.globalData.appId,
					sortId: this.newData.sortRule?this.newData.sortRule.id:'',})

        let params={
          appId: app.globalData.appId,
          sortId: this.newData.sortRule?this.newData.sortRule.id:'',}
        let promise
        if (this.isLogin){
          promise=api.getRecommendGoods(params)
        }else{
          promise=api.getRecommendGoodsFreedom(params)
        }
        promise.then(res => {
					for (let i = 0; i < res.data.length; i++) {
						res.data[i].coverUrls = JSON.parse(res.data[i].coverUrls)
					}
					this.goodsList = res.data
				}).catch(err => {
					console.log(err)
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.wrapper-list-goods {
		white-space: nowrap;
		padding: 0 30rpx 6rpx 30rpx;
	}

	.wrapper-list-goods .item {
		display: inline-block;
		width: 40%;
		margin-top: 10px;
		margin-bottom: 10px;
		margin-left: 10rpx;
		margin-right: 10rpx;
		border: 1rpx solid #eee;
		background-color: #fff;
	}

	.wrapper-list-goods .item:nth-last-child(1) {
		margin-right: 0;
	}

	.wrapper-list-goods .item .img-box {
		// width: 100%;
		// height: 260rpx;

	}

	.wrapper-list-goods .item .img-box image {
		// width: 100%;
		// height: 100%;
		// border-radius: 5rpx 5rpx 0 0;
	}

	.goods-selection {
		margin-left: 0rpx !important;
		color: #666666;
	}

	.goods-more {
		margin-right: 0rpx !important;
		color: #666666;
	}

	.goods-box {
		width: 349rpx;
		height: 530rpx;
		background-color: #fff;
		overflow: hidden;
		margin-bottom: 20rpx;
		border-radius: 10rpx;
	}

	.goods-box .img-box {
		width: 100%;
		// height: 349rpx;
		overflow: hidden;
	}

	.goods-box .img-box image {
		width: 100%;
		// height: 349rpx;
	}

	.goods-detail {
		margin-top: -20rpx !important;
	}
</style>
