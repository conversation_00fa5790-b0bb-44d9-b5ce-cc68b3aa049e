
<template>
	<!-- 双列图显示组件 -->
	<view style="background: #FFFFFF;" :style="{paddingBottom: `${newData.pagePaddingBottom}px`,paddingTop: `${newData.pagePaddingTop}px`,paddingLeft: `${newData.pagePaddingLeft}px`,paddingRight: `${newData.pagePaddingRight}px`}" >
		<view style="display: flex;justify-content: center;" >
			<div-base-navigator  :isSystemUrl="newData.firstIsSystemUrl"  :pageUrl="newData.firstPageUrl"  :style="{width: '100%',marginRight: `${newData.interval}px`}" >
				<image  :style="{width: '100%',marginRight: `${newData.interval}px`, borderRadius: `${newData.firstBorderRadius}rpx`}" mode="widthFix" :src="newData.firstImageUrl" ></image>
			</div-base-navigator>
			<div-base-navigator  :isSystemUrl="newData.secondIsSystemUrl"  :pageUrl="newData.secondPageUrl"  :style="{width: '100%',marginRight: `${newData.interval}px`}" >
				<image   :style="{width: '100%',marginLeft: `${newData.interval}px`, borderRadius: `${newData.secondBorderRadius}rpx`}" mode="widthFix" :src="newData.secondImageUrl" ></image>
			</div-base-navigator>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import divBaseNavigator from '../div-components/div-base/div-base-navigator.vue'
    export default {
        name: 'double-row-image',
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        pageUrl: ``,
                        imageUrl: '',
                        height: 100
	                }
	            }
            }
	    },
	    components: {
			divBaseNavigator
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value
			};
		},
		methods: {}
    }
</script>
<style>
</style>
