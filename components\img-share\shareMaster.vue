<template>
	<view class="shareMasterComponent"
	     :style="{marginBottom: `${newData.pageMarginBottom}px`,marginTop: `${newData.pageMarginTop}px`,marginLeft: `${newData.pageMarginLeft}px`,marginRight: `${newData.pageMarginRight}px`}">
	  <view v-show="newData.showType == '1'">
	    <view class="show">
         <view class="round cu-avatar bg-img lg margin-xs text-yellow"
				style="border: 5px white solid"
               :style="{width:`${newData.avatarSize}px`,height:`${newData.avatarSize}px`,backgroundImage:`${'url('+shareData.headimgUrl+')'}`}">
          </view>
	    </view>
	  </view>
	  <view :style="{backgroundColor: newData.backColor,
	        marginBottom: `${newData.insideMarginBottom}px`,
	        marginTop: `${newData.insideMarginTop}px`,
	        paddingBottom: `${newData.insidePaddingBottom}px`,
	        paddingTop: `${newData.insidePaddingTop}px`,
	        borderTopLeftRadius:`${newData.topBorderRadius}px`,
	        borderTopRightRadius:`${newData.topBorderRadius}px`,
	        borderBottomLeftRadius:`${newData.bottomBorderRadius}px`,
	        borderBottomRightRadius:`${newData.bottomBorderRadius}px`}">
	    <view v-show="newData.showType == '0'">
	      <view class="show">
		  <view class="round cu-avatar bg-img lg margin-xs text-yellow"
			   :style="{backgroundImage:`${'url('+shareData.headimgUrl+')'}`,width:`${newData.avatarSize}px`,height:`${newData.avatarSize}px`}">
		  </view>
	        <view :style="{color: newData.fontColor}">
	          <p class="content_title text-cut">{{ newData.title }}</p>
	          <p class="content_describe text-cut">{{ newData.describe }}</p>
	          <p class="content_remark text-cut">{{ newData.remark }}</p>
	          <p class="content_share text-cut">来自【{{imgShareData.nickName}}】的分享</p>
	        </view>
	      </view>
	      <view class="bottomFont">
	        <h5 class="text-cut" style=" font-size: 8px; margin-top: 3px; color:#999;">{{ newData.copyright }}</h5>
	      </view>
	    </view>
	    <view v-show="newData.showType == '1'">
	      <view :style="{color: newData.fontColor}">
	        <view class="content_title show text-cut"><p>{{ newData.title }}</p></view>
	        <view class="content_describe show text-cut"><p>{{ newData.describe }}</p></view>
	        <view class="content_remark show text-cut"><p>{{ newData.remark }}</p></view>
	        <view class="content_share show text-cut"><p>来自【{{imgShareData.nickName}}】的分享</p></view>
	      </view>
	      <view class="bottomFont">
	        <h5 class="show text-cut" style=" font-size:9px; margin-top: 3px; color:#999;">{{ newData.copyright }}</h5>
	      </view>
	    </view>
	  </view>
	</view>
	
 <!-- <view class="shareMasterComponent"
       :style="{marginBottom: `${newData.pageMarginBottom}px`,marginTop: `${newData.pageMarginTop}px`}">
    <view :style="{backgroundColor: newData.backColor, borderRadius:`${newData.borderRadius}px`}">
      <view v-show="newData.showType == '0'">
        <view class="show">
          <view class="cu-avatar bg-img lg margin-xs text-yellow"
				style="width: 80px;height: 80px;"
               :style="{backgroundImage:`${'url('+shareData.headimgUrl+')'}`}">
          </view>
          <view :style="{color: newData.fontColor}">
            <p>{{ newData.title }}</p>
            <p>{{ newData.describe }}</p>
            <p>{{ newData.remark }}</p>
            <p>来自【{{shareData.nickName}}】的分享</p>
          </view>
        </view>
        <view class="bottomFont">
          <h5 class="text-cut" style=" font-size: 8px; margin-top: 5px; color:#999;">{{ newData.copyright }}</h5>
        </view>
      </view>
      <view v-show="newData.showType == '1'">
        <view class="show">
          <view class="round cu-avatar bg-img lg margin-xs text-yellow"
               :style="{backgroundImage:`${'url('+shareData.headimgUrl+')'}`,width:`${newData.avatarSize}px`,height:`${newData.avatarSize}px`}">
          </view>
        </view>
        <view  :style="{color: newData.fontColor}">
          <view class="show text-cut"><p>{{ newData.title }}</p></view>
          <view class="show text-cut"><p>{{ newData.describe }}</p></view>
          <view class="show text-cut"><p>{{ newData.remark }}</p></view>
          <view class="show text-cut"><p>来自【{{shareData.nickName}}】的分享</p></view>
        </view>
        <view class="bottomFont">
          <h5 class="show text-cut" style=" font-size: 8px; margin-top: 5px; color:#999;">{{ newData.copyright }}</h5>
        </view>
      </view>
    </view>
  </view> -->
</template>

<script>
import {mapState, mapGetters, mapMutations, mapActions} from 'vuex';


export default {
  data() {
    return {
		newData:this.value,
		shareData:this.imgShareData
	};
  },
  components: {},
  props: {
	value: {
		type: Object,
		default: function() {
			return {}
		}
	},
	imgShareData: {
		type: Object,
		default: function() {
			return {}
		}
	},
  },
  watch: {
  	imgShareData(val, oldVal) {
  		if (val != oldVal) {
  			this.shareData = val;
  		}
  	}
  },
  computed: {

  },
  created() {
  },
  mounted() {
  },
  methods: {

  },

};
</script>
<style >

.show {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.bottomFont {
  padding-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.content_title{
  font-weight: bolder;
  font-size: 24px;
  padding-bottom: 10px;
}
.content_describe{
  font-size: 12px;
  padding-bottom: 5px;
}
.content_remark{
  font-size: 12px;
  padding-bottom: 5px;
}
.content_share{
  font-size: 12px;
}
</style>
