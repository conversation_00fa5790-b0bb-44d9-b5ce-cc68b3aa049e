<template>
	<!-- 分割线组件 -->
  <view :style="{paddingTop:`${newData.paddingTop}px`,paddingBottom:`${newData.paddingBottom}px`}"  >
    <view v-if="newData.type==0" :class="newData.background && newData.background.indexOf('bg-') != -1 ? newData.background : ''" :style="{backgroundColor: `${newData.background.indexOf('bg-') != -1 ?'':newData.background}`,height:`${newData.height}px`,width:newData.width+'%'}">
    </view>
    <view v-if="newData.type==1" :style="{borderColor:newData.background}" style="border:1px solid;width: 100%">
    </view>
    <view v-if="newData.type==2" :style="{borderColor:newData.background}" style="border:1px dashed;width: 100%">
    </view>
  </view>
</template>

<script>
	const app = getApp();
	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
					}
				}
			}
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
			};
		},
		methods: {
	
		}
	}
</script>

<style scoped lang="scss">
	
</style>
