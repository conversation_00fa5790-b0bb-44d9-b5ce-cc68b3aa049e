<template>
	<view class="order-confirm">
		<view class="page-title">订单确认</view>
		
		<view class="goods-info" v-if="goodsData">
			<view class="goods-name">{{goodsData.goods.name}}</view>
			
			<view class="goods-specs" v-if="goodsData.selectedSpecs && goodsData.selectedSpecs.length > 0">
				<text class="specs-label">已选:</text>
				<text class="specs-value">
					{{goodsData.selectedSpecs.map(spec => spec.value).join(', ')}}
				</text>
			</view>
			
			<view class="goods-price" v-if="goodsData.sku">
				<text class="price">¥{{goodsData.sku.salesPrice}}</text>
			</view>
		</view>
		
		<view class="empty-tip" v-else>
			<text>暂无商品数据</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				goodsData: null
			}
		},
		onLoad() {
			// 从localStorage读取数据
			try {
				const orderData = uni.getStorageSync('orderConfirmData');
				if (orderData) {
					this.goodsData = JSON.parse(orderData);
					console.log('订单数据:', this.goodsData);
				} else {
					console.log('未找到订单数据');
				}
			} catch (e) {
				console.error('读取订单数据出错:', e);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.order-confirm {
		padding: 30rpx;
		
		.page-title {
			font-size: 36rpx;
			font-weight: bold;
			margin-bottom: 30rpx;
			text-align: center;
		}
		
		.goods-info {
			background-color: #fff;
			padding: 20rpx;
			border-radius: 10rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			
			.goods-name {
				font-size: 32rpx;
				margin-bottom: 20rpx;
			}
			
			.goods-specs {
				font-size: 28rpx;
				margin-bottom: 20rpx;
				color: #666;
				
				.specs-label {
					margin-right: 10rpx;
				}
			}
			
			.goods-price {
				.price {
					font-size: 36rpx;
					color: #f53f3f;
					font-weight: bold;
				}
			}
		}
		
		.empty-tip {
			text-align: center;
			padding: 100rpx 0;
			color: #999;
			font-size: 28rpx;
		}
	}
</style> 