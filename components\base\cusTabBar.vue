<template >
	<!-- 导航按钮组件 -->
	<view  class="cus_tab_bar"   :style="{top:`${top-height}px`,width:`${width}px`}" >
		<!-- <view class=" cu-list grid no-border navButton " :class="'col-'+newData.navButtons.length" style="padding: 0 0;"> -->
		<view id="cusTabBar" class="flex justify-around  padding-top-xs padding-bottom-xs  " :style="{backgroundColor: `${newData.background}`}">
			<view class="cu-item" v-for="(item,index) in newData.navButtons" :key="index">
				<div-base-navigator :isSystemUrl="item.isSystemUrl"  :pageUrl="item.pageUrl" :style="{backgroundColor: `${newData.background}`}"  hover-class="none" >
					<image style="width: 100%;display: block;" :src="item.imageUrl" class="nav_bt_img"></image>
					<text  class="flex justify-center" :style="{color: `${newData.textColor}`}">{{item.navName}}</text>
				</div-base-navigator>
			</view>
		</view>
	</view>
</template>

<script>
	import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
	const app = getApp();
    export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        rowNum: 4,
					    textColor: '#333333',
					    pageSpacing: 0,
					    navButtons: []
	                }
	            }
            }
	    },
	    components: {
			divBaseNavigator
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
				width:"",
				top:"",//屏幕高度
				height:"",//本身高度
			};
		},
		created() {
			// console.log("屏幕高度",uni.getSystemInfoSync().windowHeight)	
			// console.log("屏幕高度",uni.getSystemInfoSync().windowWidth)	
			this.width = uni.getSystemInfoSync().windowWidth;
			this.top = uni.getSystemInfoSync().windowHeight;
		},
		mounted() {
			let query = uni.createSelectorQuery().in(this);
			query.select('#cusTabBar').boundingClientRect(data => {
			  this.height = data.height
			}).exec();	
			this.$emit("bottomHeightShow",this.height)
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					}).then(()=>{
						location.reload() 
					});
				}
			}
		}
    }
</script>

<style scoped lang="scss">
	
	/* 导航 */
	.nav_bt_img{
		width: 80rpx !important;
		height: 80rpx !important;
	}
	
	.navButton{
		padding-top: 16rpx  !important;
		padding-bottom: 5rpx  !important;
		
	}
	
	.cus_tab_bar{
		display: block;
		position: fixed;
		z-index: 2;
	}
	
</style>
