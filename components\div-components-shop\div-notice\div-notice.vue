<template>
	<!-- <view :class="'bg-'+theme.backgroundColor"> -->
	<!-- 通知通告 组件 -->
	<view class="padding-top-sm">
		<view class="adsec light bg-yellow" :style="{marginBottom: `${newData.pageSpacing}px`}">
			<view class="adsec-icon text-xl margin-right-xs margin-left-xs"><text class="cuIcon-notification text-red"></text></view>
			<swiper class="swiper_container" autoplay="true" circular="true" :interval="newData.interval">
				<swiper-item v-for="(item, index) in newData.noticeList" :key="index" @tap="jumpPage(item.pageUrl)">
					<view class="text-orange text-sm">
						<text class="round bg-red announcement text-sm padding-lr-xs">{{item.tag}}</text>
						<text class="details margin-left-xs">{{item.content}}</text>
						<text v-if="item.pageUrl" class="cuIcon-right"></text>
					</view>
				</swiper-item>
			</swiper>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import {
		pageUrls
	} from '../div-base/div-page-urls.js'
	export default {
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						noticeList: []
					}
				}
			}
		},
		components: {},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				pageUrls: pageUrls
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					if (this.pageUrls.tabPages.indexOf(page) != -1) {
						uni.switchTab({
							url: page
						});
					} else {
						uni.navigateTo({
							url: page
						});
					}
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	/* 公告 */
	.adsec {
		width: 100%;
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		align-items: center;
		padding: 10rpx 10rpx;
		height: 80rpx;
	}

	.adsec-icon {
		height: 80rpx;
		line-height: 80rpx;
	}

	.swiper_container {
		height: 80rpx;
		width: 100%;
		line-height: 80rpx;
	}
</style>
