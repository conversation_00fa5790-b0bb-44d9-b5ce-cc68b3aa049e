<template>
	<view class="thumbsUpButtonComponent"
	       :style="{marginBottom: `${newData.pageMarginBottom}px`,marginTop: `${newData.pageMarginTop}px`,marginLeft: `${newData.pageMarginLeft}px`,marginRight: `${newData.pageMarginRight}px`}">
	    <view v-if="newData.type == 0"  style="display: flex" :style="{'justify-content':`${newData.location}`, background:`${newData.backColor}`}" >
			<view  style="display: flex" :style="{ width:`${newData.size=='100%'?'100%':''}`, paddingBottom: `${newData.contentPaddingBottom}px`,paddingTop: `${newData.contentPaddingTop}px`,paddingLeft: `${newData.contentPaddingLeft}px`,paddingRight: `${newData.contentPaddingRight}px`}">
				<button
				@click="click()" 
				  :size="newData.size!='100%'?newData.size:''"
				  :style="{width:`${newData.size=='100%'?'100%':''}`,
						color:`${newData.fontColor}`,
						fontWeight:`${newData.fontWeight}`,
						fontSize:`${newData.fontSize}px`,
						letterSpacing:`${newData.spacing}px`,
						borderRadius: `${newData.borderRadius}px`,
						background:`${newData.buttonColor}`,}">{{ newData.content }}
				</button>
			</view>
		</view>
		<view v-if="newData.type == 1" :style="{ background:`${newData.backColor}`}" >
			<view @click="click()"  class="img_content flex justify-center align-center" :style="{paddingBottom: `${newData.contentPaddingBottom}px`,paddingTop: `${newData.contentPaddingTop}px`,paddingLeft: `${newData.contentPaddingLeft}px`,paddingRight: `${newData.contentPaddingRight}px`}" style="align-items: center;align-items: center">
				<view :style="{width: `${newData.imgWidth}%`,background:`${newData.backColor}`,}">
					<image style="width: 100%;display: block;" :style="{borderRadius: `${newData.borderRadius}px`}" mode="widthFix" :src="newData.imgUrl"></image>
					<!-- <image style="width: 100%;display: block;" :style="{borderRadius: `${newData.borderRadius}px`}" mode="widthFix" :src="'https://fuss10.elemecdn.com/a/3f/3302e58f9a181d2509f3dc0fa68b0jpeg.jpeg'"></image> -->
				</view>
			</view>
		</view>
		<uni-popup ref="popup" type="dialog" @touchmove.stop.prevent="moveHandle">
			<view style="width:680rpx" >
				<image style="width: 100%;display: block;" mode="widthFix" :src="popupImg"></image>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	import api from '@/utils/api'
	const util = require("utils/util.js");
	
	
	export default {
		data() {
			return {
				newData: this.value,
				popupImg:'',//弹窗图片
			};
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
			imgShareData: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		computed: {

		},
		created() {},
		mounted() {},
		methods: {
			click() {
				let pageId = util.getUrlParam(location.href, "page_id");
				let voteId = util.getUrlParam(location.href, "data_id");
				let params = {
					pageId:pageId,
					voteId:voteId
				}
				console.log("dasdas", params)
				api.imgShareVote(params).then(res => {
					console.log("投票的结果", res)
					this.$emit("shareVote")
					if(res.data){
						this.popupImg = res.data;
						this.$refs.popup.open()
					}
				}).catch(err => {
					console.log(err)
				});
			},
			closePopup() {
				this.$refs.popup.close()
			},
		},
		

	};
</script>
<style>
	.thumbsUpButtonComponent {
	}

	.img_content {
		width: 100%;
	}
</style>
