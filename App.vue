<script>
import Vue from 'vue'
import api from 'utils/api'
import util from 'utils/util'
import __config from 'config/env';
import JMessage from 'public/jmessage-wxapplet-sdk/jmessage-wxapplet-sdk-1.4.0.min.js'


// #ifdef APP-PLUS
import APPUpdate from "@/public/APPUpdate/index.js";//App版本更新
// #endif

export default {
	globalData: {
		// shoppingCartCount: 0,//购物车数量
		tenantId: "1468808703988994048",//租户Id
		appId: null,//公众号appId
		componentAppId: null,//第三方平台appid
		theme: { //主题定义
			backgroundColor: null, //背景颜色,支持渐变色
			themeColor: null, //主题颜色
			tabbarBackgroundColor: null,//tabbar背景颜色
			tabbarColor: null,//tabBar上的文字默认颜色
			tabbarSelectedColor: null,//tabBar上的文字选中时的颜色
			tabbarBorderStyle: null,//tabBar上边框的颜色
			tabbarItem: []//tabBar明细设置
		},
		JIM: null,//极光JMessage
	},
	onLaunch: function () {
		// #ifdef MP
		//小程序平台检测新版本
		this.updateManager();
		// this.doLogin();
		// #endif
		// 原生app版本更新检测
		// #ifdef APP-PLUS
		APPUpdate();
		// #endif

		// #ifdef H5
		// H5平台获取参数中的租户ID、公众号appID,并存入globalData
		let local = location.href
		let tenantId = util.getUrlParam(local, "tenant_id");
		let appId = util.getUrlParam(local, "app_id");
		let pageId = util.getUrlParam(local, "page_id");
		let componentAppId = util.getUrlParam(local, "component_appid");
		this.globalData.tenantId = tenantId;
		this.globalData.appId = appId;
		this.globalData.pageId = pageId;
		this.globalData.componentAppId = componentAppId;
		// console.log("第一次初始化",this.globalData)
		// console.log("第一次初始化third_session",uni.getStorageSync('third_session'))
		// console.log("第一次初始化user_info",uni.getStorageSync('user_info'))
		// let userInfo = uni.getStorageSync('user_info')
		// if(userInfo.appId != appId){
		// 	console.log("重新登录")
		// 	uni.removeStorageSync('third_session');
		// }
		// #endif

		// #ifdef APP-PLUS
		// APP平台需要从配置文件中获取租户ID
		let tenantId = __config.tenantId;
		this.globalData.tenantId = tenantId;
		// #endif




		//设置全局样式
		// this.setGlobalStyle();
		//获取购物车数量
		// if(uni.getStorageSync('third_session')){
		// 	this.shoppingCartCount();
		// }
	},
	//导航栏菜单
	onNavigationBarButtonTap(val) {
		console.log(val.index);
		if (val.index == 1) {
			console.log("第一个按钮");

		};
		if (val.index == 0) {
			onsole.log("第二个按钮");

		}
	},
	methods: {
		//设置全局参数
		setGlobalData() {
			let local = location.href
			let tenantId = util.getUrlParam(local, "tenant_id");
			let appId = util.getUrlParam(local, "app_id");
			let pageId = util.getUrlParam(local, "page_id");
			let componentAppId = util.getUrlParam(local, "component_appid");
			console.log("设置全局参数", local)
			this.globalData.appId = appId;
			this.globalData.tenantId = tenantId;
			this.globalData.pageId = pageId;
			this.globalData.componentAppId = componentAppId;

		},
		//设置全局样式
		setGlobalStyle() {
			uni.getSystemInfo({
				success: function (e) {
					// [2020-08-01]更新ColorUI 修复ios 状态栏错位
					// #ifndef MP
					Vue.prototype.StatusBar = e.statusBarHeight;
					if (e.platform == 'android') {
						Vue.prototype.CustomBar = e.statusBarHeight + 50;
					} else {
						Vue.prototype.CustomBar = e.statusBarHeight + 45;
					};
					// #endif
					// #ifdef MP-WEIXIN || MP-QQ
					Vue.prototype.StatusBar = e.statusBarHeight;
					let capsule = wx.getMenuButtonBoundingClientRect();
					if (capsule) {
						Vue.prototype.Custom = capsule;
						// Vue.prototype.capsuleSafe = uni.upx2px(750) - capsule.left + uni.upx2px(750) - capsule.right;
						Vue.prototype.CustomBar = capsule.bottom + capsule.top - e.statusBarHeight;
					} else {
						Vue.prototype.CustomBar = e.statusBarHeight + 50;
					}
					// #endif
					// #ifdef MP-ALIPAY
					Vue.prototype.StatusBar = e.statusBarHeight;
					Vue.prototype.CustomBar = e.statusBarHeight + e.titleBarHeight;
					// #endif
				}
			})
			//获取主题装修配置
			// api.themeMobileGet().then(res => {
			// 	let themeMobile = res.data;
			// 	//定义默认配置
			// 	let backgroundColor = 'gradual-scarlet';
			// 	let themeColor = 'red';
			// 	let tabbarBackgroundColor = '#ffffff';
			// 	let tabbarColor = '#666666';
			// 	let tabbarSelectedColor = '#e53c43';
			// 	let tabbarBorderStyle = '#black';
			// 	let tabbarItem = [
			// 		{
			// 			index: 0,
			// 			text: '首页',
			// 			iconPath: '/static/public/img/icon-1/1-001.png',
			// 			selectedIconPath: '/static/public/img/icon-1/1-002.png'
			// 		},
			// 		{
			// 			index: 1,
			// 			text: '分类',
			// 			iconPath: '/static/public/img/icon-1/2-001.png',
			// 			selectedIconPath: '/static/public/img/icon-1/2-002.png'
			// 		},
			// 		{
			// 			index: 2,
			// 			text: '消息',
			// 			iconPath: '/static/public/img/icon-1/3-001.png',
			// 			selectedIconPath: '/static/public/img/icon-1/3-002.png'
			// 		},
			// 		{
			// 			index: 3,
			// 			text: '购物车',
			// 			iconPath: '/static/public/img/icon-1/4-001.png',
			// 			selectedIconPath: '/static/public/img/icon-1/4-002.png'
			// 		},
			// 		{
			// 			index: 4,
			// 			text: '我的',
			// 			iconPath: '/static/public/img/icon-1/5-001.png',
			// 			selectedIconPath: '/static/public/img/icon-1/5-002.png'
			// 		}
			// 	]
			// 	//将默认配置换成后台数据
			// 	if(themeMobile){
			// 		themeColor = themeMobile.themeColor
			// 		backgroundColor = themeMobile.backgroundColor
			// 		tabbarBackgroundColor = themeMobile.tabbarBackgroundColor
			// 		tabbarColor = themeMobile.tabbarColor
			// 		tabbarSelectedColor = themeMobile.tabbarSelectedColor
			// 		tabbarBorderStyle = themeMobile.tabbarBorderStyle
			// 	}
			// 	this.globalData.theme.backgroundColor = backgroundColor
			// 	this.globalData.theme.themeColor = themeColor
			// 	this.globalData.theme.tabbarBackgroundColor = tabbarBackgroundColor
			// 	this.globalData.theme.tabbarColor = tabbarColor
			// 	this.globalData.theme.tabbarSelectedColor = tabbarSelectedColor
			// 	this.globalData.theme.tabbarBorderStyle = tabbarBorderStyle
			// 	this.globalData.theme.tabbarItem = tabbarItem
			// 	if(themeMobile && themeMobile.tabbarItem && themeMobile.tabbarItem.info.length > 0){
			// 		let tabbarItemInfo = themeMobile.tabbarItem.info
			// 		tabbarItemInfo.forEach(item => {
			// 			if(item.text)tabbarItem[item.index].text = item.text
			// 			if(item.iconPath)tabbarItem[item.index].iconPath = item.iconPath
			// 			if(item.selectedIconPath)tabbarItem[item.index].selectedIconPath = item.selectedIconPath
			// 		})
			// 		this.globalData.theme.tabbarItem = tabbarItem
			// 	}
			// 	this.setTabBar()
			// });
		},

		// #ifdef MP
		//小程序平台检测新版本
		updateManager() {
			const updateManager = uni.getUpdateManager();
			updateManager.onUpdateReady(function () {
				uni.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					success(res) {
						if (res.confirm) {
							updateManager.applyUpdate();
						}
					}
				});
			});
		},
		// #endif

		//设置tabbar
		setTabBar() {
			console.log("来设置")
			let themeMobile = this.globalData.theme
			themeMobile = {
				tabbarBackgroundColor: '#ffffff',
				tabbarColor: '#666666',
				tabbarSelectedColor: '#0612EE',
				tabbarBorderStyle: '#black',
				tabbarItem: [{ "selectedIconPath": "/static/public/img/icon-3/1-002.png", "index": 0, "text": "首页", "iconPath": "/static/public/img/icon-3/1-001.png", "pagePath": "pages/home/<USER>" },
				{ "selectedIconPath": "/static/public/img/icon-3/2-002.png", "index": 1, "text": "类别", "iconPath": "/static/public/img/icon-3/2-001.png", "pagePath": "pages/home/<USER>" },
				{ "selectedIconPath": "/static/public/img/icon-3/3-002.png", "index": 2, "text": "消息", "iconPath": "/static/public/img/icon-3/3-001.png", "pagePath": "pages/home/<USER>" },
				{ "selectedIconPath": "/static/public/img/icon-3/4-002.png", "index": 3, "text": "购物车", "iconPath": "/static/public/img/icon-3/4-001.png", "pagePath": "pages/home/<USER>" },
				{ "selectedIconPath": "/static/public/img/icon-3/5-002.png", "index": 4, "text": "我的", "iconPath": "/static/public/img/icon-3/5-001.png", "pagePath": "pages/home/<USER>" }]
			}
			// if(themeMobile.tabbarBackgroundColor){
			//动态设置 tabBar 的整体样式
			uni.setTabBarStyle({
				backgroundColor: themeMobile.tabbarBackgroundColor,
				color: themeMobile.tabbarColor,
				selectedColor: themeMobile.tabbarSelectedColor,
				borderStyle: themeMobile.tabbarBorderStyle
			});
			let tabbarItem = themeMobile.tabbarItem;
			tabbarItem.forEach(item => {
				//动态设置 tabBar 某一项的内容
				let iconPath = item.iconPath;
				let selectedIconPath = item.selectedIconPath;
				// #ifdef H5
				//uni tabBar H5不支持http的图片,但是修改一下可以支持
				if (selectedIconPath.indexOf('http') != -1) { // 此判断包括 http 和 https
					let indexTemp = selectedIconPath.indexOf(':/') + 1;
					selectedIconPath = selectedIconPath.substring(indexTemp, selectedIconPath.length);
				}
				if (iconPath.indexOf('http') != -1) {
					let indexTemp = iconPath.indexOf(':/') + 1;
					iconPath = iconPath.substring(indexTemp, iconPath.length);
				}
				// #endif
				uni.setTabBarItem({
					index: item.index,
					text: item.text,
					iconPath: iconPath,
					selectedIconPath: selectedIconPath,
					fail: (res) => { console.log("失败了", res) },
					complete: (res) => { console.log("complete的：", res) },
				});
			})
			// }
		},

		//获取购物车数量
		// shoppingCartCount(){
		// 	api.shoppingCartCount().then(res => {
		// 		let shoppingCartCount = res.data;
		// 		this.globalData.shoppingCartCount = shoppingCartCount + '';
		// 		uni.setTabBarBadge({
		// 			index: 3,
		// 			text: this.globalData.shoppingCartCount + ''
		// 		});
		// 	});
		// },
		//页面初始化方法，供每个页面调用
		initPage() {
			let that = this;
			return new Promise((resolve, reject) => {
				resolve("success");
				//小程序或公众号H5，每个页面都进行登录校验
				/*if(util.isMiniPg() || (that.globalData.appId && util.isWeiXinBrowser())){
					if (!uni.getStorageSync('third_session')) {
						//无thirdSession，进行登录
						that.dologin().then(res => {
							console.log("doLogin--res")
							//初始化极光IM
							// that.initJIM();
							resolve("success");
						});
		  // resolve("success");
					} else {
						//更新信息
						if (!uni.getStorageSync('user_info').nickName || !uni.getStorageSync('user_info').headimgUrl){
							this.checkUpdateByMp();
							resolve("success");
						}
						if(util.isMiniPg()){//小程序需要检查登录态是否过期
							uni.checkSession({
							  success () {
								//session_key 未过期，并且在本生命周期一直有效
								console.log('session_key 未过期')
								//初始化极光IM
								// that.initJIM();
								resolve("success");
							  },
							  fail () {
								// session_key 已经失效，需要重新执行登录流程
								console.log('session_key 已经失效')
								that.doLogin().then(res => {
									//初始化极光IM
									// that.initJIM();
									resolve("success");
								});
							  }
							})
						}else{
							//初始化极光IM
							// that.initJIM();
							resolve("success");
						}
					}
				}else{
					//初始化极光IM
					// that.initJIM();
					resolve("success");
				}*/
			});
		},

		//登录操作
		doLogin(realNeedLogin = false) {
			uni.showLoading({
				title: '登录中'
			});
			console.log("doLogin--in")
			return new Promise((resolve, reject) => {
				// #ifdef MP-WEIXIN
				if (!realNeedLogin) {
					resolve("success");
					return
				}
				//微信小程序登录
				this.loginWxMa().then(res => {
					resolve("success");
				});
				// #endif
				// #ifdef H5
				//微信公众号H5
				if (util.isWeiXinBrowser()) {
					console.log("doLogin--begin")
					let local = location.href
					let code = util.getUrlParam(local, "code");
					let state = util.getUrlParam(local, "state");
					//授权code登录
					if (code) {//有code
						if (state == 'snsapi_base') {//登录授权
							this.loginWxMp(code).then(res => {
								resolve("success");
							});
						}
					} else {//无code则发起网页授权
						//微信公众号H5，页面授权登录
						let appId = this.globalData.appId;
						let pages = getCurrentPages();
						let currentPage = pages[pages.length - 1];
						console.log(pages)
						let route = currentPage.route;
						let redirectUri = location.href;
						let componentAppId_str = this.globalData.componentAppId ? '&component_appid=' + this.globalData.componentAppId : '';
						redirectUri = encodeURIComponent(redirectUri);
						let wx_url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + appId +
							'&redirect_uri=' + redirectUri + componentAppId_str +
							'&response_type=code&scope=snsapi_base&state=snsapi_base#wechat_redirect';
						location.href = wx_url;
					}
				}
				// #endif
			});
		},
		//微信小程序登录
		// #ifdef MP-WEIXIN
		loginWxMa() {
			return new Promise((resolve, reject) => {
				let that = this;
				uni.login({
					success: function (res) {
						if (res.code) {
							api.loginWxMa({
								jsCode: res.code
							}).then(res => {
								uni.hideLoading();
								let userInfo = res.data;
								uni.setStorageSync('third_session', userInfo.thirdSession);
								uni.setStorageSync('user_info', userInfo);
								// 获取购物车数量
								// that.shoppingCartCount();
								resolve("微信小程序登录success");
							});
						}
					}
				});
			});
		},
		// #endif
		//公众号登录
		// #ifdef H5
		loginWxMp(code) {
			let that = this;
			return new Promise((resolve, reject) => {
				let that = this
				api.loginWxMp({
					jsCode: code
				}).then(res => {
					//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
					console.log("登录信息，清除URL中的code和state")

					// #ifdef H5
					// 仅在H5环境执行resetPageUrl
					util.resetPageUrl({});
					// #endif

					let userInfo = res.data;

					console.log("登录信息", res)
					//同步执行
					uni.setStorageSync('third_session', userInfo.thirdSession);
					uni.setStorageSync('user_info', userInfo);
					//获取购物车数量
					// that.shoppingCartCount();
					resolve("success");
				}).catch(res => {
					console.log("公众号登录失败 ", res)
				});
			});
		},
		// #endif
		//初始化极光IM
		initJIM() {
			let that = this
			if (!this.globalData.JIM) {
				try {
					this.globalData.JIM = new JMessage({})
				} catch (e) {
					console.log(e)
				}
			}
			return new Promise((resolve, reject) => {
				//获取用户信息
				let userInfo = uni.getStorageSync('user_info')
				let thirdSession = uni.getStorageSync('third_session')
				//还不是商城用户，不登录极光
				if (!userInfo || !userInfo.id || !thirdSession) {
					console.log("还不是商城用户，不登录极光")
					resolve("还不是商城用户，不登录极光success");
					return
				}
				let JIM = that.globalData.JIM
				if (JIM.isInit()) {//极光IM已经初始化，直接登录
					console.log("极光IM已经初始化，直接登录")
					that.loginJIM(userInfo).then(res => {
						resolve("极光IM已经初始化，直接登录success");
					});
				} else {
					api.getJiguangConfig().then(res => {
						//初始化
						JIM.init({
							"appkey": res.data.appkey,
							"random_str": res.data.random_str,
							"signature": res.data.signature,
							"timestamp": res.data.timestamp,
							"flag": res.data.flag
						}).onSuccess(function (data) {
							console.log('JIMInitSuccess')
							that.loginJIM(userInfo).then(res => {
								resolve("JIMInitSuccess");
							});
						}).onFail(function (data) {
							console.log('JIMInitFail')
							console.log(data)
							uni.showModal({
								title: '极光初始化失败',
								content: data.message,
								success() { },
								complete() {

								}
							});
							resolve("极光初始化失败suceess");
						});
						//
						JIM.onDisconnect(function () {
							console.log('JIM断开链接')
						});

						JIM.onMsgReceive(function (data) {
							// 接受在线消息
							console.log('在线接受消息')
							console.log(data)
							//更新消息未读数
							that.getJIMUnreadMsgCnt()
							uni.$emit('msg_ol', data.messages[0].content)
						});

						//JIM.isInit();// 无回调函数，调用则成功
						Vue.prototype.onSyncConversation = null
						uni.$once('onSyncConversation', function (data) {
							that.onSyncConversation = data
							console.log('离线传递：')
							console.log(data)
							uni.$off()
						})
					});
				}
			});
		},
		//极光登录
		loginJIM(userInfo) {
			let that = this
			let username = userInfo.id
			let password = userInfo.id
			return new Promise((resolve, reject) => {
				let JIM = that.globalData.JIM
				if (JIM.isLogin()) {
					console.log("极光IM已经登录，无需再登录");
					resolve("success");
					return
				};
				//登录
				JIM.login({
					'username': username,
					'password': password
				}).onSuccess(function (data) {
					console.log("极光IM登录成功");
					that.updataJIMUserInfo(userInfo).then(res => {
						resolve("success");
					});
					//更新消息未读数
					that.getJIMUnreadMsgCnt()
				}).onFail(function (data) {
					//如果用户不存在，则注册账号
					if (data.code == 880103) {
						let nickname = userInfo.nickName
						JIM.register({
							'username': username,
							'password': password,
							'nickname': nickname,
							'extras': {
								'avatar': userInfo.headimgUrl
							}
						}).onSuccess(function (data) {
							console.log("极光IM注册成功");
							//登录
							JIM.login({
								'username': username,
								'password': password
							}).onSuccess(function (data) {
								console.log("极光IM登录成功");
								that.updataJIMUserInfo(userInfo).then(res => {
									resolve("success");
								});
								//更新消息未读数
								that.getJIMUnreadMsgCnt()
							}).onFail(function (data) {
								// uni.showModal({
								// 	title: '极光IM登录失败',
								// 	content: data.message,
								// 	success() {},
								// 	complete() {

								// 	}
								// });
								console.log(data)
								resolve("success");
							});
						}).onFail(function (data) {
							console.log('JIM -注册失败')
							console.log(data)
							// uni.showModal({
							// 	title: '极光IM注册失败',
							// 	content: data.message,
							// 	success() {},
							// 	complete() {

							// 	}
							// });
							resolve("success");
						});
					} else if (data.code == 880109 || data.code == 880107) {
						console.log(data)
					} else {
						// uni.showModal({
						// 	title: '极光IM登录失败',
						// 	content: data.message,
						// 	success() {},
						// 	complete() {

						// 	}
						// });
						console.log('极光IM登录失败')
						console.log(data)
						console.log('username,password：' + username + ',' + password)
						resolve("success");
					}
				});
			});
		},
		//获取极光未读数
		getJIMUnreadMsgCnt() {
			let JIM = this.globalData.JIM
			JIM.getConversation().onSuccess(function (data) {
				let conversation = data.conversations.reverse()
				let count = 0
				for (var i = 0; i < conversation.length; i++) {
					count = count + conversation[i].unread_msg_count
				}
				//设置tabbar未读数量
				uni.setTabBarBadge({
					index: 2,
					text: count + ''
				});
			}).onFail(function (data) {
				console.log('JIM fail:' + data)
			});
		},
		//更新极光IM用户信息
		updataJIMUserInfo(userInfo) {
			let that = this
			let username = userInfo.id
			return new Promise((resolve, reject) => {
				let JIM = that.globalData.JIM;
				JIM.getUserInfo({
					'username': username,
				}).onSuccess(function (data) {
					//data.code 返回码
					//data.message 描述
					//data.user_info.username
					//data.user_info.appkey
					//data.user_info.nickname
					//data.user_info.avatar 头像
					//data.user_info.birthday 生日，默认空
					//data.user_info.gender 性别 0 - 未知， 1 - 男 ，2 - 女
					//data.user_info.signature 用户签名
					//data.user_info.region 用户所属地区
					//data.user_info.address 用户地址
					//data.user_info.mtime 用户信息最后修改时间
					//data.extras 自定义json字段
					console.log('获取IM用户信息成功')
					let nickname = userInfo.nickName
					if (data.user_info.nickname != nickname || data.user_info.extras.avatar != userInfo.headimgUrl) {
						console.log('更新JIM用户信息')
						JIM.updateSelfInfo({
							'nickname': nickname,
							'extras': {
								'avatar': userInfo.headimgUrl
							}
						}).onSuccess(function (data) {
							//data.code 返回码
							//data.message 描述
							resolve("success");
						}).onFail(function (data) {
							console.log('updataJIMUserInfo fail:' + data)
							resolve("success");
						});
					} else {
						resolve("success");
					}
				}).onFail(function (data) {
					//data.code 返回码
					//data.message 描述
					resolve("success");
				});
			});
		},
		checkUpdateByMp() {
			console.log("更新用户信息")
			let code, state;

			// #ifdef MP-WEIXIN
			// 微信小程序环境下，通过onLoad传入的options获取参数
			let pages = getCurrentPages();
			let currentPage = pages[pages.length - 1];
			if (currentPage && currentPage.options) {
				code = currentPage.options.code;
				state = currentPage.options.state;
			}
			// #endif

			// #ifdef H5
			// H5环境下通过location.href获取参数
			let local = location.href;
			code = util.getUrlParam(local, "code");
			state = util.getUrlParam(local, "state");
			// #endif

			if (code && state == 'snsapi_userinfo') { //有code
				api.userInfoUpdateByMp({
					jsCode: code,
					scope: state
				}).then(res => {
					//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
					console.log("重新设置地址")
					// #ifdef H5
					// 仅在H5环境执行resetPageUrl
					util.resetPageUrl({});
					// #endif

					let userInfo = uni.getStorageSync('user_info');
					userInfo.headimgUrl = res.data.headimgUrl;
					userInfo.nickName = res.data.nickName;
					uni.setStorageSync('user_info', userInfo);
					return;
				}).catch(res => {

				});
			}
			console.log("用户信息", uni.getStorageSync('user_info'))
			console.log("微信", util.isWeiXinBrowser())

			// #ifdef H5
			// 仅在H5环境下执行微信公众号相关逻辑
			if (util.isWeiXinBrowser()) {
				//微信公众号H5，页面授权获取用户详情信息
				let local = location.href
				let appId = util.getUrlParam(local, "app_id");
				let componentAppId = util.getUrlParam(local, "component_appid");
				let pages = getCurrentPages();
				let currentPage = pages[pages.length - 1];
				let route = currentPage.route;
				let redirectUri = location.href;
				let componentAppId_str = componentAppId ? '&component_appid=' + componentAppId : '';
				redirectUri = encodeURIComponent(redirectUri);
				let wx_url = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' + appId +
					'&redirect_uri=' + redirectUri + componentAppId_str +
					'&response_type=code&scope=snsapi_userinfo&state=snsapi_userinfo#wechat_redirect';
				location.href = wx_url;
			}
			// #endif
		}
	}
};

</script>
<style>
@import "./app.css";
</style>
