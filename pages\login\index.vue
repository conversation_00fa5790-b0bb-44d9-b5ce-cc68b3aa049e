<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">登录</block>
		</cu-custom>
		<view class="nav text-center login padding-top" style="" :class="'bg-'+theme.backgroundColor">
			<view :class="'cu-item '+ (2==TabCur?'text-white cur':'')" @click="tabSelect(2)" class="wechat-auth-login">
				<text class="">微信授权</text>
			</view>
			<view :class="'cu-item '+ (1==TabCur?'text-white cur':'')" @click="tabSelect(1)" class="quick-login">
				<text class="">快速登录</text>
			</view>
			<view :class="'cu-item '+ (0==TabCur?'text-white cur':'')" @click="tabSelect(0)">
				<text class="">账号登录</text>
			</view>
		</view>
		<!-- 微信授权登录 -->
		<view v-if="TabCur===2" class="wechat-auth-container">
			<view class="auth-tip">
				<text class="tip-text">点击下方按钮跳转到微信授权登录页面</text>
			</view>
			<view class="auth-button-wrapper">
				<button class="wechat-auth-btn" :class="'bg-'+theme.backgroundColor" @click="goToWechatAuth">
					微信快速授权登录
				</button>
			</view>
		</view>
		<userLogin v-if="TabCur===0" :reUrl="reUrl"></userLogin>
		<codeLogin v-if="TabCur===1" :reUrl="reUrl"></codeLogin>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import userLogin from "./userlogin";
	import codeLogin from "./codelogin";
	import register from "./register";
	
	export default {
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				CustomBar: this.CustomBar,
				TabCur: 2, // 默认显示微信授权登录
				reUrl: ''
			};
		},
		components: {
			userLogin,
			codeLogin,
			register
		},
		props: {
			
		},
		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let reUrl = options.reUrl
			this.reUrl = reUrl
		},

		onShow() {
		},

		methods: {
			tabSelect(index){
				this.TabCur = index
			},

			// 跳转到微信授权登录页面
			goToWechatAuth() {
				uni.navigateTo({
					url: `/pages/login/wechat-auth${this.reUrl ? '?reUrl=' + encodeURIComponent(this.reUrl) : ''}`
				});
			}
		}
	};
</script>
<style>
	.wechat-auth-login{
		margin-right: 100rpx !important;
	}

	.quick-login{
		margin-right: 100rpx !important;
	}

	.login{
		margin-left: -20rpx;
	}

	.wechat-auth-container {
		padding: 60rpx 40rpx;
	}

	.auth-tip {
		text-align: center;
		margin-bottom: 40rpx;
	}

	.tip-text {
		font-size: 28rpx;
		color: #666;
	}

	.auth-button-wrapper {
		display: flex;
		justify-content: center;
	}

	.wechat-auth-btn {
		width: 80%;
		height: 88rpx;
		color: white;
		border-radius: 44rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
