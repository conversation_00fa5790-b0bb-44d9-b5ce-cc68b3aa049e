<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<!-- 分享给好友组件  -->
<template>
	<view >
		<view >
			<!-- #ifdef MP -->
			<button class="cu-btn bg-green lg round shadow-blur" style="width: 100%;" open-type="share">发送给朋友</button>
			<!-- #endif -->
			<!-- #ifndef MP -->
			<button class="cu-btn bg-green lg round shadow-blur" style="width: 100%;" @tap="onShareWX">分享给微信好友</button>
			<!-- #endif -->
		</view>
		<view v-if="showModal" class="cu-modal" :class="showModal?'show':''" @click="showModal = false">
			<image  style="right: 0;top: 0;width: 50%;margin-left: 20%;" src="/static/public/img/share_friends.jpg"></image>
		</view>
	</view>
</template>

<script>

	import jweixin from '@/utils/jweixin.js'
	import api from 'utils/api'
	import util from '@/utils/util'
	import __config from '@/config/env';

	export default {
		props: {
			shareObj: { //为空则不初始化
				type: Object,
				default: () => {}
			},
			curLocalUrl: {
				type: String,
			},
			shareShow: {
				type: String,
			}
		},
		watch:{
			shareObj(val,oldVal){
				this.shareObjTemp = val;
				
			},
			curLocalUrl(val,oldVal){
				this.initShareWx();
			},
		},
		data() {
			return {
				showModal: false,
				isInitShare: false,
				shareObjTemp: {}
			};
		},
		created() {
			this.shareObjTemp = this.shareObj;
			this.initShareWx();
		},
		mounted(){
				this.initShareWx();
		},
		methods: {
			initShareWx(){
				// h5页面加载时 会默认初始化分享
				let that = this;
				// #ifdef H5
				if(util.isWeiXinBrowser()&&this.shareObjTemp.imgUrl&&!this.isInitShare){
					this.isInitShare = true
					var url = this.shareObjTemp.url ? this.shareObjTemp.url : util.setH5ShareUrl();
					console.log("初始化微信去配置",url)
					if(this.shareObjTemp){
						api.getJsSdkConfig({url: url}).then(res => {
							history.replaceState(history.state, null, url); 
							let wxConfig = res.data;
							let shareObjTemp = {
								title: this.shareObjTemp.title,
								desc: this.shareObjTemp.desc,
								link: wxConfig.url,
								imgUrl:  this.shareObjTemp.imgUrl,
							};
							console.log("初始化微信分享2",wxConfig,shareObjTemp)
							jweixin.shareWxFriend(wxConfig, shareObjTemp, function(){
								// that.showModal = true;
								// uni.hideLoading();
							},function(e){
								console.log(e)
							},);
						}).catch(res=>{
							console.log('调用getJsSdkConfig失败：'+res)
						});
					}
				}
				// #endif
			},
			onShareWX(){ //H5 分享微信好友
				let that = this;
				// #ifdef H5
				if(!this.shareObjTemp){
					this.showModal = true;
					return
				}
				if(this.shareObjTemp.imgUrl){
					uni.showLoading();
					var url = this.shareObjTemp.url ? this.shareObjTemp.url : util.setH5ShareUrl();
					api.getJsSdkConfig({url: url}).then(res => {
						history.replaceState(history.state, null, url); 
						let wxConfig = res.data;
						let shareObjTemp = {
							title: this.shareObjTemp.title,
							desc: this.shareObjTemp.desc,
							link: wxConfig.url,
							imgUrl:  this.shareObjTemp.imgUrl,
						};
						jweixin.shareWxFriend(wxConfig, shareObjTemp, function(){
							that.showModal = true;
							uni.hideLoading();
						},function(e){
							uni.hideLoading();
						},);
					}).catch(res=>{
						that.showModal = false;
						uni.showToast({
							icon: 'none',
							title: '分享失败 '+res,
						});
						uni.hideLoading();
					});
				}
				// #endif
				// #ifdef APP-PLUS
				// wxapp
				api.wxAppConfig(__config.wxAppId).then(res => {
					if(res.data.data&&res.data.data.isComponent=='1') {
						let url = this.shareObjTemp.url ? this.shareObjTemp.url : util.setAppPlusShareUrl(res.data.data);
						this.shareWxByApp(url)
					}else{
						let url = this.shareObjTemp.url ? this.shareObjTemp.url : util.setAppPlusShareUrl();
						this.shareWxByApp(url)
					}
				});
				// #endif
			},
			shareWxByApp(url){
				// that.showModal = true;
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession",
					type: 0,
					href: url,
					title: this.shareObjTemp.title,
					summary: this.shareObjTemp.desc,
					imageUrl: this.shareObjTemp.imgUrl,
					success: function (res) {
						// that.showModal = false;
					},
					fail: function (err) {
						// that.showModal = false;
					}
				});
			}
		}
	};
</script>
<style>
</style>
