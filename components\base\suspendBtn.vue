<template>
	<!-- 悬浮按钮组件 -->
	<view>
		<view style="color: #000000;">
			<uni-fab :pattern="{
				color:'#f37b1d',
				selectedColor:'',
				backgroundColor:'white',
				buttonColor:newData.buttonColor,
			}"
			:content="newData.content" :horizontal="newData.horizontal" :vertical="newData.vertical"
				:direction="newData.direction" @trigger="trigger" @fabClick="fabClick"  :popMenu="true"></uni-fab>
		</view>
		<uni-popup ref="popup" type="dialog" @touchmove.stop.prevent="moveHandle">
				<uni-popup-dialog v-if="popupType == 1" mode="base" message="成功消息" :content="popupContent" :duration="2000"
					:before-close="true" @close="close" @confirm="confirm"></uni-popup-dialog>
				<view v-if="popupType == 2" style="width:550rpx" @click="close()">
					<image style="width: 100%;display: block;" mode="widthFix" :src="popupImg"></image>
				</view>
				<!-- 订单信息 -->
				<view v-if="popupType == 3" style="width:600rpx" @click="close()">
					<view class="cu-card">
						<view class="cu-item">
							<!-- {{options}} -->
							<view class="solid-bottom text-xxl padding text-center">
								<text class="cuIcon-roundcheckfill text-green"> 支付成功</text>
							</view>
							<view class="margin-left-sm margin-top flex">
								<text class="margin-left flex-sub text-sm">订单名称</text>
								<view class="flex-twice text-sm text-gray">{{order.name}}</view>
							</view>
							<view class="margin-left-sm margin-top flex">
								<text class="margin-left flex-sub text-sm">订单编号</text>
								<view class="flex-twice text-sm text-gray">{{order.orderNo}}</view>
							</view>
							<view class="margin-left-sm margin-top flex">
								<text class="margin-left flex-sub text-sm">付款金额</text>
								<view class="flex-twice text-sm text-gray">{{order.paymentPrice?order.paymentPrice:'0.00'}}</view>
							</view>					
							<view class="margin-left-sm margin-top flex" v-if="order.paymentTime">
								<text class="margin-left flex-sub text-sm">付款时间</text>
								<view class="flex-twice text-sm text-gray">{{order.paymentTime}}</view>
							</view>
							<view class="padding flex flex-direction">
								<!-- <button class="cu-btn margin-tb-sm lg goBackButton " @tap="toBack()">返回</button> -->
							</view>
						</view>
					</view>
				</view>
			</uni-popup>
		
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'

	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
					}
				}
			}
		},

		created() {
		},
		mounted() {
		
		},
		watch: {

		},
		data() {
			return {
				pattern: {
					color:"#f37b1d",
					selectedColor:"",
					backgroundColor:"red",
					buttonColor:"#f37b1d",
				},
				newData: this.value,
				popupContent: '', //弹出框内容
				popupImg: '', //弹出框内容
				popupType: 0, //弹出框类型
			};
			
		},
		methods: {
			trigger(obj){
				console.log("点击的",obj);
				if (obj.item.button.type === 1) {
					this.jumpUrl(obj.item.button);
				} else if (obj.item.button.type === 2) {
					this.phone(obj.item.button);
				} else if (obj.item.button.type === 3) {
					this.showDialoag(obj.item.button);
				}
			},
			//悬浮按钮点击事件
			fabClick(){
				// console.log("121额额2啊时");
			},
			//页面跳转
			jumpUrl(obj) {
				console.log("页面跳转", obj)
				if (!obj.pageUrl) {
					return;
				}
				if (obj.isSystemUrl) {
					uni.navigateTo({
						url: obj.pageUrl
					}).then(()=>{
						location.reload() 
					});
				} else {
					window.location.href = obj.pageUrl;
				}
			},
			//弹窗显示
			showDialoag(obj) {
				console.log("弹窗显示", obj)
				this.popupType = obj.popupType;
				// 通过组件定义的ref调用uni-popup方法 ,如果传入参数 ，type 属性将失效 ，仅支持 ['top','left','bottom','right','center']
				this.popupContent = obj.content;
				this.popupImg = obj.imgUrl;
				this.$refs.popup.open('center')
			},
			//电话号码
			phone(obj) {
				console.log("电话号码", obj)
				uni.makePhoneCall({
					// 手机号
					phoneNumber: obj.phone,
					// 成功回调
					success: (res) => {},
					// 失败回调
					fail: (res) => {}
				});
			},
			//弹窗显示
			showDialoag(obj) {
				console.log("弹窗显示", obj)
				this.popupType = obj.popupType;
				// 通过组件定义的ref调用uni-popup方法 ,如果传入参数 ，type 属性将失效 ，仅支持 ['top','left','bottom','right','center']
				this.popupContent = obj.content;
				this.popupImg = obj.imgUrl;
				this.$refs.popup.open('center')
			
			},
			close() {
				this.$refs.popup.close()
			},
			confirm() {
				this.$refs.popup.close()
			},
		}
	}
</script>

<style scoped lang="scss">
	.img-category {
		width: 100rpx;
		height: 100rpx;
	}

	.img-category-banner {
		width: 100%;
		height: 180rpx;
	}

	.goods_cover {
		animation: hide 1.5s;
	}


	@keyframes hide {
		from {
			opacity: 0;
			top: 100px;
			transform: translateY(10%)
		}

		to {
			opacity: 1;
			top: -100px;
			transform: translateY(00%)
		}
	}
</style>
