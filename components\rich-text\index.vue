<template>
		<view class="ql-editor" >
			<jyf-parser  :html="newData.content"></jyf-parser>
		</view>
</template>
<script>
	const app = getApp();
    export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {}
	            }
            }
	    },
	    components: {},
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value
			};
		},
		methods: {}
    }
</script>

<style scoped lang="scss">

</style>
