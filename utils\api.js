/**
 * Copyright (C) 2018-2019
 * All rights reserved, Designed By www.joolun.com
 * 注意：
 * 本软件为www.joolun.com开发研制，未经购买不得使用
 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
import __config from 'config/env';
import util from 'utils/util';

const request = (url, method, data, showLoading) => {
	let _url = url;
	//#ifndef H5
	_url = __config.basePath + url

	//#endif
	return new Promise((resolve, reject) => {
		if (showLoading) {
			uni.showLoading({
				title: '加载中'
			});
		}
		// console.log("发送请求",url,uni.getStorageSync('third_session'))
		console.log("发送请求appId",getApp().globalData)
		let maClientType='MA'
		if (url.includes('goodsspuspec/tree')||url.includes('mallapi/goodsspu/')){
			maClientType="H5"
		}
		uni.request({
			url: _url,
			method: method,
			data: data,
			withCredentials: true,
			header: {
				//#ifdef H5
				'client-type': util.isWeiXinBrowser() ? 'H5-WX' : 'H5', //客户端类型普通H5或微信H5
				'tenant-id': getApp().globalData.tenantId,
				'app-id': getApp().globalData.appId ? getApp().globalData.appId : '', //微信h5有appId
				//#endif
				//#ifdef MP-WEIXIN
				'client-type': maClientType, //客户端类型小程序
				'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
				'tenant-id': getApp().globalData.tenantId,
				//#endif
				//#ifdef APP-PLUS
				'client-type': 'APP', //客户端类型APP
				'tenant-id': getApp().globalData.tenantId,
				//#endif
				'third-session': uni.getStorageSync('third_session') ? uni.getStorageSync(
					'third_session') : '',
			},
			success(res) {
				if (res.statusCode == 200) {
					if (res.data.code != 0) {
						// 微信授权登录页面处理
						if (res.data.code == 60001 || res.data.code == 60002 || res.data.code ==
							60003) {
							//session过期，则清除过期session，并跳转到微信授权登录页面
							if (!getApp().globalData.logining) {
								getApp().globalData.logining = true //防止同时多个接口触发登录
								if (util.isMiniPg() || (getApp().globalData.appId && util
										.isWeiXinBrowser())) {
									//小程序或公众号H5，删除third_session并跳转到微信授权登录页面
									uni.removeStorageSync('third_session');

									// 获取当前页面信息用于登录后跳转
									let pages = getCurrentPages();
									let currPage = pages[pages.length - 1];
									let reUrl = '';

									if (currPage) {
										let curParam = currPage.options;
										reUrl = '/' + currPage.route;
										if (curParam != null) {
											let param = '';
											for (let key in curParam) {
												param += '&' + key + '=' + curParam[key];
											}
											param = param.substr(1);
											reUrl = reUrl + '?' + param;
										}
										reUrl = encodeURIComponent(reUrl);
									}

									// 跳转到微信授权登录页面
									uni.navigateTo({
										url: '/pages/login/wechat-auth' + (reUrl ? '?reUrl=' + reUrl : '')
									});
								} else {
									// 非微信环境，跳转到普通登录页面
									uni.reLaunch({
										url: '/pages/login/index'
									});
								}
								setTimeout(function() {
									getApp().globalData.logining = false;
								}, 2000);
							}
							reject("session过期，跳转到登录页面");
						} else if (res.data.code == 60002 || res.data.code == 60003) {
							var pages = getCurrentPages(); // 获取页面栈
							var currPage = pages[pages.length - 1]; // 当前页面
							if (currPage) {
								let curParam = currPage.options
								// 拼接参数
								let reUrl = '/' + currPage.route
								if (curParam != null) {
									// 拼接参数
									let param = ''
									for (let key in curParam) {
										param += '&' + key + '=' + curParam[key]
									}
									param = param.substr(1)
									reUrl = reUrl + '?' + param
									reUrl = encodeURIComponent(reUrl)
								}
								//此处 待用户体系后期优化
								// uni.reLaunch({
								// 	url: '/pages/login/index?reUrl='+reUrl
								// });
							}
							// reject("请先登录商城");
						} else {
							uni.showModal({
								title: '提示',
								content: res.data.msg + '',
								success() {},
								complete() {

								}
							});
							reject(res.data.msg);
						}
					}
					resolve(res.data);
				} else if (res.statusCode == 404) {
					uni.showModal({
						title: '提示',
						content: '接口请求出错，请检查手机网络',
						success(res) {}
					});
					reject();
				} else if (res.statusCode == 502) {
					console.log(502)
					uni.showModal({
						title: '提示',
						content: '服务器维护中，请稍后再来',
						success(res) {}
					});
					reject();
				} else if (res.statusCode == 503) {
					console.log(503)
					uni.showModal({
						title: '提示',
						content: '503错误，服务未启动',
						success(res) {}
					});
					reject();
				} else {
					console.log(res);
					if(res.data.msg != null && res.data.msg != undefined && res.data.msg != ""){
						uni.showModal({
							title: '提示',
							content: '错误：' + res.data.msg,
							success(res) {}
						});
					}
					reject();
				}
			},
			fail(error) {
				console.log(error);
				uni.showModal({
					title: '提示',
					content: '接口请求出错：' + error.errMsg,
					success(res) {}
				});
				reject(error);
			},
			complete(res) {
				uni.hideLoading();
			}
		});
	});
};

/**
 * 上传文件接口
 * @param {Object} filePath 本地文件路径
 * @param {Object} dir 上传保存的文件目录, 如头像保存的目录: headimg/
 * @param {Object} fileType 文件类型说明(没有强制要求) image/video/file/...
 */
function uploadFile(filePath, dir, fileType) {
	// 上传文件
	let _url = '/weixinapi/file/upload';
	//#ifndef H5
	_url = __config.basePath + _url;
	//#endif
	let that = this;
	uni.showLoading({
		title: '上传中'
	});
	return new Promise((resolve, reject) => {
		uni.uploadFile({
			header: {
				//#ifdef H5
				'client-type': util.isWeiXinBrowser() ? 'H5-WX' : 'H5', //客户端类型普通H5或微信H5
				'tenant-id': getApp().globalData.tenantId,
				'app-id': getApp().globalData.appId ? getApp().globalData.appId : '', //微信h5有appId
				//#endif
				//#ifdef MP-WEIXIN
				'client-type': 'MA', //客户端类型小程序
				'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
				//#endif
				//#ifdef APP-PLUS
				'client-type': 'APP', //客户端类型APP
				'tenant-id': getApp().globalData.tenantId,
				//#endif
				'third-session': uni.getStorageSync('third_session') ? uni.getStorageSync(
					'third_session') : '',
			},
			url: _url,
			filePath: filePath,
			name: 'file',
			formData: {
				fileType: fileType,
				dir: dir
			},
			success: (uploadFileRes) => {
				uni.hideLoading();
				if (uploadFileRes.statusCode == '200') {
					let link = JSON.parse(uploadFileRes.data).link;
					resolve(link)
				} else {
					uni.showModal({
						title: "提示",
						content: "上传失败:" + uploadFileRes.data,
						success(res) {}
					});
					reject()
				}
			},
			fail: (err) => {
				uni.hideLoading();
				console.log(err);
				reject()
			}
		});
	})
}

module.exports = {
	request,
	uploadFile,
	loginWxMa: data => {
		//如果有分享人则带上分享人的user_code
		data = util.dataAddSharerUserCode(data)
		//微信小程序登录接口
		return request('/weixinapi/wxuser/loginma', 'post', data, false);
	},
	loginWxMp: data => {
		//如果有分享人则带上分享人的user_code
		data = util.dataAddSharerUserCode(data)
		//微信公众号登录接口
		return request('/weixinapi/wxuser/loginmp', 'post', data, false);
	},
	jokelLoginWxMp: data => {
		//如果有分享人则带上分享人的user_code
		data = util.dataAddSharerUserCode(data)
		//微信公众号登录接口
		return request('/weixinapi/wxuser/joke/loginmp', 'post', data, false);
	},
	loginByPhoneMa: data => {
		//如果有分享人则带上分享人的user_code
		data = util.dataAddSharerUserCode(data)
		//商城通过小程序授权手机号一键登录商城
		return request('/weixinapi/wxuser/ma/phoneinfo', 'post', data, true);
	},
	loginByPhone: data => {
		//如果有分享人则带上分享人的user_code
		data = util.dataAddSharerUserCode(data)
		//商城手机验证码登录商城
		return request('/mallapi/userinfo/phone/login', 'post', data, true);
	},
	login: data => {
		//如果有分享人则带上分享人的user_code
		data = util.dataAddSharerUserCode(data)
		//商城账号登录
		return request('/mallapi/userinfo/login', 'post', data, true);
	},
	logout: data => {
		//商城退出登录
		return request('/mallapi/userinfo/logout', 'post', data, true);
	},
	getPhoneCode: data => {
		//获取手机验证码
		return request('/weixinapi/phone/code', 'get', data, true);
	},
	register: data => {
		//如果有分享人则带上分享人的user_code
		data = util.dataAddSharerUserCode(data)
		//账号注册
		return request('/mallapi/userinfo/register', 'post', data, true);
	},
	updateUserPhone: data => {
		//修改商城用户手机号
		return request('/mallapi/userinfo/phone', 'post', data, true);
	},

	themeMobileGet: () => {
		//获取商城主题装修配置
		return request('/mallapi/thememobile', 'get', null, false);
	},
	shopInfoPage: data => {
		//店铺列表
		return request('/mallapi/shopinfo/page', 'get', data, false);
	},
	shopInfoPageWithSpu: data => {
		//店铺列表带商品
		return request('/mallapi/shopinfo/pagewithspu', 'get', data, false);
	},
	shopInfoGet: id => {
		//店铺查询
		return request('/mallapi/shopinfo/' + id, 'get', null, false);
	},
	userInfoUpdateByMa: data => {
		//通过微信小程序更新用户信息
		return request('/mallapi/userinfo/ma', 'put', data, true);
	},
	userInfoUpdateByMp: data => {
		//通过微信公众号网页授权更新用户信息
		return request('/weixinapi/wxuser/mp', 'put', data, true);
	},
	goodsCategoryTree: data => {
		//商城商品分类tree查询
		return request('/mallapi/goodscategory/tree', 'get', data, true);
	},
	goodsCategoryPage: data => {
		//商品标签分类list查询
		return request('/wxapi/goodscategory/page', 'get', data, true);
	},
	goodsCategoryShopTree: data => {
		//店铺商品分类tree查询
		return request('/mallapi/goodscategoryshop/tree', 'get', data, true);
	},
	goodsPage: data => {
		//商品列表
		return request('/mallapi/goodsspu/page', 'get', data, false);
	},
	
	goodsSpuGet: id => {
		//商品详情查询
		return request('/mallapi/goodsspu/' + id, 'get', null, false);
	},
	
	goodsSpuSpecTree: spuId => {
		//商品规格树查询
		return request('/mallapi/goodsspuspec/tree?spuId=' + spuId, 'get', null, false);
	},

	goodsSpecGet: data => {
		//商品规格查询
		return request('/mallapi/goodsspuspec/tree', 'get', data, true);
	},
	shoppingCartPage: data => {
		//购物车列表
		return request('/mallapi/shoppingcart/page', 'get', data, false);
	},
	shoppingCartAdd: data => {
		//购物车新增
		return request('/mallapi/shoppingcart', 'post', data, true);
	},
	shoppingCartUpdate: data => {
		//购物车修改
		return request('/mallapi/shoppingcart', 'put', data, true);
	},
	shoppingCartDel: data => {
		//购物车删除
		return request('/mallapi/shoppingcart/del', 'post', data, false);
	},
	shoppingCartCount: data => {
		//购物车数量
		return request('/mallapi/shoppingcart/count', 'get', data, false);
	},
	orderSub: data => {
		//订单提交
		return request('/mallapi/orderinfo', 'post', data, true);
	},
	orderPage: data => {
		//订单列表
		return request('/weixinapi/orderinfo/page', 'get', data, false);
	},
	orderGet: id => {
		//订单详情查询
		return request('/weixinapi/orderinfo/' + id, 'get', null, false);
	},
	orderReceiptGet: data => {
		//订单小票查询
		return request('/weixinapi/orderinfo/receipt', 'post', data, false);
	},
	orderCancel: id => {
		//订单确认取消
		return request('/mallapi/orderinfo/cancel/' + id, 'put', null, true);
	},
	orderReceive: id => {
		//订单确认收货
		return request('/mallapi/orderinfo/receive/' + id, 'put', null, true);
	},
	orderDel: id => {
		//订单删除
		return request('/mallapi/orderinfo/' + id, 'delete', null, false);
	},
	orderCountAll: data => {
		//订单计数
		return request('/mallapi/orderinfo/countAll', 'get', data, false);
	},
	orderLogisticsGet: logisticsId => {
		//订单物流查询
		return request('/mallapi/orderinfo/orderlogistics/' + logisticsId, 'get', null, false);
	},
	userAddressPage: data => {
		//用户收货地址列表
		return request('/mallapi/useraddress/page', 'get', data, false);
	},
	userAddressSave: data => {
		//用户收货地址新增
		return request('/mallapi/useraddress', 'post', data, true);
	},
	userAddressDel: id => {
		//用户收货地址删除
		return request('/mallapi/useraddress/' + id, 'delete', null, false);
	},
	userCollectAdd: data => {
		//用户收藏新增
		return request('/mallapi/usercollect', 'post', data, true);
	},
	userCollectDel: id => {
		//用户收藏删除
		return request('/mallapi/usercollect/' + id, 'delete', null, false);
	},
	userCollectPage: data => {
		//用户收藏列表
		return request('/mallapi/usercollect/page', 'get', data, false);
	},
	userFootprintPage: data => {
		//用户足迹列表
		return request('/mallapi/userfootprint/page', 'get', data, false);
	},
	goodsAppraisesAdd: data => {
		//商品评价新增
		return request('/mallapi/goodsappraises', 'post', data, true);
	},
	goodsAppraisesPage: data => {
		//商品评价列表
		return request('/mallapi/goodsappraises/page', 'get', data, false);
	},
	qrCodeUnlimited: data => {
		//获取小程序二维码
		return request('/mallapi/wxqrcode/unlimited', 'post', data, true);
	},
	orderItemGet: id => {
		//查询订单详情
		return request('/weixinapi/orderitem/' + id, 'get', null, false);
	},
	orderRefundsSave: data => {
		//发起退款
		return request('/weixinapi/orderrefunds', 'post', data, true);
	},
	userInfoGet: () => {
		//查询商城用户信息
		return request('/weixinapi/wxuser/userinfo', 'get', null, false);
	},
	userInfoUpdate: (data) => {
		//修改商城用户
		return request('/weixinapi/wxuser/userinfo', 'put', data, true);
	},
	pointsRecordPage: data => {
		//查询积分记录
		return request('/mallapi/pointsrecord/page', 'get', data, false);
	},
	pointsConfigGet: () => {
		//查询积分配置
		return request('/mallapi/pointsconfig', 'get', null, false);
	},
	couponUserSave: data => {
		//电子券用户领取
		return request('/mallapi/couponuser', 'post', data, true);
	},
	couponUserPage: data => {
		//电子券用户列表
		return request('/mallapi/couponuser/page', 'get', data, false);
	},
	couponInfoPage: data => {
		//电子券列表
		return request('/mallapi/couponinfo/page', 'get', data, false);
	},
	bargainInfoPage: data => {
		//砍价列表
		return request('/mallapi/bargaininfo/page', 'get', data, false);
	},
	bargainInfoGet: data => {
		//砍价详情
		return request('/mallapi/bargaininfo', 'get', data, true);
	},
	bargainUserSave: data => {
		//发起砍价
		return request('/mallapi/bargainuser', 'post', data, true);
	},
	bargainCutPage: data => {
		//帮砍记录
		return request('/mallapi/bargaincut/page', 'get', data, true);
	},
	bargainUserGet: id => {
		//砍价记录详情
		return request('/mallapi/bargainuser/' + id, 'get', null, false);
	},
	bargainUserPage: data => {
		//砍价记录列表
		return request('/mallapi/bargainuser/page', 'get', data, true);
	},
	bargainCutSave: data => {
		//砍一刀
		return request('/mallapi/bargaincut', 'post', data, true);
	},
	listEnsureBySpuId: data => {
		//通过spuID，查询商品保障
		return request('/mallapi/ensuregoods/listEnsureBySpuId', 'get', data, true);
	},
	grouponInfoPage: data => {
		//拼团列表
		return request('/mallapi/grouponinfo/page', 'get', data, false);
	},
	grouponInfoGet: id => {
		//拼团详情
		return request('/mallapi/grouponinfo/' + id, 'get', null, true);
	},
	grouponUserPageGrouponing: data => {
		//拼团中分页列表
		return request('/mallapi/grouponuser/page/grouponing', 'get', data, true);
	},
	grouponUserPage: data => {
		//拼团记录列表
		return request('/mallapi/grouponuser/page', 'get', data, true);
	},
	grouponUserGet: id => {
		//拼团记录详情
		return request('/mallapi/grouponuser/' + id, 'get', null, false);
	},

	seckillhallList: date => {
		//秒杀会场时间列表
		return request('/mallapi/seckillhall/list?hallDate=' + date, 'get', null, false);
	},
	seckillinfoPage: data => {
		//秒杀列表
		return request('/mallapi/seckillinfo/page', 'get', data, false);
	},
	seckillInfoGet: (seckillHallInfoId) => {
		//秒杀详情
		return request('/mallapi/seckillinfo/' + seckillHallInfoId, 'get', null, true);
	},
	wxTemplateMsgList: data => {
		//订阅消息列表
		return request('/mallapi/wxtemplatemsg/list', 'post', data, false);
	},
	liveRoomInfoList: data => {
		//获取直播房间列表
		return request('/mallapi/wxmalive/roominfo/list', 'get', data, true);
	},
	customerServiceList: shopId => {
		//客服列表
		return request('/mallapi/customerservice/list/' + shopId, 'get', null, false);
	},

	pagedeviseShop: shopId => {
		// 店铺组件配置
		return request('/mallapi/pagedevise?pageType=2&shopId=' + shopId, 'get', null, false);
	},
	getJiguangConfig: data => {
		//获取极光参数
		return request('/mallapi/jiguang/config', 'get', data, false);
	},
	getJiguangMessages: data => {
		//获取极光消息
		return request('/mallapi/jiguang/messages?userName=' + data.userName + '&beginTime=' + data.beginTime +
			'&endTime=' + data.endTime, 'get', null, false);
	},
	articlePage: data => {
		//文章列表
		return request('/mallapi/articleinfo/page', 'get', data, false);
	},
	articleGet: id => {
		//文章详情
		return request('/mallapi/articleinfo/' + id, 'get', null, false);
	},
	articleCategoryPage: data => {
		//文章分类列表
		return request('/mallapi/articlecategory/page', 'get', data, false);
	},
	userSign: data => {
		//积分签到
		return request('/mallapi/signrecord/user', 'post', data, false);
	},
	getSignRecord: data => {
		//签到记录查询
		return request('/mallapi/signrecord/user', 'get', data, false);
	},
	signConfigPage: data => {
		//获取签到积分记录
		return request('/mallapi/signconfig/page', 'get', data, false);
	},
	distributionConfig: () => {
		//分销设置查询
		return request('/mallapi/distributionconfig', 'get');
	},
	distributionuser: () => {
		//分销员查询
		return request('/mallapi/distributionuser', 'get');
	},
	distributionuserPage: (data) => {
		//分销员查询
		return request('/mallapi/distributionuser/page', 'get', data);
	},
	distributionorderPage: data => {
		//分销订单分页
		return request('/mallapi/distributionorder/page', 'get', data);
	},
	distributionorderFrozenAmount: data => {
		//获取当前分销员的冻结金额
		return request('/mallapi/distributionorder/frozenAmount', 'get', data);
	},
	distributionPromotionPage: data => {
		//分销 推广人统计
		return request('/mallapi/userinfo/page', 'get', data);
	},
	userwithdrawRecordSave: data => {
		//分销 申请提现
		return request('/mallapi/userwithdrawrecord', 'post', data, true);
	},
	userwithdrawRecordUpdate: data => {
		//分销 提现申请修改
		return request('/mallapi/userwithdrawrecord', 'put', data, true);
	},
	userwithdrawRecordPage: data => {
		//分销 提现记录
		return request('/mallapi/userwithdrawrecord/page', 'get', data);
	},
	userwithdrawRecord: id => {
		//分销 提现记录详情
		return request('/mallapi/userwithdrawrecord/' + id, 'get', null);
	},
	userRecord: () => {
		//分销 用户消费总额记录
		return request('/mallapi/userrecord', 'get', null);
	},
	wxAppConfig: id => {
		//获取wx一件授权的component_appid
		return request('/mallapi/wxapp/' + id, 'get', null);


	},
	pagedevise: data => {
		//组件页配置
		return request('/weixinapi/wxpagedevise/show', 'get', data, false);
	},
	pageDeviseFreedom: data => {
		//组件页配置
		return request('/weixinapi/wxpagedevise/freedom/show', 'get', data, false);
	},
	goodsGet: id => {
		//商品查询
		return request('/weixinapi/goods/' + id, 'get', null, false);
	},
	goodsGetFreedom: id => {
		//商品查询
		return request('/weixinapi/goods/freedom/getById?id=' + id, 'get', null, false);
	},

	goodsCover: data => {
		//组件页配置
		return request('/weixinapi/goods/page', 'get', data, false);
	},
	goodsByTag: data => {
		//通过商品标签加载商品
		return request('/weixinapi/goods/tagpage', 'get', data, false);
	},
	goodsByTagFreedom: data => {
		//通过商品标签加载商品
		return request('/weixinapi/goods/freedom/tagpage', 'get', data, false);
	},
	goodsCoverByType: data => {
		//通过商品标签类别加载商品
		return request('/weixinapi/goods/tagpage/type', 'get', data, false);
	},
	goodsCoverByTypeFreedom: data => {
		//通过商品标签类别加载商品
		return request('/weixinapi/goods/freedom/tagpage/type', 'get', data, false);
	},
	submitFormData: data => {
		//提交表单
		return request('/weixinapi/wxformdata/submit', 'post', data, true);
	},
	getSpellGroup: data => {
		//查询活动信息
		return request('/weixinapi/wxspellgroup/info', 'get', data);
	},
	joinSpellGroup: data => {
		//参加拼团活动
		return request('/weixinapi/wxspellgroup/join', 'post', data);
	},
	wxunifiedOrder: data => {
		//下单接口
		return request('/weixinapi/orderinfo/wxunifiedOrder', 'post', data, true);
	},
	getOrderUser: id => {
		//拼团已购用户
		return request('/weixinapi/wxspellgroup/orderuser/' + id, 'get', null);
	},
	getUserNum: id => {
		//用户进入数量和购买数量
		return request('/weixinapi/wxspellgroup/usernum/' + id, 'get', null);
	},
	imgShareBindPage: id => {
		//客片分享的绑定页面
		return request('/weixinapi/imgsharebindpage/' + id, 'get', null);
	},
	becomeSignUser: data => {
		//保存签约用户
		return request('/weixinapi/imgshareuser/save', 'post', data, true);
	},
	loadImgShareData: data => {
		//加载客片分享数据
		return request('/weixinapi/imgshareuser/info', 'get',data, null);
	},
	imgShareVote: data => {
		//加载客片分享投票数据
		return request('/weixinapi/voterecord/vote', 'post', data, null);
	},
	getVoteList: id => {
		//得到客票投票人数信息
		return request('/weixinapi/voterecord/votelist/'+id, 'get', null);
	},
	getGoodsTagType: () => {
		//商品标签分类list查询
		return request('/weixinapi/wxgoodstagtype/list', 'get', null);
	},
	getRecommendGoods: (data) => {
		//商品甄选加载五个
		return request('/weixinapi/goods/recommend', 'get', data, false);
	},
	getRecommendGoodsFreedom: (data) => {
		//商品甄选加载五个
		return request('/weixinapi/goods/freedom/recommend', 'get', data, false);
	},
	getJsSdkConfig: data => {
		//获取jssdk config参数
		return request('/weixinapi/wxjssdk/config', 'post', data, false);
	},
	createButtonOrder: data => {
		//创建按钮订单
		return request('/weixinapi/button/order', 'post', data, false);
	},
	getButtonOrder: id => {
		//查询按钮订单
		return request('/weixinapi/button/order/get/' + id, 'get');
	},
	getScheduleDate: (data) => {
		//查询某天档期
		return request('/weixinapi/auctioncalendar/oneday', 'get', data);
	},
	addAppoint: (data) => {
		//确定预约档期
		return request('/weixinapi/auctioncalendar', 'post', data);
	},
	resetAppoint: (data) => {
		//重选档期
		return request('/weixinapi/auctioncalendar/reset', 'post', data);
	},
	getAppointInfo: (data) => {
		//得到档期信息
		return request('/weixinapi/auctioncalendar/info', 'get', data, true);
	},
	wxCustomerServiceList: () => {
		//客服列表
		return request('/weixinapi/customerservice/list', 'get', null, false);
	},
	getPageIdByPayPageId: payPageId => {
		//根据支付页面ID获取约档页面ID
		return request('/weixinapi/auctioncalendar/getPageIdByPayPageId?payPageId=' + payPageId, 'get', null, false);
	},


};
