<!-- 进度条 -->
<template>
	<view class="progressComponent" :style="{marginBottom: `${newData.pageMarginBottom}px`,
	       marginLeft: `${newData.pageMarginLeft}px`,
	       marginRight: `${newData.pageMarginRight}px`,
	       marginTop: `${newData.pageMarginTop}px`}">
		<view :style="{
	     backgroundColor:`${newData.backColor}`,
	     paddingTop:`${newData.contentPaddingTop}px`,
	     paddingBottom:`${newData.contentPaddingBottom}px`,
	     paddingLeft:`${newData.contentPaddingLeft}px`,
	     paddingRight:`${newData.contentPaddingRight}px`,
	      }">
			<view class="cu-progress round" :style="{height: `${newData.size}px`}">
				<view class=" justify-center align-center"
					:style="[{ width:true?(shareData.voteNum/shareData.aimNum*100).toFixed(0)+'%':'',backgroundColor: newData.color}]">
					{{getProgress()}}%
				</view>
			</view>
			<view class="count_pre flex justify-end align-center">
				<view>当前进度：{{imgShareData.voteNum?imgShareData.voteNum:0}}/{{imgShareData.aimNum?imgShareData.aimNum:0}}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				shareData: this.imgShareData,
			};
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
			imgShareData: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		watch: {
			imgShareData(val, oldVal) {
				if (val != oldVal) {
					this.shareData = val;
				}
			}
		},
		computed: {

		},
		created() {},
		mounted() {},
		methods: {
			getProgress() {
				let value = (this.shareData.voteNum / this.shareData.aimNum * 100).toFixed(0);
				if (!value) {
					return 0;
				} else if (value >= 100) {
					return 100;
				}
				return value;
			}
		},

	};
</script>
<style>
	.count_pre {
		padding-top: 5px;
	}
</style>
