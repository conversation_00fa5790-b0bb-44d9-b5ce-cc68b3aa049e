<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="false">
			<block slot="backText">返回</block>
			<block slot="content">消息</block>
		</cu-custom>

		<view class="cu-list menu-avatar">
			<navigator :url="'/pages/message/chat/index?userId=' + item.username" hover-class="none" v-for="(item,index) in Conversation" :key="index" class="cu-item">
				<view class="cu-avatar round lg" :style="item.extras.avatar?'background-image:url(' + item.extras.avatar + ')':''">
					<text class="cuIcon-people" v-if="!item.extras.avatar"></text>
					<view v-if="item.unread_msg_count !=0" class="cu-tag badge">{{item.unread_msg_count}}</view>
				</view>
				<view class="content">
					<view class="text-gray">
						<view class="text-cut">{{item.nickName}}</view>
					</view>
				</view>
				<view class="action">
					<view class="text-gray text-xs">{{item.timess}}</view>
				</view>
			</navigator>
		</view>
		<view :class="'cu-load bg-gray ' + (loadmore?'loading':'over')"></view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				Conversation: null,
				loadmore: false,
				Time_now:{
					Date:'',
					Y:'',
					M:'',
					D:'',
					h:'',
					m:'',
					s:''
				}
			};
		},

		props: {},

		onShow() {
			//更新购物车tabar角标数量
			uni.setTabBarBadge({
				index: 3,
				text: app.globalData.shoppingCartCount + ''
			});
			if(!uni.getStorageSync('user_info').id){
				uni.reLaunch({
				    url: '/pages/login/index'
				});
			}
			app.initJIM().then(res => {
				this.Times_now();
				this.getConversation();
				app.getJIMUnreadMsgCnt()
			});
		},
		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
		},

		methods: {
			getConversation() {
				let that = this;
				let JIM = app.globalData.JIM;
				this.loadmore = true
				try{
					JIM.getConversation().onSuccess(function(data) {
						that.loadmore = false
						that.Conversation = data.conversations.reverse();
						//data.code 返回码
						//data.message 描述
						//data.conversations[] 会话列表，属性如下示例
						//data.conversations[0].extras 附加字段
						//data.conversations[0].unread_msg_count 消息未读数
						//data.conversations[0].name  会话名称
						//data.conversations[0].appkey  appkey(单聊)
						//data.conversations[0].username  用户名(单聊)
						//data.conversations[0].nickname  用户昵称(单聊)
						//data.conversations[0].avatar  头像 media_id 
						//data.conversations[0].mtime 会话最后的消息时间戳
						//data.conversations[0].gid 群 id(群聊)
						//data.conversations[0].type  会话类型(3 代表单聊会话类型，4 代表群聊会话类型)
					
						console.log(that.Conversation)
						
						for (var i = 0; i < data.conversations.length; i++) {
							that.get_message_time(data.conversations[i].mtime,i)
							that.get_avatar(data.conversations[i].avatar,i)
						}
						uni.hideLoading()
						
					}).onFail(function(data) {
						console.log('JIM fail:' + data )
						//data.code 返回码
						//data.message 描述
						that.loadmore = false
						uni.hideLoading()
					});
				} catch (e){
					console.log('JIM catch fail:' + e )
					that.loadmore = false
					uni.hideLoading()
				}
			},
			get_message_time(timestamp,msg_ids) {
				let that=this;
				let Time_now=that.Time_now;
				
				var date = new Date(timestamp);
				var Y = date.getFullYear() + '-';
				var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
				var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
				var h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
				var m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()) + '';
				var s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
				if (Y==Time_now.Y && M==Time_now.M && D==Time_now.D) {
					that.Conversation[msg_ids].timess=h+m;
				} else{
					that.Conversation[msg_ids].timess=M+D;
				}
				// return Y + M + D + h + m + s;
			},
			Times_now(){
				var that=this;
				var date = new Date();
				var Y = date.getFullYear() + '-';
				var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
				var D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate()) + ' ';
				var h = (date.getHours() < 10 ? '0' + (date.getHours()) : date.getHours()) + ':';
				var m = (date.getMinutes() < 10 ? '0' + (date.getMinutes()) : date.getMinutes()) + ':';
				var s = (date.getSeconds() < 10 ? '0' + (date.getSeconds()) : date.getSeconds());
				// console.log(date)
				that.Time_now.Date=date;
				that.Time_now.Y=Y;
				that.Time_now.M=M;
				that.Time_now.D=D;
				that.Time_now.h=h;
				that.Time_now.m=m;
				that.Time_now.s=s;
			},
			get_avatar(media_id,msg_ids){
				var that=this;
				let JIM = app.globalData.JIM;
				JIM.getResource({
					'media_id': media_id,
				}).onSuccess(function(data) {
					//data.code 返回码
					//data.message 描述
					//data.url 资源临时访问路径，具体超时时间expire time会包含在url中
					console.log(data)
					//头像：data.user_info.avatar
					//that.my_avatar=data.url
					that.Conversation[msg_ids].avatar=data.url
				}).onFail(function(data) {
					//data.code 返回码
					//data.message 描述
				});
			}
		}
	};
</script>
<style>

</style>
