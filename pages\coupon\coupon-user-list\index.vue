<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">我的优惠券</block>
		</cu-custom>
		<scroll-view scroll-x class="bg-white nav fixed scroll-nav">
			<view class="flex text-center">
				<view :class="'cu-item flex-sub ' + (index==tabCur?'cur text-'+theme.themeColor:'')" v-for="(item, index) in couponStatus" :key="index"
				 @tap="tabSelect" :data-index="index" :data-key="item.key">{{item.value}}</view>
			</view>
		</scroll-view>
		<view class="margin-top-bar">
			<view class="cu-list padding-bottom-sm ">
				<view class="cu-item padding bg-white" v-for="(item, index) in couponUserList" :key="index">
					<coupon-user-info :couponUserInfo="item"></coupon-user-info>
				</view>
			</view>
			<view :class="'cu-load  ' + (loadmore?'loading':'over')"></view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	import couponUserInfo from "components/coupon-user-info/index";

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				tabCur: 0,
				couponStatus: [{
					value: '全部',
					key: '0'
				}, {
					value: '已使用',
					key: '1'
				}, {
					value: '已过期',
					key: '2'
				}],
				page: {
					searchCount: false,
					current: 1,
					size: 10,
					ascs: '',
					//升序字段
					descs: 'create_time'
				},
				parameter: {
					status: '0'
				},
				loadmore: true,
				couponUserList: []
			};
		},

		components: {
			couponUserInfo
		},
		props: {},

		onShow() {},

		onLoad: function(options) {
			app.initPage().then(res => {
				this.couponUserPage();
			});
		},

		onReachBottom() {
			if (this.loadmore) {
				this.page.current = this.page.current + 1;
				this.couponUserPage();
			}
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框

			uni.hideNavigationBarLoading(); // 停止下拉动作

			uni.stopPullDownRefresh();
		},

		methods: {
			couponUserPage() {
				api.couponUserPage(Object.assign({}, this.page, util.filterForm(this.parameter))).then(res => {
					let couponUserList = res.data.records;
					this.couponUserList = [...this.couponUserList, ...couponUserList];

					if (couponUserList.length < this.page.size) {
						this.loadmore = false;
					}
				});
			},

			refresh() {
				this.loadmore = true;
				this.couponUserList = [];
				this.page.current = 1;
				this.couponUserPage();
			},

			tabSelect(e) {
				let dataset = e.currentTarget.dataset;
				if (dataset.index != this.tabCur) {
					this.tabCur = dataset.index;
					this.parameter.status = dataset.key;
					this.refresh();
				}
			}

		}
	};
</script>

<style>
	.scroll-nav {
		top: unset !important;
	}
</style>
