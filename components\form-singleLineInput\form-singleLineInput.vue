<template>
	<view  class="flex justify-center">
	<view  class="singleLineFontComponent">
		<view
			:style="{color: `${newData.titleColor}`, fontSize: `${newData.titleSize}px`,fontWeight:`${newData.titleWeight?'bold':'normal'}`}">
			{{newData.title}}<i v-show="newData.required" style="color: #FF0000">*</i>
		</view>
		<view
			class="margin-tb-xs"
			:style="{color: `${newData.describeColor}`, fontSize: `${newData.describeSize}px`,fontWeight:`${newData.describeWeight?'bold':'normal'}`}">
			{{newData.describe}}
		</view>
		<view class="form_comp_view flex justify-start align-start solid">
		
			<view class="form_comp_view_icon margin-sm" :style="{color: `${newData.titleColor}`}"
				:class="newData.titleIcon"></view>
			<input class=" form_comp_view_input" v-model="inputValue" type="text"  :placeholder="'请输入'+newData.title">
		</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				newData: this.value,
				inputValue:'',
			};
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		methods: {
			checkValue(){
				console.log("检验值了singleLineFontComponent:",this.inputValue)
				if(this.newData.required && !this.inputValue){
					uni.showToast( {
						title: "请完善"+this.newData.title+"的值",
						icon: 'none',
						duration: 2000
					});
					return false;
				}
				return {value:this.inputValue};
			}
		}
	};
</script>
<style>	
	.singleLineFontComponent {
		padding: 5px 15px;
		width: 90%;
		border-bottom:1px dashed rgba(0,0,0,.2);
	}
	.form_comp_view{
		min-height: 20px;
		justify-content: center;
		align-items: center;
	}
	.form_comp_view_icon{
		font-size: 18px;
	}
	.form_comp_view_input{
		min-height: 20px;
		width: 100%;
	}
</style>
