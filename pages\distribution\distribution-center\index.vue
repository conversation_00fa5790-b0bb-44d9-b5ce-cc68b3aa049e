<template>
	<view class="">
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">分销中心</block>
		</cu-custom>
		<!-- 如果没有成为分销员 -->
		<view v-if="!distribution">
			<!-- 指定分销 -->
			<view class="distribution-image-bg" v-if="distributionConfig.distributionModel==1" :class="'bg-'+theme.themeColor">
				<image class="distribution-image" src="/static/public/img/distribution-1.png"></image>
				<view class="text-center distribution-text">
					<view class="text-xl font-weight text-yellow">您当前还未获得分销权限</view>
					<view class="margin-top text-df font-weight text-yellow text-height">请联系商城管理员即可开启分销权限<br/></view>
					<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
						<button class="cu-btn back-home text-red">点击返回</button>
					</navigator>
				</view>
			</view>
			<!-- 人人分销 -->
			<view class="distribution-image-bg" v-else-if="distributionConfig.distributionModel==2" :class="'bg-'+theme.themeColor">
				<image class="distribution-image" src="/static/public/img/distribution-1.png"></image>
				<view class="text-center distribution-text">
					<view class="text-xl font-weight text-yellow">您当前还未获得分销权限</view>
					<view class="margin-top text-df font-weight text-yellow text-height">分享商品链接或活动海报给好友<br/>好友注册并登录商城后成功支付订单<br/>即可自动获得分销权限</view>
					<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
						<button class="cu-btn back-home text-red">点击返回</button>
					</navigator>
				</view>
			</view>
			<!-- 满额分销 -->
			<view class="distribution-image-bg" style="" v-else-if="distributionConfig.distributionModel==3" :class="'bg-'+theme.themeColor">
				<image class="distribution-image" src="/static/public/img/distribution-1.png"></image>
				<view class="text-center distribution-text">
					<view class="text-xl font-weight text-yellow">您当前还未获得分销权限</view>
					<view class="margin-top text-df font-weight text-yellow text-height">您当前在商城的消费金额为<text class="text-price">{{userConsumptionRecord.totalAmount}}</text>元<br/>当消费满<text class="text-price">{{distributionConfig.fullAmount}}</text>元，即可获得分销权限<br/>快去商城购置商品获取分销权限吧！</view>
					<navigator open-type="navigateBack" class="cu-item arrow margin-top-xl" hover-class="none">
						<button class="cu-btn back-home text-red">点击返回</button>
					</navigator>
				</view>
			</view>
		</view>
		<!-- 如果是分销员 -->
		<view  v-else>
			<view class="money-bg" :class="'bg-'+theme.themeColor">
				<view class="cu-item flex align-center margin-left">
					<view class="cu-avatar round lg margin-top" 
						:style="userInfo.headimgUrl?'background-image:url(' + userInfo.headimgUrl + ')':''">
						{{!userInfo.headimgUrl ? '头' : ''}}</view>
					<view class="content flex-sub margin-left-sm margin-top">
						<view class="font-weight text-xl">{{userInfo.nickName}}</view>
					</view>
				</view>
				<view class="padding-left padding-right flex justify-between margin-top-xs">
					<view class="text-price text-xxl margin-top"><text class="distribution-money margin-left-xs">{{money}}</text></view>
					<navigator class="cu-item arrow" url="/pages/distribution/distribution-withdraw/index" hover-class="none">
						<button class="cu-btn bg-white withdraw round">
							<text class="text-red">提现</text>
						</button>
					</navigator>
				</view>
				<view class="flex justify-between padding margin-top">
					<view class="text-center font-weight">
						<view class="text-sm">累计获得佣金</view>
						<view class="text-price text-sm">{{distribution.commissionTotal}}</view>
					</view>
					<view class="text-center font-weight margin-right-sm">
						<view class="text-sm">累计提现</view>
						<view class="text-price text-sm">{{distribution.commissionWithdrawal}}</view>
					</view>
				</view>
			</view>
			<view class="cu-list menu distribution-list-bg font-weight">
				<navigator class="cu-item arrow" url="/pages/distribution/distribution-card/index" hover-class="none">
					<view class="content">
						<image class="distribution-icon" src="/static/public/img/distribution-icon/ic_distribution_1.png"></image>
						<text class="text-black text-df margin-left-xs">推广名片</text>
					</view>
				</navigator>
				<navigator class="cu-item arrow" url="/pages/distribution/distribution-withdraw-list/index" hover-class="none">
					<view class="content">
						<image class="distribution-icon" src="/static/public/img/distribution-icon/ic_distribution_2.png"></image>
						<text class="text-black text-df margin-left-xs">提现记录</text>
					</view>
				</navigator>
				<navigator class="cu-item arrow" url="/pages/distribution/distribution-promotion-statistical/index" hover-class="none">
					<view class="content">
						<image class="distribution-icon" src="/static/public/img/distribution-icon/ic_distribution_4.png"></image>
						<text class="text-black text-df margin-left-xs">推广统计</text>
					</view>
				</navigator>
				<navigator class="cu-item arrow" url="/pages/distribution/distribution-order-list/index" hover-class="none">
					<view class="content">
						<image class="distribution-icon" src="/static/public/img/distribution-icon/ic_distribution_5.png"></image>
						<text class="text-black text-df margin-left-xs">分销订单</text>
					</view>
				</navigator>
				<navigator class="cu-item arrow" url="/pages/distribution/distribution-promotion-ranking/index" hover-class="none">
					<view class="content">
						<image class="distribution-icon" src="/static/public/img/distribution-icon/ic_distribution_6.png"></image>
						<text class="text-black text-df margin-left-xs">推广排行</text>
					</view>
				</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2021
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const numberUtil = require("utils/numberUtil.js");
	const app = getApp();
	import api from 'utils/api'
	
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				userInfo: {},
				distribution: undefined,
				money: 0,//当前佣金金额
				userConsumptionRecord: {
					totalAmount: 0//已消费金额
				},//用户消费记录
				distributionConfig: {
					fullAmount: 0
				}
			}
		},
		onLoad(){
			this.userInfo = uni.getStorageSync('user_info')
		},
		onShow(){
			this.initData();
		},
		methods: {
			initData(){
				api.distributionuser().then(res => {
					if(res.data) {//是分销员
						this.distribution = res.data
						let money = this.distribution.commissionTotal - this.distribution.commissionWithdrawal
						this.money = numberUtil.numberFormat(money, 2)
					}
				});
				api.userRecord().then(res => {
					if(res.data) {
						this.userConsumptionRecord = res.data
					}
				});
				api.distributionConfig().then(res => {
					if(res.data) {
						this.distributionConfig = res.data
					}
				});
			}
		}
	}
</script>

<style>
	page{
		background-color: #FFFFFF;
	}
	
	.distribution-text{
		width: 100%;
	}
	
	.distribution-image-bg{
		position: relative;
		height: 100vh;
	}
	
	.distribution-image{
		width: 100%;
		position: fixed;
		height: 79vh;
	}
	
	.text-height{
		line-height: 50rpx;
	}
	
	.distribution-text{
		position: fixed;
		padding-top: 800rpx;
	}
	
	.money-bg{
		width: 93%; 
		height: 400rpx; 
		background-image: url(http://minio.joolun.com/joolun/1/material/8c02edc1-1c5a-452c-b67a-5f8d18a19a62.png);
		background-size: 100% 100%;
		border-radius: 30rpx; 
		margin:80rpx auto;
		box-shadow:0px 10px 30px #ffcfcf;
	}
	
	.distribution-list-bg{
		margin-top: 100rpx;
		width: 93%;
		border-radius: 30rpx;
		margin: auto;
		background: none;
	}
	
	.withdraw{
		width: 140rpx;
		height: 140rpx;
	}
	
	.arrow{
		background: none !important;
		height: 120rpx;
	}
	
	.font-weight{
		font-weight: 300;
	}
	
	.distribution-money{
		font-size: 68rpx;
	}
	
	.back-home{
		background-color: #fbd461 !important;
		width: 80% !important; 
		height: 86rpx !important;
	}
	
	.distribution-icon{
		width: 36rpx !important; 
		height: 36rpx !important;
	}
</style>
