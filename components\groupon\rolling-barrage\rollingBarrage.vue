<template>
	<!-- 滚动弹幕 -->
	<!-- 	<view class="rollingBarrageComponent">
		<view v-for="(item,index) in showList" :key="index" >
			<view class="barrage" :style="item.style" :key="item.nickName+index">
				<view class="avatar-img cu-avatar round bg-img lg margin-xs "
					:style="{'background-image':`${'url('+item.headimgUrl+')'}`,height: '30px',width: '30px'}">
				</view>
				<p>{{item.nickName}}</p>
				<p class="flex-twice">{{newData.subscribe}}</p>
			</view>
		</view>
	</view> -->
	<view style="overflow: hidden;position: fixed;width: 100%;height: 100%;pointer-events: none; top: 0;">
		<view class="danmu-li" v-for="(item,index) in listData" :class="item.type" :style="item.style" :key="index">
			<view class="danmu-inner">
				<view class="user-box" :style="{backgroundColor: newData.backColor, opacity:newData.opacity/100}">
					<view class="user-img">
						<view class=" img-box avatar-img cu-avatar round bg-img lg margin-xs "
							:style="{'background-image':`${'url('+item.item.headimgUrl+')'}`,height: '30px',width: '30px'}">
						</view>
					</view>
					<view class="user-text cl1" :style="{color: `${newData.fontColor}`}">
						{{item.item.nickName}}
					</view>
					<view class="user-status cl1" :style="{color: `${newData.fontColor}`}">
						{{newData.subscribe}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	const util = require("utils/util.js");
	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
			grouponInfo: {
				type: Object,
				default: function() {
					return {}
				}
			},
			type: {
				type: String,
				default: 'rightToLeft'
			},
			minTime: {
				type: Number,
				default: 4
			},
			maxTime: {
				type: Number,
				default: 9
			},
			minTop: {
				type: Number,
				default: 0
			},
			maxTop: {
				type: Number,
				default: 240
			},
			hrackH: { //轨道高度
				type: Number,
				default: 40
			},
			noStacked: { //不允许堆叠(暂不可用)
				type: Array,
				default () {
					return []
				}
			}
		},
		watch: {
			grouponInfo(val, oldVal) {
				if (val != oldVal) {
					this.getList()
				}
			}
		},
		mounted() {},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				userList: [], //组员信息
				showList: [], //显示弹幕
				preList: [], //等待弹幕
				time: '',
				listData: [],
			};
		},
		methods: {
			getList() {
				let id = this.grouponInfo.id;
				// console.log("rolong ", this.grouponInfo)
				api.getOrderUser(id).then(res => {
					this.userList = res.data
					// console.log("用户信息", res.data)
					for (var i = 0; i < this.userList.length; i++) {
						this.add(this.userList[i])
					}
					console.log("showList", this.showList)
					this.setTime();
				});
			},
			setTime() {
				this.timer = setInterval(() => {
					for (var i = 0; i < this.userList.length; i++) {
						this.add(this.userList[i])
					}
				}, 6000)
			},
			add(obj) {
				let data = {
					item: obj,
					id: Date.parse(new Date()),
					time: Math.ceil(Math.floor(Math.random() * (this.maxTime - this.minTime + 1) + this.minTime)),
					type: this.type
				}
				if (this.type === 'leftBottom') {
					let objData = {
						item: data.item,
						type: 'leftBottomEnter',
						style: {
							transition: `all 0.5s`,
							animationDuration: `0.5s`,
							transform: `translateX(0%)`,
							bottom: `${this.minTop}px`
						}
					}
					let listLen = this.listData.length;
					let hrackNum = this.hrackNum;
					for (let i in this.listData) {
						if (this.listData[i].status === 'reuse') { //重用
							this.$set(this.listData, i, objData);
						} else if (this.listData[i].status === 'reset') { //重置
							this.listData[i].style.transition = 'none';
							this.listData[i].style.bottom = 0;
							this.listData[i].status = 'reuse';
						} else if (this.listData[i].status === 'recycle') { //回收
							this.listData[i].type = 'leftBottomExit';
							this.listData[i].status = 'reset';
						} else {
							this.listData[i].style.bottom = parseInt(this.listData[i].style.bottom) + this.hrackH + 'px';
						}
						if (parseInt(this.listData[i].style.bottom) >= (this.maxTop - this.hrackH) && this.listData[i]
							.status !== 'reset') { //需要回收
							this.listData[i].status = 'recycle';
						}
					}
					if (listLen < hrackNum + 2) {
						this.listData.push(objData);
					}
				} else if (this.type === 'rightToLeft' || this.type === 'leftToRight') {
					let objData = this.horStacked(data);
					for (let i in this.listData) {
						if (this.listData[i].delTime <= Date.parse(new Date())) {
							this.repaint(i, objData.type);
							objData.type = '';
							this.$set(this.listData, i, objData);
							return
						}
					}
					this.listData.push(objData);
				}
			},
			horStacked(data) {
				return {
					item: data.item,
					type: this.type,
					style: {
						animationDuration: `${data.time}s`,
						top: `${Math.ceil(Math.random() * (this.maxTop - this.minTop + 1) + this.minTop)}px`
					},
					delTime: Date.parse(new Date()) + data.time * 1200
				}
			},
			repaint(index, type) {
				setTimeout(() => {
					this.listData[index].type = type;
				}, 100)
			}
		}
	}
</script>

<style scoped lang="scss">
	.rollingBarrageComponent {
		overflow: hidden;
		position: absolute;
		top: 0;
		z-index: 99999;
		animation: barrage 5s linear infinite forwards;
		// visibility:hidden;
	}

	.box {
		display: flex;
		justify-content: center;
		/* 水平居中 */
		align-items: center;
		/* 垂直居中 */
		height: 40px;
		text-align: center;
	}

	.barrage {
		display: flex;
		width: 180px;
		height: 30px;
		line-height: 30px;
		align-items: center;
		border-radius: 5px;
	}

	@keyframes barrage {
		0% {
			top: 0px;
			transform: translateX(180%);
		}

		100% {
			top: 200px;
			transform: translateX(-100%);
		}
	}


	//------------------------------------------
	@keyframes leftBottomEnter {
		0% {
			transform: translateY(100%);
			opacity: 0;
		}

		100% {
			transform: translateY(0%);
			opacity: 1;
		}
	}

	@keyframes leftBottomExit {
		0% {
			transform: translateY(0%);
			opacity: 1;
		}

		100% {
			transform: translateY(-200%);
			opacity: 0;
		}
	}

	@keyframes leftToRight {
		0% {
			transform: translateX(-100%);
		}

		100% {
			transform: translateX(100%);
		}
	}

	@keyframes rightToLeft {
		0% {
			transform: translateX(150%);
		}

		100% {
			transform: translateX(-100%);
		}
	}

	.danmu-li {
		position: absolute;
		width: 100%;
		transform: translateX(100%);
		animation-timing-function: linear;

		&.leftBottomEnter {
			animation-name: leftBottomEnter;
		}

		&.leftBottomExit {
			animation-name: leftBottomExit;
			animation-fill-mode: forwards;
		}

		&.rightToLeft {
			animation-name: rightToLeft;
		}

		&.leftToRight {
			animation-name: leftToRight;
		}

		.danmu-inner {
			display: inline-block;

			.user-box {
				display: flex;
				padding: 3rpx 40rpx 3rpx 10rpx;
				// background: rgba(0, 0, 0, 0.3); //用户box
				border-radius: 32rpx;
				align-items: center;

				.user-img {
					.img-box {
						display: flex;

						image {
							width: 88rpx;
							height: 58rpx;
							background: rgba(55, 55, 55, 1);
							border-radius: 50%;
						}
					}
				}

				.user-status {
					margin-left: 10rpx;
					white-space: nowrap;
					font-size: 28rpx;
					font-weight: 400;
					color: rgba(255, 255, 255, 1);
				}

				.user-text {
					margin-left: 10rpx;
					// white-space: nowrap;
					font-size: 28rpx;
					font-weight: 400;
					// width: 200rpx;
					color: rgba(255, 255, 255, 1);
				}
			}
		}
	}
</style>
