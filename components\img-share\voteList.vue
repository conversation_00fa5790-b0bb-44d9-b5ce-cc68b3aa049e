<template>
	<view class="voteListComponent" :style="{marginBottom: `${newData.pageMarginBottom}px`,
       marginLeft: `${newData.pageMarginLeft}px`,
       marginRight: `${newData.pageMarginRight}px`,
       marginTop: `${newData.pageMarginTop}px`}">
		<view class="content" :style="{backgroundColor: newData.background,
          paddingTop:`${newData.contentPaddingTop}px`,
          paddingBottom:`${newData.contentPaddingBottom}px`,
          paddingLeft:`${newData.contentPaddingLeft}px`,
          paddingRight:`${newData.contentPaddingRight}px`,
          borderTopLeftRadius:`${newData.topBorderRadius}px`,
          borderTopRightRadius:`${newData.topBorderRadius}px`,
          borderBottomLeftRadius:`${newData.bottomBorderRadius}px`,
          borderBottomRightRadius:`${newData.bottomBorderRadius}px`}">
			<view v-if="userList" class="flex justify-center align-center"   v-for="(item,index1) in userList" :key="index1">
				<view  v-for="(user,index) in item" class="avatar_box round cu-avatar bg-img lg margin-xs"
					:style="{width:'40px',height:'40px',backgroundImage:`${'url('+user.headimgUrl+')'}`}">
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import api from '@/utils/api'
	const util = require("utils/util.js");


	export default {
		data() {
			return {
				newData: this.value,
				userList: [], //用户列表
			};
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
			imgShareData: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		computed: {

		},
		created() {
			this.getVoteList()
		},
		mounted() {},
		methods: {
			getVoteList() {
				let id = util.getUrlParam(location.href, "data_id");
				console.log("getVoteList", id)
				api.getVoteList(id).then(res => {
					console.log("投票的查看", res)
					let userList = res.data;
					let one =[]
					let sec =[]
					for (let i = 0; i < userList.length; i++) {
						sec.push(userList[i])
						if( (i+1)%8 == 0){
							one.push(sec)
							sec = [];
						}
						if(i == userList.length-1){
							one.push(sec)
							sec = [];
						}
					}
					console.log("1212",one)
					this.userList = one;
				}).catch(err => {
					console.log(err)
				});
			},
		},
	};
</script>
<style>
	.voteListComponent {
	}

	.content {
		display: inline-block;
		width: 100%;
	}

	.avatar_box {
		display: inline-block;
		padding: 1px 1px;
	}

	.avatar {}
</style>
