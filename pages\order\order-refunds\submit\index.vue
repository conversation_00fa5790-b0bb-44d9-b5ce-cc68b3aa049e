<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>

		<view class="cu-bar bg-white solid-bottom">
			<view class="action">
				<text class="cuIcon-titles" :class="'text-'+theme.themeColor"></text>退款信息</view>
		</view>
		<view class="cu-card no-card article">
			<view class="cu-item">
				<view class="content response align-center">
					<image :src="orderItem.picUrl ? orderItem.picUrl : '/static/public/img/no_pic.png'" mode="aspectFill" class="row-img margin-top-xs"></image>
					<view class="desc row-info block">
						<view class="text-black margin-top-sm overflow-2">{{orderItem.spuName}}</view>
						<view class="text-gray text-sm margin-top-sm overflow-2" v-if="orderItem.specInfo">{{orderItem.specInfo}}</view>
						<view class="flex justify-between align-center">
							<view class="text-price text-gray text-sm margin-top-sm">{{orderItem.salesPrice}}</view>
							<view class="text-gray text-sm margin-top-sm">x{{orderItem.quantity}}</view>
						</view>
					</view>
				</view>
			</view>
			<view class="cu-item">
				<!-- <view class="padding solid-top">
					<radio-group @change="radioChange">
						<radio class="red margin-right-xs" :class="theme.themeColor" value="1"></radio>退款
						<radio class="red margin-left-sm margin-right-xs" :class="theme.themeColor"  value="2"></radio>退货退款
					</radio-group>
				</view> -->
				<view class="padding solid-top">退款金额：<text class="text-price text-bold text-red">{{orderItem.paymentPrice}}</text>
				</view>
				<view class="padding solid-top" v-if="orderItem.paymentPoints">退款积分：<text class="text-bold text-red">{{orderItem.paymentPoints}}</text>
				</view>
				<view class="padding">退款数量：x{{orderItem.quantity}}</view>
			</view>
		</view>
		<view class="cu-card no-card padding-top-xs">
			<view class="cu-item cu-form-group align-start">
				<view class="title">退款原因</view>
				<textarea maxlength="40" @input="resonInput" placeholder="请输入退款原因" :value="orderRefunds.refundReson"></textarea>
			</view>
		</view>
		<view class="cu-bar bg-white tabbar foot justify-end">
<!--			<navigator class="cu-btn round margin-right shadow-blur" open-type="navigate"
				:url="'/pages/customer-service/customer-service-list/index?shopId='+orderRefunds.shopId">
				<view class="cuIcon-servicefill">联系客服</view>
			</navigator>-->
      <button class="cu-btn round line-blue margin-right cuIcon-servicefill"  @click="openCustomerService">客服
      </button>
			<button class="cu-btn round margin-right shadow-blur" :class="'bg-'+theme.themeColor" @tap="subRefunds">确认并提交</button>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	const util = require("utils/util.js");
	import api from 'utils/api'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderItem: {},
				orderRefunds: {},
				orderItemId: ""
			};
		},

		components: {},
		props: {},

		onShow() {},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.orderItemId = options.orderItemId;
			this.orderRefunds.orderItemId = options.orderItemId;
			app.initPage().then(res => {
				this.orderItemGet(this.orderItemId);
			});
		},

		methods: {
    openCustomerService() {
      wx.openCustomerServiceChat({
        extInfo: { url: 'https://work.weixin.qq.com/kfid/kfcfd2e702b9ffd3ba7' }, corpId: 'wwd396b79dd20220ba', success(res) {
          console.log(res);
        }, fail(err) {
          console.log(err);

        },
      })
    },
			orderItemGet(id) {
				let that = this;
				api.orderItemGet(id).then(res => {
					this.orderItem = res.data;
				});
			},

			resonInput(e) {
				this.orderRefunds.refundReson = e.detail.value

			},

			radioChange(e) {
				this.orderRefunds.status = e.detail.value;
			},

			subRefunds() {
				/* if (!this.orderRefunds.status) {
					uni.showToast({
						title: '请选择退款类型',
						icon: 'none',
						duration: 2000
					});
					return;
				} */

				/* if (!this.orderRefunds.refundReson) {
					uni.showToast({
						title: '请输入退款原因',
						icon: 'none',
						duration: 2000
					});
					return;
				} */
				this.orderRefunds.status = '1';
				let that = this;
				uni.showModal({
					content: '确认提交退款申请吗？',
					cancelText: '我再想想',
					confirmColor: '#ff0000',

					success(res) {
						if (res.confirm) {
							api.orderRefundsSave(that.orderRefunds).then(res => {
								uni.redirectTo({
									url: '/pages/order/order-refunds/form/index?orderItemId=' + that.orderItemId
								});
							});
						}
					}

				});
			}

		}
	};
</script>
<style>
	.row-img {
		width: 200rpx !important;
		height: 200rpx !important;
		border-radius: 10rpx
	}
</style>
