<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">积分签到</block>
		</cu-custom>
		<view class="signrecord-bg" :class="'bg-'+theme.backgroundColor">		
			<image class="signrecord-image" src="http://minio.joolun.com/joolun/1/material/36280a7d-1d82-441e-b3d0-d97aee1af626.png"></image>
			<view class="cu-item personal-information">
				<view class="content text-center">
					<view class="flex align-center justify-center">
						<text class="text-white text-l font-weight">已累计</text>
						<view class="padding-xs number-bg" v-for="(item,index) in signRecord.cumulateDays" :key="index">
							<text class="text-red number-day">{{item}}</text>
							<view class="number-bg-top"></view>
							<view class="number-bg-bottom"></view>
						</view>
						<text class="text-white text-l font-weight">天签到</text>
					</view>
					<view class="font-weight padding-top">
						<view class="text-white text-sm margin-top-xl">你已连续签到 {{signRecord.continuDays}} 天</view>
						<view class="text-white text-sm margin-top-xs">据说连续签到七天可获得超额积分</view>
						<view class="text-white text-sm margin-top-xs">一定要坚持签到哦～</view>
					</view>	
				</view>
			</view>
		</view>
		<view class="bg-white signrecord-bg-2">
			<view class="flex">
				<image class="integral" src="http://minio.joolun.com/joolun/1/material/56b987a1-edea-42a3-b1a4-f9fdd372bca7.png"></image>
			</view>
			<view class="cu-steps steps-bottom margin-top-xl signrecord-day">
				<view class="cu-item" :class="index>scroll?'':'text-red'" v-for="(item,index) in signConfigList" :key="index" :id="'scroll-' + index">
					<text class="font-weight text-sm">{{item.name}}</text>
					<text class="num" :data-index="index + 1"></text>
					<text>+{{item.posts}}</text>
				</view>
			</view>
			<view class="action margin-top-xl">
				<button class="cu-btn round shadow flex signrecord-btn" :class="canSign ? 'bg-'+theme.backgroundColor : 'bg-grey'" @tap="userSign">{{canSign?'立即签到':'已签到'}}</button>
			</view>	
			<view class="cu-modal" :class="showModal?'show':''">
				<view class="cu-dialog dialog-bg">
					<view class="bg-img">
						<view class="cu-bar justify-end text-white">
							<view class="action" @tap="hideModal">
								<text class="cuIcon-close "></text>
							</view>
						</view>
						<view class="sign-succeed">
							<view><text class="text-red">签到成功！</text></view>
							<view><text class="text-red text-lg text-bold">获得{{signConfig.posts}}积分</text></view>
						</view>
						<view class="action">
							<button class="cu-btn bg-yellow round shadow flex text-white succeed-btn" @tap="hideModal">我知道了</button>
						</view>
					</view>
				</view>
			</view>
			<navigator class="flex margin-top-xl padding-bottom-xl" :key="index" hover-class="none" :url="'/pages/signrecord/signrecord-list/index'">
				<text class="text-sm check-record">查看签到记录</text>
			</navigator>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from '@/utils/api';
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				scroll: 0,
				signRecord: {},
				signConfigList: [],
				signConfig: {},
				canSign: false,
				showModal: false
			}
		},
		onLoad(option) {
			this.getSignRecord()
			this.signConfigPage()
		},
		methods: {
			getSignRecord(){
				api.getSignRecord().then(res => {
					let signRecord = res.data;
					let str = signRecord.cumulateDays.toString()
					let j = 4 - str.length
					let beginStr = ''
					for(var i= 0;i<j;i++){
						beginStr = beginStr +'0'
					}
					str = beginStr + str
					signRecord.cumulateDays = str
					this.signRecord = signRecord
					this.scroll = signRecord.continuDays - 1
					let updateTime = this.$moment(this.signRecord.updateTime).format("YYYY-MM-DD")
					let curDate = this.$moment().format("YYYY-MM-DD")
					if(curDate != updateTime){
						this.canSign = true
					}else{
						this.canSign = false
					}
				});
			},
			signConfigPage(){
				api.signConfigPage({
					searchCount: false,
					current: 1,
					size: 100,
					ascs: 'sort'
				}).then(res => {
					this.signConfigList = res.data.records;
				});
			},
			userSign(){
				if(this.canSign){
					api.userSign().then(res => {
						this.signConfig = res.data
						this.getSignRecord()
						this.signConfigPage()
						this.showModal = true
					});
				}
			},
			hideModal(){
				this.showModal = false
			}
		}
	}
</script>

<style>
	.signrecord-bg{
		height: 1000rpx;
	}
	
	.signrecord-image{
		width: 750rpx;
		height: 443rpx;
	}
	
	.personal-information{
		margin-top: -250rpx;
	}
	
	.head{
		margin:40rpx auto 0 auto;
	}
	
	.number-bg-top{
		width: 80rpx;
		height: 60rpx;
		border-radius: 10rpx 10rpx 0rpx 0rpx;
		background-color: #e7e1dd;
	}
	
	.number-bg-bottom{
		width: 80rpx;
		height: 60rpx;
		border-radius: 0rpx 0rpx 10rpx 10rpx;
		background-color: #ffffff;
	}
	
	.number-day{
		position: absolute;
		font-size: 3em;
		margin: 10rpx 0rpx 0rpx -22rpx;
	}
	
	.font-weight{
		font-weight: 300;
	}
	
	.signrecord-bg-2{
		width: 94%;
		height: 620rpx;
		margin: -250rpx auto;
		border-radius: 15rpx;
	}
	
	.integral{
		width: 384rpx;
		height: 322rpx;
		margin: -150rpx auto 0rpx auto;
	}
	
	.signrecord-day{
		overflow: scroll;
	}
	
	.signrecord-btn{
		width: 90%;
		height: 80rpx;
		margin: auto;
		box-shadow:0px 5px 10px #f9ccc1;
	}
	
	.check-record{
		margin: auto;
		font-weight: 300;
	}
	
	.dialog-bg{
		background-color: unset;
	}
	
	.bg-img{
		background-image: url('http://minio.joolun.com/joolun/1/material/bbb479db-eb6a-4245-b799-e95040cdd13c.png');
		height:600rpx;
	}
	
	.sign-succeed{
		margin-top: 230rpx;
	}
	
	.succeed-btn{
		width: 50%;
		margin: 50rpx auto 0 auto;
	}
</style>
