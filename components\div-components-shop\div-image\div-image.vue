<template>
	<!-- 单图显示组件 -->
	<div-base-navigator :pageUrl="newData.pageUrl" style="background: #FFFFFF;" :style="{height: `${newData.height}px`}">
		<view :style="{height: `${newData.height}px`}" style="height: 100%;">
			<image :src="newData.imageUrl" style="width: 100%;" 
			:style="[{'height':`${newData.height}px`}]"></image>
		</view>
	</div-base-navigator>
</template>

<script>
	const app = getApp();
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
    export default {
        name: 'basic-image',
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
                        pageUrl: ``,
                        imageUrl: '',
                        height: 100
	                }
	            }
            }
	    },
	    components: {
			divBaseNavigator
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value
			};
		},
		methods: {}
    }
</script>

<style scoped lang="scss">

</style>
