<!-- 以这个为主 -->
<template>
	<!-- 多功能按钮组件 -->
	<view class="functionButton bg-white" :style="functionButtonStyle">
		<!-- {{newData}} -->
		<view class="functionButtonComponent"
			:style="{marginBottom: `${newData.pageMarginBottom}px`,marginTop: `${newData.pageMarginTop}px`}">
			<!--    {{ newData }}-->
			<view class="button_item">
				<view v-for="(item,index) in newData.buttonList" :key="index" :style="{width: `${item.width}%`}">
					<view v-if="item.showType==1" @click="buttonClick(item)" class="button_content" :style="{color: `${item.fontColor}`,
			fontSize: `${item.fontSize}px`,
			height: `${newData.height}px`,
		        letterSpacing: `${item.fontSpacing}px`,
		        fontWeight:`${item.fontWeight?'bold':'normal'}`,
		        backgroundColor:`${item.backColor}`}">{{ item.title }}
					</view>
					<view v-if="item.showType==2" class="bg-img" @click="buttonClick(item)"
						:style="{'background-image':'url('+`${item.imgUrl}`+')',height: `${newData.height}px`}">
					</view>
				</view>
			</view>
		</view>
		<uni-popup ref="popup" type="dialog" @touchmove.stop.prevent="moveHandle">
			<uni-popup-dialog v-if="popupType == 1" mode="base" message="成功消息" :content="popupContent" :duration="2000"
				:before-close="true" @close="close" @confirm="confirm"></uni-popup-dialog>
			<view v-if="popupType == 2" style="width:550rpx" @click="close()">
				<image style="width: 100%;display: block;" mode="widthFix" :src="popupImg"></image>
			</view>
			<!-- 订单信息 -->
			<view v-if="popupType == 3" style="width:600rpx" @click="close()">
				<view class="cu-card">
					<view class="cu-item">
						<view v-if="!order || order.isPay == '0'">
							<view class="solid-bottom text-xxl padding text-center">
								<text class="cuIcon-roundcheckfill text-yellow"> 您还没有购买</text>
							</view>
						</view>
						<view v-if="order && order.isPay == '1'">
							<view class="solid-bottom text-xxl padding text-center">
								<text class="cuIcon-roundcheckfill text-green"> 支付成功</text>
							</view>
							<view class="margin-left-sm margin-top flex">
								<text class="margin-left flex-sub text-sm">订单名称</text>
								<view class="flex-twice text-sm text-gray">{{order.name}}</view>
							</view>
							<view class="margin-left-sm margin-top flex">
								<text class="margin-left flex-sub text-sm">订单编号</text>
								<view class="flex-twice text-sm text-gray">{{order.orderNo}}</view>
							</view>
							<view class="margin-left-sm margin-top flex">
								<text class="margin-left flex-sub text-sm">付款金额</text>
								<view class="flex-twice text-sm text-gray">{{order.paymentPrice?order.paymentPrice:'0.00'}}</view>
							</view>					
							<view class="margin-left-sm margin-top flex" v-if="order.paymentTime">
								<text class="margin-left flex-sub text-sm">付款时间</text>
								<view class="flex-twice text-sm text-gray">{{order.paymentTime}}</view>
							</view>
							<view class="padding flex flex-direction">
								<!-- <button class="cu-btn margin-tb-sm lg goBackButton " @tap="toBack()">返回</button> -->
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		
		
		
		<!-- <button @click="open">打开弹窗</button> -->
		<uni-popup ref="limituserpopup" type="dialog">
			<uni-popup-dialog title="请填写手机号码" mode="input"
				:duration="2000" :before-close="true" @close="closeLimituser" @confirm="confirmlLimituser">
			</uni-popup-dialog>
		</uni-popup>
		<!-- <button>手机号码限制框</button> -->
		<uni-popup ref="captainPhoneMessage" type="message">
			<uni-popup-message type="warn" message="请输入正确的手机号码" :duration="2000"></uni-popup-message>
		</uni-popup>
	</view>

</template>

<script>
	const app = getApp();
	import api from '@/utils/api'
	const util = require("utils/util.js");
	import jweixin from '@/utils/jweixin';
	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
			order: {
			    type: Object,
			    default: function() {
			        return {}
			    }
			}
		},
		created() {
				this.width = uni.getSystemInfoSync().windowWidth;
				this.top = uni.getSystemInfoSync().windowHeight;	
		},
		mounted() {
				let query = uni.createSelectorQuery().in(this);
				query.select('.functionButton').boundingClientRect(data => {
					 this.height = data.height
				}).exec();
				console.log("点击按钮1121", this.order)
				this.changeStyle()
				
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				couponInfoList: [],
				orderInfo: {}, //订单信息
				buttonOrder: {}, //按钮订单信息
				popupContent: '', //弹出框内容
				popupImg: '', //弹出框内容
				popupType: 0, //弹出框类型
				functionButtonStyle: {}, //组件样式
			};
		},
		methods: {
			buttonClick(item) {
				console.log("点击按钮", item)
				if (item.button.type === 1) {
					this.jumpUrl(item.button);
				} else if (item.button.type === 2) {
					this.compelteOrder(item.button);
				} else if (item.button.type === 3) {
					this.getOrderInfo(item.button);
				} else if (item.button.type === 4) {
					this.showDialoag(item.button);
				} else if (item.button.type === 5) {
					this.phone(item.button);
				}
			},
			//页面跳转
			jumpUrl(obj) {
				console.log("页面跳转", obj)
				if (!obj.pageUrl || obj.pageUrl.indexOf('/') < 0) {
					return;
				}
				if (obj.isSystemUrl) {
					uni.navigateTo({
						url: obj.pageUrl
					}).then(()=>{
							location.reload() 
					});
				} else {
					window.location.href = obj.pageUrl;
				}
			},
			compelteOrder(obj) {
				this.buttonOrder = {
					pageId: util.getUrlParam(location.href, "page_id"),
					price: obj.price,
					name: obj.name,
					phone: '',
					repeatFlag: false,
				}
				if(obj.phoneFlag){
					this.$refs.limituserpopup.open()
					return;
				}
				this.createOrder(this.buttonOrder)

			},
			//创建订单
			createOrder(obj) {
				console.log("请求", obj)
				api.createButtonOrder(obj).then(res => {
					this.orderInfo = res.data
					if (this.orderInfo.isPay == 1) {
						uni.showModal({
							title: '提示',
							content: '该产品已经购买，是否要重复下单？',
							success: (res) => {
								if (res.confirm) {
									console.log("确认了")
									this.buttonOrder.repeatFlag = true;
									this.createOrder(this.buttonOrder)
								} else if (res.cancel) {
									return
								}
							}
						});
					} else if (this.orderInfo.isPay == 0) {
						this.pay()
					}
				}).catch(err => {
					console.log(err)
				});
			},
			close() {
				this.$refs.popup.close()
			},
			confirm() {
				this.$refs.popup.close()
			},
			//确认支付
			pay() {
				console.log("订单信息", this.orderInfo)
				api.wxunifiedOrder({
					id: this.orderInfo.id,
					// #ifdef MP-WEIXIN
					tradeType: 'JSAPI',
					// #endif
					// #ifdef H5
					tradeType: util.isWeiXinBrowser() ? 'JSAPI' : 'MWEB',
					// #endif
					// #ifdef APP-PLUS
					tradeType: 'APP',
					// #endif
				}).then(res => {
					console.log("res的结果", res)
					this.loading = false;
					if (this.orderInfo.isPay == 1) {
						return
					}
					if (this.orderInfo.paymentPrice <= 0) {
						//0元付款
						console.log("0元付款了", this.orderInfo)
					} else {
						let payData = res.data;
						// #ifdef MP-WEIXIN
						//微信小程序
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: payData.timeStamp,
							nonceStr: payData.nonceStr,
							package: payData.packageValue,
							signType: payData.signType,
							paySign: payData.paySign,
							success: function(res) {
								console.log('succ:' + JSON.stringify(res));
							},
							fail: function(res) {
								console.log('fail:' + JSON.stringify(res));
							},
							complete: function(res) {
								console.log(res);
							}
						});
						// #endif
						// #ifdef APP-PLUS
						//app支付
						let orderInfo = {
							"appid": payData.appId,
							"noncestr": payData.nonceStr,
							"package": payData.packageValue,
							"partnerid": payData.partnerId,
							"prepayid": payData.prepayId,
							"timestamp": payData.timeStamp,
							"sign": payData.sign
						}
						uni.requestPayment({
							provider: 'wxpay',
							orderInfo: orderInfo,
							success: function(res) {
								this.paySuccess();
							},
							fail: function(res) {
								console.log('fail:' + JSON.stringify(res));
							},
							complete: function(res) {
								console.log(res);
							}
						});
						// #endif
						// #ifdef H5
						if (util.isWeiXinBrowser()) {
							//公众号H5
							jweixin.payRequest(payData, res => {
								this.paySuccess();
								this.buttonOrder.repeatFlag = false;
							}, e => {

							})
						}
						// #endif
					}
				}).catch(() => {
					this.loading = false;
				});
			},
			//查看订单
			getOrderInfo() {
				this.popupType = 3;
				this.$refs.popup.open('center')
			},
			//弹窗显示
			showDialoag(obj) {
				console.log("弹窗显示", obj)
				this.popupType = obj.popupType;
				// 通过组件定义的ref调用uni-popup方法 ,如果传入参数 ，type 属性将失效 ，仅支持 ['top','left','bottom','right','center']
				this.popupContent = obj.content;
				this.popupImg = obj.imgUrl;
				this.$refs.popup.open('center')
			},
			//电话号码
			phone(obj) {
				console.log("电话号码", obj)
				uni.makePhoneCall({
					// 手机号
					phoneNumber: obj.phone,
					// 成功回调
					success: (res) => {},
					// 失败回调
					fail: (res) => {}
				});
			},
			paySuccess() {
				uni.redirectTo({
					url: '/pages/spellgroup/groupon-pay-result/index?id=' + this.orderInfo.id
				});
			},
			//转变悬浮样式
			changeStyle() {
				if (this.newData.location == 1) { //不悬浮
					return {}
				} else if (this.newData.location == 2) { //固定悬浮底部
				
					this.functionButtonStyle =  {
						"display": "block",
						"position": "fixed",
						"width": "100%",
						"top": (this.top-this.height)+'px',
						"z-index": 2,
					}
					//父页面高度  是否查询按钮订单
					this.$emit("bottomHeightShow",this.height)
				}
			},
			closeLimituser() {
				this.$refs.limituserpopup.close()
			},
			confirmlLimituser(value) {
				// 输入框的值
				if (!(/^1[3456789]\d{9}$/.test(value))) {
					console.log("格式不正确")
					this.$refs.captainPhoneMessage.open()
					return
				}
				this.buttonOrder.phone = value;
				this.createOrder(this.buttonOrder)
				this.$refs.limituserpopup.close()
			}
		}
	}
</script>

<style scoped lang="scss">
	.button_item {
		align-items: center;
		display: flex;
		text-align: center;
		//垂直方向元素居中，两边留白
	}

	.button_content {
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
