<template>
	<!-- 团主头像 -->
	<view class="bg-white memberComponent"
		:style="{marginBottom: `${newData.pageMarginBottom}px`,marginTop: `${newData.pageMarginTop}px`}">
		<view  class="cu-item ">
			<view class="content  text-center">
				<view v-for="item in memberList">
					<view class="cu-avatar round bg-img lg margin-xs text-yellow groupon-user"
						:style="{'background-image':`${'url('+item.headimgUrl+')'}`,height: '60px',width: '60px'}">
					</view>
					<p v-if="item.nickName">{{item.nickName}}</p>
					<p v-if="!item.nickName">等待加入</p>
				</view>
			</view>
			<view class="captain_description">
				<p v-show="grouponInfo.status =='1' || grouponInfo.status =='2'">{{ newData.emptyMsg }}</p>
				<p v-show="grouponInfo.status =='3'">{{ newData.fullMsg }}</p>
			</view>
		</view>
	</view>
</template> 

<script>
	const app = getApp();
	import api from '@/utils/api'
	const util = require("utils/util.js");
	import jweixin from '@/utils/jweixin';
	export default {
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {
						background: ``,
						themeColor: ``,
					}
				}
			},
			grouponInfo: {
				type: Object,
				default: function() {
					return {}
				}
			},

		},
		watch: {
			grouponInfo(val, oldVal) {
				if (val != oldVal) {
					
					this.completeInfo()
				}
			}
		},
		mounted() {
			this.completeInfo()
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				newData: this.value,
				memberList: [], //组员信息
			};
		},
		methods: {
			//完善信息
			completeInfo() {
				this.memberList=[];
				for (let i = 0; i < (this.grouponInfo.number - 1); i++) {
					let obj = {
						headimgUrl: '',
						nickName: ''
					}
					this.memberList.push(obj)
				}
				if(!this.grouponInfo.groupUserList){
					return;
				}
				for (let i = 0; i < this.grouponInfo.groupUserList.length; i++) {
					if (this.grouponInfo.groupUserList[i].isLeader != "1") {
						for (let j = 0; j < this.memberList.length; j++) {
							if (!this.memberList[j].headimgUrl) {
								this.memberList[j].headimgUrl = this.grouponInfo.groupUserList[i].headimgUrl;
								this.memberList[j].nickName = this.grouponInfo.groupUserList[i].nickName;
								break;
							}
						}
					}
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
	}

	.captain_description {
		display: flex;
		justify-content: center;
		align-items: center;
		text-align: center;
	}

	.captain_button {
		display: flex;
		justify-content: center;
		align-items: center;
	}
</style>
