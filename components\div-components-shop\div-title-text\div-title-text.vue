<template>
	<!-- 标题文字组件 -->
	<view :style="{marginBottom: `${newData.pageSpacing}px`}" style="background: #FFFFFF;">
		<div-base-navigator 
					style="width: 100%;text-align: center;"
					:pageUrl="newData.pageUrl"
		            :style="{
						height: `${newData.height}px`,
						lineHeight: `${newData.height}px`,
						color: `${newData.color}`,
						fontSize: `${newData.fontSize}px`
					}">{{newData.title}}</div-base-navigator>
	</view>
</template>

<script>
	const app = getApp();
	import divBaseNavigator from '../div-base/div-base-navigator.vue'
    export default {
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
	                }
	            }
            }
	    },
	    components: {
			divBaseNavigator
		},
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
			};
		},
		methods: {
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
    }
</script>

<style scoped lang="scss">
	
</style>
