<template>
	<view class="div-video" :style="{'padding': data.padding + 'px'}" :class="data.background">
		<view class="title" v-if="data.showTitle" :style="{
			color: data.titleColor,
			'text-align': data.titlePosition,
			'font-size': data.titleSize + 'px',
			'font-weight': data.titleBold ? 'bold' : 'normal'
		}">
			{{data.title}}
		</view>
		<view class="video-container" :style="{
			height: data.height + 'px',
			'border-radius': data.radius + 'px'
		}">
			<video 
				:src="data.videoUrl" 
				:poster="data.coverUrl"
				:controls="data.showControls"
				:autoplay="data.autoplay"
				:loop="data.loop"
				:muted="data.muted"
				object-fit="cover"
				style="width: 100%; height: 100%;"
			></video>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'div-video',
		props: {
			value: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				data: this.value
			}
		},
		watch: {
			value: {
				handler(val) {
					this.data = val;
				},
				deep: true
			}
		},
		methods: {
		}
	}
</script>

<style scoped>
	.div-video {
		width: 100%;
	}

	.video-container {
		width: 100%;
		overflow: hidden;
	}
</style> 