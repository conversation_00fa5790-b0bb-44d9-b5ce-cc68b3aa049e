!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.io=e():t.io=e()}(this,function(){return function(t){function e(o){if(n[o])return n[o].exports;var i=n[o]={exports:{},id:o,loaded:!1};return t[o].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var n={};return e.m=t,e.c=n,e.p="",e(0)}([function(t,e,n){t.exports=n(18)},function(t,e,n){(function(o){function i(){return"undefined"!=typeof document&&"WebkitAppearance"in document.documentElement.style||window.console&&(console.firebug||console.exception&&console.table)||navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31}function s(){var t=arguments,n=this.useColors;if(t[0]=(n?"%c":"")+this.namespace+(n?" %c":" ")+t[0]+(n?"%c ":" ")+"+"+e.humanize(this.diff),!n)return t;var o="color: "+this.color;t=[t[0],o,"color: inherit"].concat(Array.prototype.slice.call(t,1));var i=0,s=0;return t[0].replace(/%[a-z%]/g,function(t){"%%"!==t&&(i++,"%c"===t&&(s=i))}),t.splice(s,0,o),t}function r(){return"object"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)}function a(t){try{null==t?e.storage.removeItem("debug"):e.storage.debug=t}catch(t){}}function c(){try{return e.storage.debug}catch(t){}if("undefined"!=typeof o&&"env"in o)return o.env.DEBUG}function p(){try{return window.localStorage}catch(t){}}e=t.exports=n(34),e.log=r,e.formatArgs=s,e.save=a,e.load=c,e.useColors=i,e.storage="undefined"!=typeof chrome&&"undefined"!=typeof chrome.storage?chrome.storage.local:p(),e.colors=["lightseagreen","forestgreen","goldenrod","dodgerblue","darkorchid","crimson"],e.formatters.j=function(t){try{return JSON.stringify(t)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}},e.enable(c())}).call(e,n(38))},function(t,e,n){t.exports=n(25)},function(t,e,n){function o(t){if(t)return i(t)}function i(t){for(var e in o.prototype)t[e]=o.prototype[e];return t}t.exports=o,o.prototype.on=o.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},o.prototype.once=function(t,e){function n(){this.off(t,n),e.apply(this,arguments)}return n.fn=e,this.on(t,n),this},o.prototype.off=o.prototype.removeListener=o.prototype.removeAllListeners=o.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n=this._callbacks["$"+t];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var o,i=0;i<n.length;i++)if(o=n[i],o===e||o.fn===e){n.splice(i,1);break}return this},o.prototype.emit=function(t){this._callbacks=this._callbacks||{};var e=[].slice.call(arguments,1),n=this._callbacks["$"+t];if(n){n=n.slice(0);for(var o=0,i=n.length;o<i;++o)n[o].apply(this,e)}return this},o.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},o.prototype.hasListeners=function(t){return!!this.listeners(t).length}},function(t,e){var n={SDK_VERSION:"2.6.0",WSS_ADDRESS:"wss://ws.im.jiguang.cn",UPLOAD_FILE:"https://sdk.im.jiguang.cn/resource",ALLOW_MSG_TYPE:["text","image","file","location","custom"],LOGIN_OUT_EVENT:[1,2],FROM_PLATFORM:"x",ACK_TIMEOUT:5e3,RESP_TIMEOUT:3e4,RETRY_TIMES:5,SYNC_INTERVAL:15e4,RECEIPT_REPORT_INTERVAL:2e3,RECEIPT_REPORT_MAX_SIZE:50,EVENT_KEY:"eve-k-",CONVERSATION_KEY:"conversations-",SYNC_TYPE_OPEN:1,SYNC_TYPE_CLOSE:0,FRIEND_INVITE:1,FRIEND_INVITED:2,PLAT_CHANNEL:"w",EVENTS:{ACK:"ack",INIT:"c_init",LOGIN:"login",REGISTER:"register",GET_USER_INFO:"get_across_user_info",GET_ACROSS_USER_INFO:"get_across_user_info",S_SINGLE_TEXT:"s_across_single_text",S_SINGLE_TEXT_:"s_single_text",MSG_SYNC:"msg_sync",MSG_RECV:"msg_recv",MSG_RECV_V2:"msg_recv_v2",SEND_GROUP_MSG:"send_group_msg",CREATE_GROUP:"create_group",GET_GROUPS_LIST:"get_groups_list",GET_GROUP_INFO:"get_group_info",ADD_GROUP_MEMBER:"add_group_member",ADD_ACROSS_GROUP_MEMBER:"add_across_group_member",DEL_GROUP_MEMBER:"del_group_member",DEL_ACROSS_GROUP_MEMBER:"del_across_group_member",GET_GROUP_MEMBERS:"get_group_members",UPDATE_GROUP_INFO:"update_group_info",EXIT_GROUP:"exit_group",EVENT_NOTIFICATION:"event_notification",GET_CONVERSATIONS:"get_conversations",GET_UPLOAD_TOKEN:"get_upload_token",NO_DISTURB:"no_disturb",ADD_MSG_NO_DISTURB_SINGLE:"add_msg_no_disturb_single",DELETE_MSG_NO_DISTURB_SINGLE:"delete_msg_no_disturb_single",ADD_MSG_NO_DISTURB_GROUP:"add_msg_no_disturb_group",DELETE_MSG_NO_DISTURB_GROUP:"delete_msg_no_disturb_group",ADD_MSG_NO_DISTURB_GLOBAL:"add_msg_no_disturb_global",DELETE_MSG_NO_DISTURB_GLOBAL:"delete_msg_no_disturb_global",DISCONNECT:"disconnect",GET_BLACK_LIST:"get_black_list",ADD_BLACK_LIST:"add_black_list",DEL_BLACK_LIST:"del_black_list",UPDATE_SELF_INFO:"update_user_inf",UPDATE_SELF_PWD:"update_user_pwd",ADD_MSG_SHIELD_GROUP:"add_msg_shield_group",DEL_MSG_SHIELD_GROUP:"del_msg_shield_group",ADD_FRIEND:"add_friend",DEL_FRIEND:"del_friend",UPDATE_FRIEND_MEMO:"update_friend_memo",GET_FRIEND_LIST:"get_friend_list",SYNC_CHECK:"sync_check",SYNC_CONVERSATION:"sync_conversation",SYNC_CONVERSATION_ACK:"sync_conversation_ack",MSG_RETRACT:"msg_retract",GET_RESOURCE:"get_resource",LIST_SHIELD_GROUP:"list_shield_group",SYNC_EVENT_CHECK:"sync_event_check",SYNC_EVENT:"sync_event",SYNC_EVENT_ACK:"sync_event_ack",RECEIPT_REPORT:"receipt_report",SYNC_RECEIPT_ACK:"sync_receipt_ack",SYNC_RECEIPT:"sync_receipt",RECEIPT_CHANGE:"receipt_change",UNREAD_GROUP_COUNT:"unread_group_count",UNREAD_SINGLE_COUNT:"unread_single_count",MSG_UNREAD_LIST:"msg_unread_list",TRANS_MSG_SINGLE:"trans_msg_single",TRANS_MSG_GROUP:"trans_msg_group",TRANS_MSG_PLATFORM:"trans_msg_platform",TRANS_MSG_REC:"trans_msg_rec",ADMIN_ADD_GROUP_MEMBER:"admin_add_group_member",APPLY_JOIN_GROUP:"apply_join_group",ROOM_LIST:"room_list",ROOM_LIST_SELF:"room_list_self",JOIN_ROOM:"join_room",EXIT_ROOM:"exit_room",ROOM_INFO:"room_info",SEND_ROOM_MSG:"send_room_msg",ROOM_MSG_SYNC:"room_msg_sync",GROUP_MEM_SILENCE:"group_mem_silence",ROOM_MSG_SYNC_HIS:"room_msg_sync_his",DISSOLVE_GROUP:"dissolve_group",ADD_GROUP_KEEPER:"add_group_keeper",DEL_GROUP_KEEPER:"del_group_keeper",CHANGE_GROUP_ADMIN:"change_group_admin",PUBLIC_GROUP_LIST:"public_group_list"}};t.exports=n},function(t,e,n){function o(){}function i(t){var n="",o=!1;return n+=t.type,e.BINARY_EVENT!=t.type&&e.BINARY_ACK!=t.type||(n+=t.attachments,n+="-"),t.nsp&&"/"!=t.nsp&&(o=!0,n+=t.nsp),null!=t.id&&(o&&(n+=",",o=!1),n+=t.id),null!=t.data&&(o&&(n+=","),n+=JSON.stringify(t.data)),h("encoded %j as %s",t,n),n}function s(t,e){function n(t){var n=f.deconstructPacket(t),o=i(n.packet),s=n.buffers;s.unshift(o),e(s)}f.removeBlobs(t,n)}function r(){this.reconstructor=null}function a(t){var n={},o=0;if(n.type=Number(t.charAt(0)),null==e.types[n.type])return u();if(e.BINARY_EVENT==n.type||e.BINARY_ACK==n.type){for(var i="";"-"!=t.charAt(++o)&&(i+=t.charAt(o),o!=t.length););if(i!=Number(i)||"-"!=t.charAt(o))throw new Error("Illegal attachments");n.attachments=Number(i)}if("/"==t.charAt(o+1))for(n.nsp="";++o;){var s=t.charAt(o);if(","==s)break;if(n.nsp+=s,o==t.length)break}else n.nsp="/";var r=t.charAt(o+1);if(""!==r&&Number(r)==r){for(n.id="";++o;){var s=t.charAt(o);if(null==s||Number(s)!=s){--o;break}if(n.id+=t.charAt(o),o==t.length)break}n.id=Number(n.id)}return t.charAt(++o)&&(n=c(n,t.substr(o))),h("decoded %s as %j",t,n),n}function c(t,e){try{t.data=JSON.parse(e)}catch(t){return u()}return t}function p(t){this.reconPack=t,this.buffers=[]}function u(t){return{type:e.ERROR,data:"parser error"}}var h=n(1)("socket.io-parser"),_=n(40),f=n(39),d=n(16);e.protocol=4,e.types=["CONNECT","DISCONNECT","EVENT","ACK","ERROR","BINARY_EVENT","BINARY_ACK"],e.CONNECT=0,e.DISCONNECT=1,e.EVENT=2,e.ACK=3,e.ERROR=4,e.BINARY_EVENT=5,e.BINARY_ACK=6,e.Encoder=o,e.Decoder=r,o.prototype.encode=function(t,n){if(h("encoding packet %j",t),e.BINARY_EVENT==t.type||e.BINARY_ACK==t.type)s(t,n);else{var o=i(t);n([o])}},_(r.prototype),r.prototype.add=function(t){var n;if("string"==typeof t)n=a(t),e.BINARY_EVENT==n.type||e.BINARY_ACK==n.type?(this.reconstructor=new p(n),0===this.reconstructor.reconPack.attachments&&this.emit("decoded",n)):this.emit("decoded",n);else{if(!d(t)&&!t.base64)throw new Error("Unknown type: "+t);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");n=this.reconstructor.takeBinaryData(t),n&&(this.reconstructor=null,this.emit("decoded",n))}},r.prototype.destroy=function(){this.reconstructor&&this.reconstructor.finishedReconstruction()},p.prototype.takeBinaryData=function(t){if(this.buffers.push(t),this.buffers.length==this.reconPack.attachments){var e=f.reconstructPacket(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null},p.prototype.finishedReconstruction=function(){this.reconPack=null,this.buffers=[]}},function(t,e,n){function o(t,e){return this instanceof o?(t&&"object"==typeof t&&(e=t,t=void 0),e=e||{},e.path=e.path||"/socket.io",this.nsps={},this.subs=[],this.opts=e,this.reconnection(e.reconnection!==!1),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||2e3),this.reconnectionDelayMax(e.reconnectionDelayMax||2e3),this.randomizationFactor(e.randomizationFactor||.5),this.backoff=new _({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?3e3:e.timeout),this.readyState="closed",this.uri=t,this.connecting=[],this.lastPing=null,this.encoding=!1,this.packetBuffer=[],this.encoder=new a.Encoder,this.decoder=new a.Decoder,this.autoConnect=e.autoConnect!==!1,void(this.autoConnect&&this.open())):new o(t,e)}var i=n(27),s=n(8),r=n(3),a=n(5),c=n(7),p=n(11),u=n(1)("socket.io-client:manager"),h=n(12),_=n(32),f=Object.prototype.hasOwnProperty;t.exports=o,o.prototype.emitAll=function(){this.emit.apply(this,arguments);for(var t in this.nsps)f.call(this.nsps,t)&&this.nsps[t].emit.apply(this.nsps[t],arguments)},o.prototype.updateSocketIds=function(){for(var t in this.nsps)f.call(this.nsps,t)&&(this.nsps[t].id=this.engine.id)},r(o.prototype),o.prototype.reconnection=function(t){return arguments.length?(this._reconnection=!!t,this):this._reconnection},o.prototype.reconnectionAttempts=function(t){return arguments.length?(this._reconnectionAttempts=t,this):this._reconnectionAttempts},o.prototype.reconnectionDelay=function(t){return arguments.length?(this._reconnectionDelay=t,this.backoff&&this.backoff.setMin(t),this):this._reconnectionDelay},o.prototype.randomizationFactor=function(t){return arguments.length?(this._randomizationFactor=t,this.backoff&&this.backoff.setJitter(t),this):this._randomizationFactor},o.prototype.reconnectionDelayMax=function(t){return arguments.length?(this._reconnectionDelayMax=t,this.backoff&&this.backoff.setMax(t),this):this._reconnectionDelayMax},o.prototype.timeout=function(t){return arguments.length?(this._timeout=t,this):this._timeout},o.prototype.maybeReconnectOnOpen=function(){!this.reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()},o.prototype.open=o.prototype.connect=function(t,e){if(u("readyState %s",this.readyState),~this.readyState.indexOf("open"))return this;u("opening %s",this.uri),this.engine=i(this.uri,this.opts);var n=this.engine,o=this;this.readyState="opening",this.skipReconnect=!1;var s=c(n,"open",function(){o.onopen(),t&&t()}),r=c(n,"error",function(e){if(u("connect_error"),o.cleanup(),o.readyState="closed",o.emitAll("connect_error",e),t){var n=new Error("Connection error");n.data=e,t(n)}else o.maybeReconnectOnOpen()});if(!1!==this._timeout){var a=this._timeout;u("connect attempt will timeout after %d",a);var p=setTimeout(function(){u("connect attempt timed out after %d",a),s.destroy(),n.close(),n.emit("error","timeout"),o.emitAll("connect_timeout",a)},a);this.subs.push({destroy:function(){clearTimeout(p)}})}return this.subs.push(s),this.subs.push(r),this},o.prototype.onopen=function(){u("open"),this.cleanup(),this.readyState="open",this.emit("open");var t=this.engine;this.subs.push(c(t,"data",p(this,"ondata"))),this.subs.push(c(t,"ping",p(this,"onping"))),this.subs.push(c(t,"pong",p(this,"onpong"))),this.subs.push(c(t,"error",p(this,"onerror"))),this.subs.push(c(t,"close",p(this,"onclose"))),this.subs.push(c(this.decoder,"decoded",p(this,"ondecoded")))},o.prototype.onping=function(){this.lastPing=new Date,this.emitAll("ping")},o.prototype.onpong=function(){this.emitAll("pong",new Date-this.lastPing)},o.prototype.ondata=function(t){this.decoder.add(t)},o.prototype.ondecoded=function(t){this.emit("packet",t)},o.prototype.onerror=function(t){u("error",t),this.emitAll("error",t)},o.prototype.socket=function(t,e){function n(){~h(i.connecting,o)||i.connecting.push(o)}var o=this.nsps[t];if(!o){o=new s(this,t,e),this.nsps[t]=o;var i=this;o.on("connecting",n),o.on("connect",function(){o.id=i.engine.id}),this.autoConnect&&n()}return o},o.prototype.destroy=function(t){var e=h(this.connecting,t);~e&&this.connecting.splice(e,1),this.connecting.length||this.close()},o.prototype.packet=function(t){u("writing packet %j",t);var e=this;t.query&&0===t.type&&(t.nsp+="?"+t.query),e.encoding?e.packetBuffer.push(t):(e.encoding=!0,this.encoder.encode(t,function(n){for(var o=0;o<n.length;o++)e.engine.write(n[o],t.options);e.encoding=!1,e.processPacketQueue()}))},o.prototype.processPacketQueue=function(){if(this.packetBuffer.length>0&&!this.encoding){var t=this.packetBuffer.shift();this.packet(t)}},o.prototype.cleanup=function(){u("cleanup");for(var t=this.subs.length,e=0;e<t;e++){var n=this.subs.shift();n.destroy()}this.packetBuffer=[],this.encoding=!1,this.lastPing=null,this.decoder.destroy()},o.prototype.close=o.prototype.disconnect=function(){u("disconnect"),this.skipReconnect=!0,this.reconnecting=!1,"opening"===this.readyState&&this.cleanup(),this.backoff.reset(),this.readyState="closed",this.engine&&this.engine.close()},o.prototype.onclose=function(t){u("onclose"),this.cleanup(),this.backoff.reset(),this.readyState="closed",this.emit("close",t),this._reconnection&&!this.skipReconnect&&this.reconnect()},o.prototype.reconnect=function(){if(this.reconnecting||this.skipReconnect)return this;var t=this;if(this.backoff.attempts>=this._reconnectionAttempts)u("reconnect failed"),this.backoff.reset(),this.emitAll("reconnect_failed"),this.reconnecting=!1;else{var e=this.backoff.duration();u("will wait %dms before reconnect attempt",e),this.reconnecting=!0;var n=setTimeout(function(){t.skipReconnect||(u("attempting reconnect"),t.emitAll("reconnect_attempt",t.backoff.attempts),t.emitAll("reconnecting",t.backoff.attempts),t.skipReconnect||t.open(function(e){e?(u("reconnect attempt error"),t.reconnecting=!1,t.reconnect(),t.emitAll("reconnect_error",e.data)):(u("reconnect success"),t.onreconnect())}))},e);this.subs.push({destroy:function(){clearTimeout(n)}})}},o.prototype.onreconnect=function(){var t=this.backoff.attempts;this.reconnecting=!1,this.backoff.reset(),this.updateSocketIds(),this.emitAll("reconnect",t)}},function(t,e){function n(t,e,n){return t.on(e,n),{destroy:function(){t.removeListener(e,n)}}}t.exports=n},function(t,e,n){function o(t,e,n){this.io=t,this.nsp=e,this.json=this,this.ids=0,this.acks={},this.receiveBuffer=[],this.sendBuffer=[],this.connected=!1,this.disconnected=!0,n&&n.query&&(this.query=n.query),this.io.autoConnect&&this.open()}var i=n(5),s=n(3),r=n(41),a=n(7),c=n(11),p=n(1)("socket.io-client:socket"),u=n(35);t.exports=e=o;var h={connect:1,connect_error:1,connect_timeout:1,connecting:1,disconnect:1,error:1,reconnect:1,reconnect_attempt:1,reconnect_failed:1,reconnect_error:1,reconnecting:1,ping:1,pong:1},_=s.prototype.emit;s(o.prototype),o.prototype.subEvents=function(){if(!this.subs){var t=this.io;this.subs=[a(t,"open",c(this,"onopen")),a(t,"packet",c(this,"onpacket")),a(t,"close",c(this,"onclose"))]}},o.prototype.open=o.prototype.connect=function(){return this.connected?this:(this.subEvents(),this.io.open(),"open"===this.io.readyState&&this.onopen(),this.emit("connecting"),this)},o.prototype.send=function(){var t=r(arguments);return t.unshift("message"),this.emit.apply(this,t),this},o.prototype.emit=function(t){if(h.hasOwnProperty(t))return _.apply(this,arguments),this;var e=r(arguments),n=i.EVENT,o={type:n,data:e};return o.options={},o.options.compress=!this.flags||!1!==this.flags.compress,"function"==typeof e[e.length-1]&&(p("emitting packet with ack id %d",this.ids),this.acks[this.ids]=e.pop(),o.id=this.ids++),this.connected?this.packet(o):this.sendBuffer.push(o),delete this.flags,this},o.prototype.packet=function(t){t.nsp=this.nsp,this.io.packet(t)},o.prototype.onopen=function(){p("transport is open - connecting"),"/"!==this.nsp&&(this.query?this.packet({type:i.CONNECT,query:this.query}):this.packet({type:i.CONNECT}))},o.prototype.onclose=function(t){p("close (%s)",t),this.connected=!1,this.disconnected=!0,delete this.id,this.emit("disconnect",t)},o.prototype.onpacket=function(t){if(t.nsp===this.nsp)switch(t.type){case i.CONNECT:this.onconnect();break;case i.EVENT:this.onevent(t);break;case i.BINARY_EVENT:this.onevent(t);break;case i.ACK:this.onack(t);break;case i.BINARY_ACK:this.onack(t);break;case i.DISCONNECT:this.ondisconnect();break;case i.ERROR:this.emit("error",t.data)}},o.prototype.onevent=function(t){var e=t.data||[];p("emitting event %j",e),null!=t.id&&(p("attaching ack callback to event"),e.push(this.ack(t.id))),this.connected?_.apply(this,e):this.receiveBuffer.push(e)},o.prototype.ack=function(t){var e=this,n=!1;return function(){if(!n){n=!0;var o=r(arguments);p("sending ack %j",o);var s=u(o)?i.BINARY_ACK:i.ACK;e.packet({type:s,id:t,data:o})}}},o.prototype.onack=function(t){var e=this.acks[t.id];"function"==typeof e?(p("calling ack %s with %j",t.id,t.data),e.apply(this,t.data),delete this.acks[t.id]):p("bad ack %s",t.id)},o.prototype.onconnect=function(){this.connected=!0,this.disconnected=!1,this.emit("connect"),this.emitBuffered()},o.prototype.emitBuffered=function(){var t;for(t=0;t<this.receiveBuffer.length;t++)_.apply(this,this.receiveBuffer[t]);for(this.receiveBuffer=[],t=0;t<this.sendBuffer.length;t++)this.packet(this.sendBuffer[t]);this.sendBuffer=[]},o.prototype.ondisconnect=function(){p("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")},o.prototype.destroy=function(){if(this.subs){for(var t=0;t<this.subs.length;t++)this.subs[t].destroy();this.subs=null}this.io.destroy(this)},o.prototype.close=o.prototype.disconnect=function(){return this.connected&&(p("performing disconnect (%s)",this.nsp),this.packet({type:i.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this},o.prototype.compress=function(t){return this.flags=this.flags||{},this.flags.compress=t,this}},function(t,e,n){function o(t){this.path=t.path,this.hostname=t.hostname,this.port=t.port,this.secure=t.secure,this.query=t.query,this.timestampParam=t.timestampParam,this.timestampRequests=t.timestampRequests,this.readyState="",this.agent=t.agent||!1,this.socket=t.socket,this.enablesXDR=t.enablesXDR,this.pfx=t.pfx,this.key=t.key,this.passphrase=t.passphrase,this.cert=t.cert,this.ca=t.ca,this.ciphers=t.ciphers,this.rejectUnauthorized=t.rejectUnauthorized,this.forceNode=t.forceNode,this.extraHeaders=t.extraHeaders,this.localAddress=t.localAddress}var i=n(2),s=n(3);t.exports=o,s(o.prototype),o.prototype.onError=function(t,e){var n=new Error(t);return n.type="TransportError",n.description=e,this.emit("error",n),this},o.prototype.open=function(){return"closed"!==this.readyState&&""!==this.readyState||(this.readyState="opening",this.doOpen()),this},o.prototype.close=function(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this},o.prototype.send=function(t){if("open"!==this.readyState)throw new Error("Transport not open");this.write(t)},o.prototype.onOpen=function(){this.readyState="open",this.writable=!0,this.emit("open")},o.prototype.onData=function(t){var e=i.decodePacket(t,this.socket.binaryType);this.onPacket(e)},o.prototype.onPacket=function(t){this.emit("packet",t)},o.prototype.onClose=function(){this.readyState="closed",this.emit("close")}},function(t,e,n){function o(t){var e=t&&t.forceBase64;e&&(this.supportsBinary=!1),this.perMessageDeflate=t.perMessageDeflate,i.call(this,t)}var i=n(9),s=n(2),r=n(14),a=n(33),c=n(42),p=n(1)("engine.io-client:websocket");t.exports=o,a(o,i),o.prototype.name="wx",o.prototype.supportsBinary=!0,o.prototype.doOpen=function(){if(this.check()){var t=this.uri(),e={agent:this.agent,perMessageDeflate:this.perMessageDeflate};e.pfx=this.pfx,e.key=this.key,e.passphrase=this.passphrase,e.cert=this.cert,e.ca=this.ca,e.ciphers=this.ciphers,e.rejectUnauthorized=this.rejectUnauthorized,this.extraHeaders&&(e.headers=this.extraHeaders),this.localAddress&&(e.localAddress=this.localAddress),this.isOk=!1,this.ws=wx,this.ws.connectSocket({url:t}),void 0===this.ws.binaryType&&(this.supportsBinary=!1),this.ws.supports&&this.ws.supports.binary?(this.supportsBinary=!0,this.ws.binaryType="nodebuffer"):this.ws.binaryType="arraybuffer",this.addEventListeners()}},o.prototype.addEventListeners=function(){var t=this;this.ws.onSocketOpen(function(){t.onOpen(),t.isOk=!0}),this.ws.onSocketClose(function(){t.onClose(),t.isOk=!1}),this.ws.onSocketMessage(function(e){t.onData(e.data)}),this.ws.onSocketError(function(e){t.onError("websocket error",e)})},o.prototype.write=function(t){function e(){n.emit("flush"),setTimeout(function(){n.writable=!0,n.emit("drain")},0)}var n=this;this.writable=!1;for(var o=t.length,i=0,r=o;i<r;i++)!function(t){s.encodePacket(t,n.supportsBinary,function(t){try{n.ws.sendSocketMessage({data:t})}catch(t){p("websocket closed before onclose event")}--o||e()})}(t[i])},o.prototype.onClose=function(){i.prototype.onClose.call(this)},o.prototype.doClose=function(){"undefined"!=typeof this.ws&&this.ws.closeSocket()},o.prototype.uri=function(){var t=this.query||{},e=this.secure?"wss":"ws",n="";this.port&&("wss"===e&&443!==Number(this.port)||"ws"===e&&80!==Number(this.port))&&(n=":"+this.port),this.timestampRequests&&(t[this.timestampParam]=c()),this.supportsBinary||(t.b64=1),t=r.encode(t),t.length&&(t="?"+t);var o=this.hostname.indexOf(":")!==-1;return e+"://"+(o?"["+this.hostname+"]":this.hostname)+n+this.path+t},o.prototype.check=function(){return!this.isOk}},function(t,e){var n=[].slice;t.exports=function(t,e){if("string"==typeof e&&(e=t[e]),"function"!=typeof e)throw new Error("bind() requires a function");var o=n.call(arguments,2);return function(){return e.apply(t,o.concat(n.call(arguments)))}}},function(t,e){var n=[].indexOf;t.exports=function(t,e){if(n)return t.indexOf(e);for(var o=0;o<t.length;++o)if(t[o]===e)return o;return-1}},function(t,e){t.exports=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)}},function(t,e){e.encode=function(t){var e="";for(var n in t)t.hasOwnProperty(n)&&(e.length&&(e+="&"),e+=encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return e},e.decode=function(t){for(var e={},n=t.split("&"),o=0,i=n.length;o<i;o++){var s=n[o].split("=");e[decodeURIComponent(s[0])]=decodeURIComponent(s[1])}return e}},function(t,e){var n=/^(?:(?![^:@]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,o=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];t.exports=function(t){var e=t,i=t.indexOf("["),s=t.indexOf("]");i!=-1&&s!=-1&&(t=t.substring(0,i)+t.substring(i,s).replace(/:/g,";")+t.substring(s,t.length));for(var r=n.exec(t||""),a={},c=14;c--;)a[o[c]]=r[c]||"";return i!=-1&&s!=-1&&(a.source=e,a.host=a.host.substring(1,a.host.length-1).replace(/;/g,":"),a.authority=a.authority.replace("[","").replace("]","").replace(/;/g,":"),a.ipv6uri=!0),a}},function(t,e){(function(e){function n(t){return e.Buffer&&e.Buffer.isBuffer(t)||e.ArrayBuffer&&t instanceof ArrayBuffer}t.exports=n}).call(e,function(){return this}())},function(t,e,n){"use strict";var o=n(3),i=n(28),s=n(4),r=function(t){this.init(t)};r.prototype.init=function(t){this.opts=t,this.dataCache={},this.memStore={},this.sync_key=0,this.sync_type=0,void 0!=this.client&&this.client.close(),this.client=i(this.opts.address,{transports:["websocket","polling"]});var e=this,n=o.prototype.emit,s=this.client.onevent;this.client.onevent=function(t){var o=t.data||[];s.call(e.client,t),n.apply(e.client,["*"].concat(o))},this.client.on("*",function(t,n){e.onReceive(t,n)})},r.prototype.onReceive=function(t,e){if(this.opts.debug&&console.log("---<- event:%s, data:%s",t,JSON.stringify(e)),t!==s.EVENTS.EVENT_NOTIFICATION&&t!==s.EVENTS.MSG_SYNC&&t!==s.EVENTS.SYNC_CONVERSATION&&t!==s.EVENTS.SYNC_EVENT&&t!==s.EVENTS.SYNC_RECEIPT&&t!==s.EVENTS.RECEIPT_CHANGE&&t!==s.ROOM_MSG_SYNC&&t!==s.ROOM_MSG_SYNC_HIS){var n=this.dataCache[e.rid];delete e.rid,n&&(0===e.code&&t===s.EVENTS.GET_GROUP_MEMBERS?e.member_list.forEach(function(t){n.userInfoGet&&n.userInfoGet(t.uid,t.mtime),delete t.uid,delete t.mtime}):0===e.code&&t===s.EVENTS.GET_CONVERSATIONS?e.conversations.forEach(function(t){3===t.type&&(n.userInfoGet&&n.userInfoGet(t.key,t.utime),delete t.utime)}):0===e.code&&t===s.EVENTS.GET_FRIEND_LIST?e.friend_list.forEach(function(t){n.userInfoGet&&n.userInfoGet(t.uid,1e3*t.mtime),delete t.uid}):0===e.code&&t===s.EVENTS.GET_BLACK_LIST&&e.black_list.forEach(function(t){delete t.uid}),t===s.EVENTS.ACK?(n.ack&&n.ack({rid:e.rid,data:n.data}),n.cleanAckTimeout()):(n.cleanRespTimeout(),delete this.dataCache[n.rid],e.code&&0!==e.code?n.fail&&n.fail(e):t!=s.EVENTS.S_SINGLE_TEXT_&&t!=s.EVENTS.SEND_GROUP_MSG&&t!=s.EVENTS.SEND_ROOM_MSG?n.hook?n.hook(e,n.success):n.success&&n.success(e):(n.data.msg_id=e.msg_id,t===s.EVENTS.S_SINGLE_TEXT_&&(e.target_username=n.data.content.target_id,e.appkey=n.data.appkey),n.success&&n.success(e,n.data),n.innerCall&&n.innerCall(e.msg_id))))}},r.prototype.generateRid=function(){for(var t=Math.floor(Math.random()*-2147483646+2147483647);this.dataCache[t];)t=Math.floor(Math.random()*-2147483646+2147483647);return t},r.prototype.send=function(t,e){this.opts.debug&&console.log("--->- event:%s, data:%s",t,JSON.stringify(e)),this.client.emit(t,e)},t.exports=r},function(t,e,n){"use strict";var o=n(19);t.exports=o},function(t,e,n){"use strict";var o=n(31),i=n(17),s=n(4),r=n(21),a=n(23),c=n(22),p=n(20)(),u=n(24),h=function(t){var e=t?t:{};this.opts={address:e.address?e.address:s.WSS_ADDRESS,debug:!!e.debug},this.channel=new i(this.opts),this.syncTask=0,this.msgReceipTask=0};h.prototype.init=function(t){var e=this;return e.autoDiscon=!0,t.flag!==s.SYNC_TYPE_OPEN&&t.flag!==s.SYNC_TYPE_CLOSE||(e.channel.sync_type=t.flag),t.fromPlatForm=s.FROM_PLATFORM,new r(this.channel).setEvent(s.EVENTS.INIT).setData(t).send().addHook(function(n,o){e.current_appkey=t.appkey,o&&o(n)})},h.prototype.loginOut=function(){if(this.current_user){this.autoDiscon=!1,this.channel.client.close();var t=this.channel.dataCache;for(var e in t)t[e].cleanAckTimeout(),t[e].cleanRespTimeout();this.current_user=null,this.current_appkey=null,this.channel.init(this.channel.opts)}},h.prototype.login=function(t){this.__checkInit(),t.is_md5||(t.password=p(t.password)),t.version=s.SDK_VERSION;var e=this,n=new r(this.channel).setEvent(s.EVENTS.LOGIN).setData(t).addHook(function(n,o){e.current_user=t.username,u.StorageUtils.removeItems(e.current_user),e.channel.sync_key=0,e.channel.sync_event_key=0,e.channel.msg_receipt_key=0,e.channel.ses_key=s.SESSION_KEY+e.current_appkey+"-"+e.current_user,e.channel.conversations_key=s.CONVERSATION_KEY+e.current_appkey+"-"+e.current_user,e.channel.event_key=s.EVENT_KEY+e.current_appkey+"-"+e.current_user,e._syncCheckTask(),e._receiptReportTask(),e._initConversation(),e.lastMsgs={},e.channel.client.removeListener(s.EVENTS.LOGIN),e._addEventListen(),e.firstLogin=!1,o&&o(n)});return setTimeout(function(){n.send()},500),n},h.prototype._initConversation=function(){var t=this,e=u.StorageUtils.getItem(t.channel.conversations_key);null!==e&&""!==e||(e=JSON.stringify({}),u.StorageUtils.addItem(t.channel.conversations_key,e)),t.conversations=JSON.parse(e)},h.prototype._receiptReportTask=function(){var t=this;t.report=[],t.msgReceipTask=setInterval(function(){t._receiptReport()},s.RECEIPT_REPORT_INTERVAL)},h.prototype._syncCheckTask=function(){var t=this,e=u.StorageUtils.getItem(t.channel.event_key);null!=e&&(t.channel.sync_event_key=e),t._syncCheck({sync_key:t.channel.sync_key,sync_type:t.channel.sync_type,sync_event_key:t.channel.sync_event_key,msg_receipt_key:t.channel.msg_receipt_key}).onSuccess(function(e){e&&0===e.code&&(t.channel.sync_key=e.sync_key,t.channel.sync_type=e.sync_type,t.channel.sync_event_key=e.sync_event_key,t.channel.msg_receipt_key=e.msg_receipt_key,u.StorageUtils.addItem(t.channel.event_key,e.sync_event_key))}),t.syncTask=setInterval(function(){t._syncCheck({sync_key:t.channel.sync_key,sync_type:t.channel.sync_type,sync_event_key:t.channel.sync_event_key,msg_receipt_key:t.channel.msg_receipt_key}).onSuccess(function(e){e&&0===e.code&&(t.channel.sync_key=e.sync_key,t.channel.sync_type=e.sync_type,t.channel.sync_event_key=e.sync_event_key,t.channel.msg_receipt_key=e.msg_receipt_key,u.StorageUtils.addItem(t.channel.event_key,e.sync_event_key))})},s.SYNC_INTERVAL)},h.prototype._syncCheck=function(t){return new r(this.channel).setEvent(s.EVENTS.SYNC_CHECK).setData(t).send()},h.prototype.register=function(t){return this.__checkInit(),t.is_md5||(t.password=p(t.password)),new r(this.channel).setEvent(s.EVENTS.REGISTER).setData(t).send()},h.prototype.getUserInfo=function(t){return this.__checkLogin(),u.StringUtils.isBlack(t.appkey)&&(t.appkey=this.current_appkey),new r(this.channel).setEvent(s.EVENTS.GET_ACROSS_USER_INFO).setData(t).send()},h.prototype.updateSelfInfo=function(t){return this.__checkLogin(),u.StringUtils.isBlack(t.avatar)||delete t.avatar,new r(this.channel).setEvent(s.EVENTS.UPDATE_SELF_INFO).setData(t).send()},h.prototype.updateSelfAvatar=function(t){this.__checkLogin();var e=new r(this.channel).setEvent(s.EVENTS.UPDATE_SELF_INFO),n=this;return this.__uploadFile({appkey:n.current_appkey,username:n.current_user,file:t.avatar,type:"image"},function(t,n){return t?t.is_timeout?e.timeout&&e.timeout(t.data):e.fail&&e.fail(t.data):void e.setData({avatar:n.media_id}).send()}),e},h.prototype.updateSelfPwd=function(t){return this.__checkLogin(),t.is_md5||(t.old_pwd=p(t.old_pwd),t.new_pwd=p(t.new_pwd)),new r(this.channel).setEvent(s.EVENTS.UPDATE_SELF_PWD).setData(t).send()},h.prototype.getConversation=function(){var t=this;return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.GET_CONVERSATIONS).setData({}).send().onUserInfoGet(function(e,n){t[e]=n}).addHook(function(e,n){e.conversations.forEach(function(e){var n;3===e.type?(t[e.key]=e.utime,delete e.utime,n=e.appkey+e.username):(n=e.key,e.gid=e.key),t.conversations[n]?(t.conversations[n].extras?e.extras=t.conversations[n].extras:e.extras={},e.unread_msg_count=t.conversations[n].unread_msg_count):(e.extras={},e.unread_msg_count=0,t.conversations[n]={},t.conversations[n].extras={},t.conversations[n].unread_msg_count=0,t.conversations[n].msg_time=[])}),u.StorageUtils.addItem(t.channel.conversations_key,JSON.stringify(t.conversations)),n&&n(e)})},h.prototype.resetUnreadCount=function(t){this.__checkLogin();var e,n=this;t.gid?e=String(t.gid):(t.appkey||(t.appkey=n.current_appkey),e=t.appkey+t.username),n.conversations[e]=void 0===n.conversations[e]?{}:n.conversations[e],t.gid?n._updateGroupUnreadCount({gid:t.gid}):n._updateSingleUnreadCount({appkey:t.appkey,username:t.username}),n.conversations[e].unread_msg_count=0,n.conversations[e].msg_time=[];var o=(new Date).getTime();n.lastMsgs[e]&&(o=n.lastMsgs[e].last_msg_time),n.conversations[e].recent_time=o,n.current_conversation=e,u.StorageUtils.addItem(n.channel.conversations_key,JSON.stringify(n.conversations));
},h.prototype.getUnreadMsgCnt=function(t){this.__checkLogin();var e,n=this;return t.gid?e=String(t.gid):(t.appkey||(t.appkey=n.current_appkey),e=t.appkey+t.username),n.conversations[e]||(n.conversations[e]={}),n.conversations[e]=void 0===n.conversations[e]?{}:n.conversations[e],n.conversations[e].unread_msg_count?n.conversations[e].unread_msg_count:0},h.prototype.msgRetract=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.MSG_RETRACT).setData(t).send()},h.prototype.sendSingleMsg=function(t){return this.__checkLogin(),this.__sendMsg({type:"single",target_id:t.target_username,target_name:t.target_nickname,content:t.content,extras:t.extras,msg_body:t.msg_body,appkey:t.appkey,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt})},h.prototype.sendGroupMsg=function(t){return this.__checkLogin(),this.__sendMsg({type:"group",target_id:t.target_gid,target_name:t.target_gname,content:t.content,extras:t.extras,msg_body:t.msg_body,at_list:t.at_list,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt})},h.prototype.sendSinglePic=function(t){return this.__checkLogin(),this.__sendPic({type:"single",target_id:t.target_username,target_name:t.target_nickname,file:t.image,msg_body:t.msg_body,extras:t.extras,appkey:t.appkey,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt})},h.prototype.sendGroupPic=function(t){return this.__checkLogin(),this.__sendPic({type:"group",target_id:t.target_gid,target_name:t.target_gname,file:t.image,msg_body:t.msg_body,extras:t.extras,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt})},h.prototype.sendSingleFile=function(t){return this.__sendVideoOrFile({type:"single",target_id:t.target_username,target_name:t.target_nickname,file:t.file,msg_body:t.msg_body,extras:t.extras,appkey:t.appkey,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt},"file")},h.prototype.sendGroupFile=function(t){return this.__sendVideoOrFile({type:"group",target_id:t.target_gid,target_name:t.target_gname,file:t.file,msg_body:t.msg_body,extras:t.extras,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt},"file")},h.prototype.sendSingleVedio=function(t){return this.__sendVideoOrFile({type:"single",target_id:t.target_username,target_name:t.target_nickname,file:t.file,msg_body:t.msg_body,extras:t.extras,appkey:t.appkey,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt},"video")},h.prototype.sendGroupVedio=function(t){return this.__sendVideoOrFile({type:"group",target_id:t.target_gid,target_name:t.target_gname,file:t.file,msg_body:t.msg_body,extras:t.extras,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt},"video")},h.prototype.sendSingleLocation=function(t){return this.__checkLogin(),this.__sendLocation({type:"single",target_id:t.target_username,target_name:t.target_nickname,latitude:t.latitude,longitude:t.longitude,scale:t.scale,label:t.label,msg_body:t.msg_body,extras:t.extras,appkey:t.appkey,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt})},h.prototype.sendGroupLocation=function(t){return this.__checkLogin(),this.__sendLocation({type:"group",target_id:t.target_gid,target_name:t.target_gname,latitude:t.latitude,longitude:t.longitude,scale:t.scale,label:t.label,msg_body:t.msg_body,extras:t.extras,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt})},h.prototype.sendSingleCustom=function(t){return this.__checkLogin(),this.__sendCustom({type:"single",target_id:t.target_username,target_name:t.target_nickname,custom:t.custom,extras:t.extras,msg_body:t.msg_body,appkey:t.appkey,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt})},h.prototype.sendGroupCustom=function(t){return this.__checkLogin(),this.__sendCustom({type:"group",target_id:t.target_gid,target_name:t.target_gname,custom:t.custom,msg_body:t.msg_body,extras:t.extras,no_offline:t.no_offline,no_notification:t.no_notification,custom_notification:t.custom_notification,need_receipt:t.need_receipt})},h.prototype.createGroup=function(t){this.__checkLogin();var e=this,n=new r(this.channel).setEvent(s.EVENTS.CREATE_GROUP);return t.avatar?this.__uploadFile({appkey:e.current_appkey,username:e.current_user,file:t.avatar,type:"image"},function(e,o){return e?e.is_timeout?n.timeout&&n.timeout(e.data):n.fail&&n.fail(e.data):(delete t.avatar,t.media_id=o.media_id,void n.setData(t).send())}):n.setData(t).send(),n},h.prototype.exitGroup=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.EXIT_GROUP).setData(t).send()},h.prototype.getGroups=function(){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.GET_GROUPS_LIST).setData({}).send().addHook(function(t,e){t.group_list.forEach(function(t){t.group_type=t.flag,delete t.flag}),e&&e(t)})},h.prototype.getGroupInfo=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.GET_GROUP_INFO).setData(t).send().addHook(function(t,e){t.group_info.group_type=t.group_info.flag,delete t.group_info.flag,e&&e(t)})},h.prototype.updateGroupInfo=function(t){this.__checkLogin();var e=this,n=new r(this.channel).setEvent(s.EVENTS.UPDATE_GROUP_INFO);return t.avatar?this.__uploadFile({appkey:e.current_appkey,username:e.current_user,file:t.avatar,type:"image"},function(e,o){return e?e.is_timeout?n.timeout&&n.timeout(e.data):n.fail&&n.fail(e.data):(delete t.avatar,t.media_id=o.media_id,void n.setData(t).send())}):n.setData(t).send(),n},h.prototype.getGroupMembers=function(t){var e=this;return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.GET_GROUP_MEMBERS).setData(t).send().onUserInfoGet(function(t,n){e[t]=n})},h.prototype.addGroupMembers=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.ADD_ACROSS_GROUP_MEMBER).setData(t).send()},h.prototype.delGroupMembers=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.DEL_ACROSS_GROUP_MEMBER).setData(t).send()},h.prototype.getNoDisturb=function(){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.NO_DISTURB).setData({version:0}).send().addHook(function(t,e){t.no_disturb.groups.forEach(function(t){t.group_type=t.flag,delete t.flag}),e&&e(t)})},h.prototype.addSingleNoDisturb=function(t){return this.__checkLogin(),t.version=0,new r(this.channel).setEvent(s.EVENTS.ADD_MSG_NO_DISTURB_SINGLE).setData(t).send()},h.prototype.delSingleNoDisturb=function(t){return this.__checkLogin(),t.version=0,new r(this.channel).setEvent(s.EVENTS.DELETE_MSG_NO_DISTURB_SINGLE).setData(t).send()},h.prototype.addGroupNoDisturb=function(t){return this.__checkLogin(),t.version=0,new r(this.channel).setEvent(s.EVENTS.ADD_MSG_NO_DISTURB_GROUP).setData(t).send()},h.prototype.delGroupNoDisturb=function(t){return this.__checkLogin(),t.version=0,new r(this.channel).setEvent(s.EVENTS.DELETE_MSG_NO_DISTURB_GROUP).setData(t).send()},h.prototype.addGlobalNoDisturb=function(){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.ADD_MSG_NO_DISTURB_GLOBAL).setData({version:0}).send()},h.prototype.delGlobalNoDisturb=function(){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.DELETE_MSG_NO_DISTURB_GLOBAL).setData({version:0}).send()},h.prototype.getBlacks=function(){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.GET_BLACK_LIST).setData({version:0}).send()},h.prototype.addSingleBlacks=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.ADD_BLACK_LIST).setData(t).send()},h.prototype.delSingleBlacks=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.DEL_BLACK_LIST).setData(t).send()},h.prototype.getFriendList=function(){var t=this;return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.GET_FRIEND_LIST).setData({}).send().onUserInfoGet(function(e,n){t[e]=n})},h.prototype.addFriend=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.ADD_FRIEND).setData(t).send()},h.prototype.acceptFriend=function(t){return this.__checkLogin(),t.why="yes",t.from_type=s.FRIEND_INVITED,new r(this.channel).setEvent(s.EVENTS.ADD_FRIEND).setData(t).send()},h.prototype.declineFriend=function(t){return this.__checkLogin(),t.why&&""!=t.why.trim()||(t.why="no"),t.from_type=s.FRIEND_INVITED,new r(this.channel).setEvent(s.EVENTS.ADD_FRIEND).setData(t).send()},h.prototype.delFriend=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.DEL_FRIEND).setData(t).send()},h.prototype.updateFriendMemo=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.UPDATE_FRIEND_MEMO).setData(t).send()},h.prototype.addGroupShield=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.ADD_MSG_SHIELD_GROUP).setData(t).send()},h.prototype.delGroupShield=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.DEL_MSG_SHIELD_GROUP).setData(t).send()},h.prototype.groupShieldList=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.LIST_SHIELD_GROUP).setData(t).send().addHook(function(t,e){t.groups.forEach(function(t){t.group_type=t.flag,delete t.flag}),e&&e(t)})},h.prototype.getResource=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.GET_RESOURCE).setData(t).send()},h.prototype._updateGroupUnreadCount=function(t){this.__checkLogin(),t.type=4,new r(this.channel).setEvent(s.EVENTS.UNREAD_GROUP_COUNT).setData(t).send()},h.prototype._updateSingleUnreadCount=function(t){this.__checkLogin(),t.type=3,new r(this.channel).setEvent(s.EVENTS.UNREAD_SINGLE_COUNT).setData(t).send()},h.prototype.msgUnreadList=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.MSG_UNREAD_LIST).setData(t).send()},h.prototype.addGroupReceiptReport=function(t){this.__checkLogin();var e=this;if(!(t.msg_ids instanceof Array)||0===t.msg_ids.length)return void console.error("msg_ids is not Array type or msg_ids size=0");t.key=t.gid,t.type=4;var n=new a(t);return e.report.push(n),setTimeout(function(){e._checkReportSize()>=s.RECEIPT_REPORT_MAX_SIZE&&e._receiptReport()},50),n},h.prototype.addSingleReceiptReport=function(t){this.__checkLogin();var e=this;if(!(t.msg_ids instanceof Array)||0===t.msg_ids.length)return void console.error("msg_ids is not Array type or msg_ids size=0");t.appkey||(t.appkey=e.current_appkey),t.type=3,t.key=t.appkey+t.username;var n=new a(t);return e.report.push(n),setTimeout(function(){e._checkReportSize()>=s.RECEIPT_REPORT_MAX_SIZE&&e._receiptReport()},50),n},h.prototype._checkReportSize=function(){var t=this,e=0;return t.report.forEach(function(t){e+=t.args.msg_ids.length}),e},h.prototype.transSingleMsg=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.TRANS_MSG_SINGLE).setData(t).send()},h.prototype.transGroupMsg=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.TRANS_MSG_GROUP).setData(t).send()},h.prototype.transPlatMsg=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.TRANS_MSG_PLATFORM).setData(t).send()},h.prototype.updateConversation=function(t){this.__checkLogin();var e=this;t.appkey||(t.appkey=e.current_appkey);var n;t.gid?n=t.gid:t.username&&(n=t.appkey+t.username),n&&t.extras&&(e.conversations[n]||(e.conversations[n]={}),e.conversations[n].extras=t.extras),u.StorageUtils.addItem(e.channel.conversations_key,JSON.stringify(e.conversations))},h.prototype.addGroupMemberResp=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.ADMIN_ADD_GROUP_MEMBER).setData(t).send()},h.prototype.joinGroup=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.APPLY_JOIN_GROUP).setData(t).send()},h.prototype.getAppkeyChatrooms=function(t){return this.__checkLogin(),!t&&(t={}),new r(this.channel).setEvent(s.EVENTS.ROOM_LIST).setData(t).send().addHook(function(t,e){t.result.rooms.forEach(function(t){u.Cache.rooms[t.id]=t}),e&&e(t)})},h.prototype.getSelfChatrooms=function(){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.ROOM_LIST_SELF).setData({}).send().addHook(function(t,e){t.chat_rooms.forEach(function(t){u.Cache.rooms[t.id]=t}),e&&e(t)})},h.prototype.getChatroomInfo=function(t){this.__checkLogin();var e=new r(this.channel);return u.Cache.rooms[t.id]?setTimeout(function(){var n={};n.code=0,n.message="success",n.info=u.Cache.rooms[t.id],e.cleanRespTimeout(),e.success&&e.success(n)},100):e.setEvent(s.EVENTS.ROOM_INFO).setData(t).send().addHook(function(t,e){u.Cache.rooms[t.info.id]=t.info,e&&e(t)}),e},h.prototype.enterChatroom=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.JOIN_ROOM).setData(t).send()},h.prototype.exitChatroom=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.EXIT_ROOM).setData(t).send()},h.prototype.sendChatroomMsg=function(t){return this.__checkLogin(),t.target_rname||(t.target_rname=void 0===u.Cache.rooms[t.target_rid]?"":u.Cache.rooms[t.target_rid].name),this.__sendMsg({type:"chatroom",target_id:t.target_rid,target_name:t.target_rname,content:t.content,extras:t.extras,msg_body:t.msg_body})},h.prototype.sendChatroomPic=function(t){return this.__checkLogin(),t.target_rname||(t.target_rname=void 0===u.Cache.rooms[t.target_rid]?"":u.Cache.rooms[t.target_rid].name),this.__sendPic({type:"chatroom",target_id:t.target_rid,target_name:t.target_rname,file:t.image,extras:t.extras,msg_body:t.msg_body})},h.prototype.sendChatroomFile=function(t){return this.__checkLogin(),t.target_rname||(t.target_rname=void 0===u.Cache.rooms[t.target_rid]?"":u.Cache.rooms[t.target_rid].name),this.__sendVideoOrFile({type:"chatroom",target_id:t.target_rid,target_name:t.target_rname,file:t.file,extras:t.extras,msg_body:t.msg_body},"file")},h.prototype.sendChatroomVideo=function(t){return this.__checkLogin(),t.target_rname||(t.target_rname=void 0===u.Cache.rooms[t.target_rid]?"":u.Cache.rooms[t.target_rid].name),this.__sendVideoOrFile({type:"chatroom",target_id:t.target_rid,target_name:t.target_rname,file:t.file,msg_body:t.msg_body,extras:t.extras},"video")},h.prototype.sendChatroomCustom=function(t){return this.__checkLogin(),t.target_rname||(t.target_rname=void 0===u.Cache.rooms[t.target_rid]?"":u.Cache.rooms[t.target_rid].name),this.__sendCustom({type:"chatroom",target_id:t.target_rid,target_name:t.target_rname,custom:t.custom,extras:t.extras,msg_body:t.msg_body})},h.prototype.sendChatroomLocation=function(t){return this.__checkLogin(),t.target_rname||(t.target_rname=void 0===u.Cache.rooms[t.target_rid]?"":u.Cache.rooms[t.target_rid].name),this.__sendLocation({type:"chatroom",target_id:t.target_rid,target_name:t.target_rname,latitude:t.latitude,longitude:t.longitude,scale:t.scale,label:t.label,extras:t.extras,msg_body:t.msg_body})},h.prototype.addGroupMemSilence=function(t){return this.__checkLogin(),t.keep_silence=!0,new r(this.channel).setEvent(s.EVENTS.GROUP_MEM_SILENCE).setData(t).send()},h.prototype.delGroupMemSilence=function(t){return this.__checkLogin(),t.keep_silence=!1,new r(this.channel).setEvent(s.EVENTS.GROUP_MEM_SILENCE).setData(t).send()},h.prototype.dissolveGroup=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.DISSOLVE_GROUP).setData(t).send()},h.prototype.addGroupKeeper=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.ADD_GROUP_KEEPER).setData(t).send()},h.prototype.delGroupKeeper=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.DEL_GROUP_KEEPER).setData(t).send()},h.prototype.changeGroupAdmin=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.CHANGE_GROUP_ADMIN).setData(t).send()},h.prototype.getAppkeyPublicGroups=function(t){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.PUBLIC_GROUP_LIST).setData(t).send().addHook(function(t,e){t.result.groups.forEach(function(t){t.group_type=t.flag,delete t.flag}),e&&e(t)})},h.prototype.isInit=function(){return!!this.current_appkey},h.prototype.isLogin=function(){return!!this.current_user},h.prototype.isConnect=function(){return!!this.channel.client.connected},h.prototype._addEventListen=function(){var t=this;t.channel.client.on(s.EVENTS.MSG_SYNC,function(e){t._onMsgReceive(e)}),t.channel.client.on(s.EVENTS.EVENT_NOTIFICATION,function(e){t._onEventNotification(e)}),t.channel.client.on(s.EVENTS.SYNC_CONVERSATION,function(e){t._onSyncConversation(e)}),t.channel.client.on(s.EVENTS.SYNC_EVENT,function(e){t._onSyncEvent(e)}),t.channel.client.on(s.EVENTS.SYNC_RECEIPT,function(e){t._onSyncMsgReceipt(e)}),t.channel.client.on(s.EVENTS.RECEIPT_CHANGE,function(e){t._onMsgReceiptChange(e)}),t.channel.client.on(s.EVENTS.TRANS_MSG_REC,function(e){t._onTransMsgRec(e)}),t.channel.client.on(s.EVENTS.ROOM_MSG_SYNC,function(e){t._onRoomMsg(e)}),t.channel.client.on(s.EVENTS.ROOM_MSG_SYNC_HIS,function(e){t._onSyncRoomMsg(e)}),t.channel.client.on("disconnect",function(){t._onDisconnect()})},h.prototype.onRoomMsg=function(t){this._onRoomMsgFn=t},h.prototype._onRoomMsg=function(t){this._onRoomMsgFn&&this._onRoomMsgFn(t)},h.prototype._onSyncRoomMsg=function(t){var e=this;t.messages.forEach(function(n){u.Cache.rooms[n.room_id]||e.getChatroomInfo({id:t.room_id}),n.msgs&&n.msgs.forEach(function(t){e._onRoomMsgFn&&e._onRoomMsgFn(t)})})},h.prototype.onMsgReceive=function(t){this._onMsgReceiveFn=t},h.prototype._onMsgReceive=function(t){var e=[],n=this;Array.prototype.push.apply(e,t.messages.map(function(t){return{msg_id:t.msg_id,msg_type:t.msg_type,from_uid:t.from_uid,from_gid:t.from_gid}})),t.messages.forEach(function(t){if(t.content.sui_mtime&&n[t.from_uid]&&t.content.sui_mtime>new Date(n[t.from_uid]).getTime()/1e3){n[t.from_uid]=1e3*t.content.sui_mtime;var e={};e.from_username=t.content.from_id,e.from_appkey=t.content.from_appkey,e.mtime=t.content.sui_mtime,delete t.content.sui_mtime,n._updateInfoEventFun&&n._updateInfoEventFun(e)}var o;3===t.msg_type?(t.key=t.from_uid,t.from_username=t.content.from_id,t.from_appkey=t.content.from_appkey,o=t.from_appkey+t.from_username):(t.key=t.from_gid,o=String(t.from_gid)),0===t.msg_level?t.msg_level="normal":1===t.msg_level&&(t.msg_level="across");var i=!1;if(t.from_appkey===n.current_appkey&&t.from_username===n.current_user){i=!0;var a=void 0===t.content.target_appkey||""===t.content.target_appkey?t.content.from_appkey:t.content.target_appkey;o=a+t.content.target_id}n.lastMsgs[o]={last_msg_time:t.ctime_ms},n.conversations[o]||(n.conversations[o]={},n.conversations[o].extras={},n.conversations[o].unread_msg_count=0,n.conversations[o].msg_time=[]),n.current_conversation===o||i?(n.conversations[o].recent_time=t.ctime_ms,n.conversations[o].unread_msg_count=0,n.conversations[o].msg_time=[]):(n.conversations[o].unread_msg_count=n.conversations[o].unread_msg_count+1,n.conversations[o].msg_time.push(t.ctime_ms)),new r(n.channel).setEvent(s.EVENTS.MSG_RECV_V2).setData({msg_id:t.msg_id,msg_type:t.msg_type,from_uid:t.from_uid,from_gid:t.from_gid}).send()}),u.StorageUtils.addItem(n.channel.conversations_key,JSON.stringify(n.conversations)),this._onMsgReceiveFn&&this._onMsgReceiveFn(t)},h.prototype.onEventNotification=function(t){this._onEventNotificationFn=t},h.prototype._onEventNotification=function(t){var e=this;if(200===t.event_type)return void(3===t.description.type?e._dealMutlReadEvent(t.description.type,t.ctime_ms,t.description.appkey,t.description.username):e._dealMutlReadEvent(t.description.type,t.ctime_ms,t.description.gid));var n=e.__eventDateFilter(t);if(56===t.event_type&&10==t.extra?(n.by_self=!1,delete n.extra):56===t.event_type&&59==t.extra&&(n.by_self=!0,delete n.extra),1===t.event_type){var o={event_id:t.event_id,event_type:t.event_type,from_uid:t.from_uid,gid:t.gid};new r(e.channel).setEvent(s.EVENTS.EVENT_NOTIFICATION).setData(o).send()}e._onEventNotificationFn&&e._onEventNotificationFn(n),s.LOGIN_OUT_EVENT.indexOf(t.event_type)!=-1&&e.loginOut()},h.prototype.onSyncConversation=function(t){this._onSyncConversationFn=t},h.prototype._onSyncConversation=function(t){var e=this;e.channel.sync_key=t.sync_key,t.messages&&(t.messages.forEach(function(t){var n,o=0;n=3===t.msg_type?t.from_appkey+t.from_username:String(t.from_gid);var i=t.msgs[t.msgs.length-1].ctime_ms;if(e.current_conversation===n)e.conversations[n]=void 0===e.conversations[n]?{}:e.conversations[n],e.conversations[n].unread_msg_count=o,e.conversations[n].recent_time=i,e.conversations[n].msg_time=[];else if(e.conversations[n]&&e.conversations[n].recent_time){var r=e.conversations[n].recent_time;t.msgs.forEach(function(t){t.ctime_ms<=r||t.content.from_appkey===e.current_appkey&&t.content.from_id===e.current_user?(o=0,e.conversations[n].msg_time=[]):(o++,e.conversations[n].msg_time.push(t.ctime_ms))}),e.channel.sync_type===s.SYNC_TYPE_OPEN?e.conversations[n].unread_msg_count=o:e.conversations[n].unread_msg_count+=o}else o=t.unsync_count,e.conversations[n]=void 0===e.conversations[n]?{}:e.conversations[n],e.conversations[n].unread_msg_count=o,t.msgs.length-1-o<0?e.conversations[n].recent_time=-1:e.conversations[n].recent_time=t.msgs[t.msgs.length-1-o].ctime_ms,e.conversations[n].msg_time=[];e.lastMsgs[n]={last_msg_time:i},delete t.unsync_count,t.unread_msg_count=o}),u.StorageUtils.addItem(e.channel.conversations_key,JSON.stringify(e.conversations)),e._onSyncConversationFn&&t.messages.length>0&&e._onSyncConversationFn(t.messages));var n={sync_key:e.channel.sync_key};new r(e.channel).setEvent(s.EVENTS.SYNC_CONVERSATION_ACK).setData(n).send()},h.prototype.onSyncEvent=function(t){this._onSyncEventFn=t},h.prototype._onSyncEvent=function(t){var e=this;e.channel.sync_event_key=t.sync_key,u.StorageUtils.addItem(e.channel.event_key,t.sync_key);var n={sync_key:e.channel.sync_event_key};new r(e.channel).setEvent(s.EVENTS.SYNC_EVENT_ACK).setData(n).send(),setTimeout(function(){if(e._onSyncEventFn&&t.events&&t.events.length>0){var n=[];t.events.forEach(function(o){if(200===o.event_type)3===o.description.type?e._dealMutlReadEvent(o.description.type,o.ctime_ms,o.description.appkey,o.description.username):e._dealMutlReadEvent(o.description.type,o.ctime_ms,o.description.gid);else{var i=e.__eventDateFilter(o);56===t.event_type&&10==t.extra?(delete i.extra,i.by_self=!1):56===t.event_type&&59==t.extra&&(delete i.extra,i.by_self=!0),n.push(i)}}),e._onSyncEventFn(n)}},1700)},h.prototype.onSyncMsgReceipt=function(t){this._onSyncMsgReceiptFn=t},h.prototype._onSyncMsgReceipt=function(t){var e=this;if(e.channel.msg_receipt_key=t.sync_key,e._onSyncMsgReceiptFn&&t.receipts&&t.receipts.length>0){var n={},o=[];t.receipts.forEach(function(t){var e=t.gid;if(3===t.type&&(e=t.appkey+t.username),n[e]){var i=Number(n[e]);Array.prototype.push.apply(o[i].receipt_msgs,t.receipt_msgs)}else t.receipt_msgs.length>0&&(n[e]=String(o.length),o.push(t))}),setTimeout(function(){e._onSyncMsgReceiptFn&&o.length>0&&e._onSyncMsgReceiptFn(o)},1500)}var i={sync_key:e.channel.msg_receipt_key};new r(e.channel).setEvent(s.EVENTS.SYNC_RECEIPT_ACK).setData(i).send()},h.prototype.onMsgReceiptChange=function(t){this._onMsgReceiptChangeFn=t},h.prototype._onMsgReceiptChange=function(t){this._onMsgReceiptChangeFn&&this._onMsgReceiptChangeFn(t)},h.prototype.onUserInfUpdate=function(t){this._updateInfoEventFun=t},h.prototype.onMutiUnreadMsgUpdate=function(t){this._conversationUpdateFun=t},h.prototype.onTransMsgRec=function(t){this._onTransMsgRecFn=t},h.prototype._onTransMsgRec=function(t){this._onTransMsgRecFn&&this._onTransMsgRecFn(t)},h.prototype.onDisconnect=function(t){this._onDisconnectFn=t},h.prototype._onDisconnect=function(){var t=this;clearTimeout(t.syncTask),clearTimeout(t.msgReceipTask),t.autoDiscon&&(t.current_appkey=null,t.current_user=null,t._onDisconnectFn&&t._onDisconnectFn()),this.channel.init(this.channel.opts)},h.prototype._dealMutlReadEvent=function(t,e,n,o){var i,s=this,r={};if(r.type=t,3===t?(i=n+o,r.username=o,r.appkey=n):(i=String(n),r.gid=n),s.conversations[i]=void 0===s.conversations[i]?{msg_time:[],unread_msg_count:0}:s.conversations[i],s.conversations[i].recent_time=e,s.current_conversation===i)s.conversations[i].unread_msg_count=0,s.conversations[i].msg_time=[];else{var a=s.conversations[i].unread_msg_count,c=s.conversations[i].msg_time,p=[],h=0;c.forEach(function(t){t>e&&(h++,p.push(t))}),s.conversations[i].msg_time=p,h<a&&(s.conversations[i].unread_msg_count=h,r.unreadCount=h,u.StorageUtils.addItem(s.channel.conversations_key,JSON.stringify(s.conversations)),s._conversationUpdateFun&&s._conversationUpdateFun(r))}},h.prototype._receiptReport=function(){var t=this;if(t.report.length>0){var e={},n=[];t.report.forEach(function(t){if(e[t.args.key]){var o=Number(e[t.args.key]);Array.prototype.push.apply(n[o].result.msg_ids,t.args.msg_ids),n[o].msg_reports.push(t)}else e[t.args.key]=String(n.length),n.push({result:t.args,msg_reports:[t]})}),t.report=[];for(var o=0;o<n.length;o++)n[o].result.msg_ids=u.ArrayUtils.delRepeatItem(n[o].result.msg_ids),delete n[o].result.key,t._msgReceipt(n[o])}},h.prototype._msgReceipt=function(t){new r(this.channel).setEvent(s.EVENTS.RECEIPT_REPORT).setData({sessions:[t.result]}).send().onSuccess(function(e){t.msg_reports.forEach(function(t){t.success&&t.success(e,t.args.msg_ids)})}).onFail(function(e){t.msg_reports.forEach(function(t){t.fail&&t.fail(e,t.args.msg_ids)})}).onAck(function(e){t.msg_reports.forEach(function(t){t.ack&&t.ack(e,t.args.msg_ids)})}).onTimeout(function(e){t.msg_reports.forEach(function(t){t.timeout&&t.timeout(e,t.args.msg_ids)})})},h.prototype.__eventDateFilter=function(t){var e={};return e.event_id=t.event_id,e.event_type=t.event_type,e.from_username=t.from_username,e.gid=t.gid,e.to_usernames=t.to_usernames,e.ctime=t.ctime,e.extra=t.extra,e.return_code=t.return_code,e.from_appkey=t.from_appkey,e.msg_ids=t.msg_ids,e.from_gid=t.from_gid,e.msgid_list=t.msgid_list,e.to_groups=t.to_groups,e.new_owner=t.new_owner,e.group_name=t.group_name,e.ctime_ms=t.ctime_ms,e.media_id=t.media_id,e.from_nickname=t.from_nickname,e.from_eventid=t.from_eventid,100===t.event_type&&7===t.extra?e.description=JSON.parse(t.description):e.description=t.description,55===t.event_type&&0===t.from_gid?e.type=0:55===t.event_type&&0!=t.from_gid&&(e.type=1),e},h.prototype.__checkConnect=function(){if(!this.channel.client.connected)throw new Error("wss socket not connect")},h.prototype.__checkInit=function(){if(!this.current_appkey)throw new Error("必须执行init操作后能执行此动作")},h.prototype.__checkLogin=function(){if(!this.current_user)throw new Error("必须执行login操作后能执行此动作")},h.prototype.__getUploadToken=function(){return this.__checkLogin(),new r(this.channel).setEvent(s.EVENTS.GET_UPLOAD_TOKEN).setData({}).send()},h.prototype.__uploadFile0=function(t,e){wx.uploadFile({url:s.UPLOAD_FILE+"?type="+t.type,filePath:t.file,name:"file",header:{"X-App-Key":t.appkey,Authorization:"Basic "+o.btoa(t.username+":"+t.token),"jm-channel":s.PLAT_CHANNEL},success:function(t){if(200==t.statusCode)e(null,JSON.parse(t.data));else try{var n=JSON.parse(t.data);e(898061===n.error.code?{code:880210,message:"file size exceed limit"}:n)}catch(t){e({code:880210,message:"file size exceed the limit"})}},fail:function(t){console.error(JSON.parse(t))}})},h.prototype.__uploadFile=function(t,e){var n=this;n.__getUploadToken().onSuccess(function(o){n.__uploadFile0({type:t.type,file:t.file,appkey:t.appkey,username:t.username,token:o.uptoken},e)}).onFail(function(t){e({data:t})}).onTimeout(function(t){e({is_timeout:!0,data:t})})},h.prototype.__sendMsg=function(t){return this.__checkLogin(),new r(this.channel).setEvent("single"===t.type?s.EVENTS.S_SINGLE_TEXT:"group"===t.type?s.EVENTS.SEND_GROUP_MSG:s.EVENTS.SEND_ROOM_MSG).setData(new c(this.current_user,this.current_appkey).setType(t.type).setAppkey(t.appkey).setNeedReceipt(t.need_receipt).setTarget(t.target_id,t.target_name).setText(t.content?t.content:t.msg_body.text,t.content?t.extras:t.msg_body.extras).setAtList(t.at_list).setNoOffline(t.no_offline===!0).setNoNotification(t.no_notification===!0).setCustomNotification(t.custom_notification).build()).send()},h.prototype.__sendPic=function(t){this.__checkLogin();var e=new r(this.channel).setEvent("single"===t.type?s.EVENTS.S_SINGLE_TEXT:"group"===t.type?s.EVENTS.SEND_GROUP_MSG:s.EVENTS.SEND_ROOM_MSG),n=this,o=new c(n.current_user,n.current_appkey).setType(t.type).setAppkey(t.appkey).setNeedReceipt(t.need_receipt).setTarget(t.target_id,t.target_name).setNoOffline(t.no_offline===!0).setNoNotification(t.no_notification===!0).setCustomNotification(t.custom_notification);return t.file?this.__uploadFile({appkey:n.current_appkey,username:n.current_user,file:t.file,type:"image"},function(n,i){return n?n.is_timeout?e.timeout&&e.timeout(n.data):e.fail&&e.fail(n.data):void e.setData(o.setImage(i,t.extras).build()).send()}):e.setData(o.setImage(t.msg_body,t.msg_body.extras).build()).send(),e},h.prototype.__sendVideoOrFile=function(t,e){this.__checkLogin();var n=new r(this.channel).setEvent("single"===t.type?s.EVENTS.S_SINGLE_TEXT:"group"===t.type?s.EVENTS.SEND_GROUP_MSG:s.EVENTS.SEND_ROOM_MSG),o=this,i=new c(o.current_user,o.current_appkey).setType(t.type).setAppkey(t.appkey).setNeedReceipt(t.need_receipt).setTarget(t.target_id,t.target_name).setNoOffline(t.no_offline===!0).setNoNotification(t.no_notification===!0).setCustomNotification(t.custom_notification);return t.file?this.__uploadFile({appkey:o.current_appkey,username:o.current_user,file:t.file,type:"file"},function(o,s){return o?o.is_timeout?n.timeout&&n.timeout(o.data):n.fail&&n.fail(o.data):("video"===e&&(!t.extras&&(t.extras={}),t.extras.video=s.media_id.slice(s.media_id.lastIndexOf(".")+1)),void n.setData(i.setFile(s,t.extras).build()).send())}):n.setData(i.setFile(t.msg_body,t.msg_body.extras).build()).send(),n},h.prototype.__sendVoice=function(t){this.__checkLogin();var e=new r(this.channel).setEvent("single"===t.type?s.EVENTS.S_SINGLE_TEXT:s.EVENTS.SEND_GROUP_MSG),n=this,o=new c(n.current_user,n.current_appkey).setType(t.type).setAppkey(t.appkey).setNeedReceipt(t.need_receipt).setTarget(t.target_id,t.target_name).setNoOffline(t.no_offline===!0).setNoNotification(t.no_notification===!0).setCustomNotification(t.custom_notification);return t.file?this.__uploadFile({appkey:n.current_appkey,username:n.current_user,file:t.file,type:"voice"},function(n,i){return n?n.is_timeout?e.timeout&&e.timeout(n.data):e.fail&&e.fail(n.data):void e.setData(o.setVoice(i,t.extras).build()).send()}):e.setData(o.setVoice(t.msg_body,t.msg_body.extras).build()).send(),e},h.prototype.__sendLocation=function(t){return this.__checkLogin(),new r(this.channel).setEvent("single"===t.type?s.EVENTS.S_SINGLE_TEXT:"group"===t.type?s.EVENTS.SEND_GROUP_MSG:s.EVENTS.SEND_ROOM_MSG).setData(new c(this.current_user,this.current_appkey).setType(t.type).setAppkey(t.appkey).setNeedReceipt(t.need_receipt).setTarget(t.target_id,t.target_name).setLocation(t.latitude?t:t.msg_body,t.latitude?t.extras:t.msg_body.extras).setNoOffline(t.no_offline===!0).setNoNotification(t.no_notification===!0).setCustomNotification(t.custom_notification).build()).send()},h.prototype.__sendCustom=function(t){return this.__checkLogin(),new r(this.channel).setEvent("single"===t.type?s.EVENTS.S_SINGLE_TEXT:"group"===t.type?s.EVENTS.SEND_GROUP_MSG:s.EVENTS.SEND_ROOM_MSG).setData(new c(this.current_user,this.current_appkey).setType(t.type).setAppkey(t.appkey).setNeedReceipt(t.need_receipt).setTarget(t.target_id,t.target_name).setCustom(t.custom?t.custom:t.msg_body,t.custom?t.extras:t.msg_body.extras).setCustom(t.custom).setNoOffline(t.no_offline===!0).setNoNotification(t.no_notification===!0).setCustomNotification(t.custom_notification).build()).send();
},t.exports=h},function(t,e){"use strict";t.exports=function(){function t(t,e){var n=(65535&t)+(65535&e),o=(t>>16)+(e>>16)+(n>>16);return o<<16|65535&n}function e(t,e){return t<<e|t>>>32-e}function n(n,o,i,s,r,a){return t(e(t(t(o,n),t(s,a)),r),i)}function o(t,e,o,i,s,r,a){return n(e&o|~e&i,t,e,s,r,a)}function i(t,e,o,i,s,r,a){return n(e&i|o&~i,t,e,s,r,a)}function s(t,e,o,i,s,r,a){return n(e^o^i,t,e,s,r,a)}function r(t,e,o,i,s,r,a){return n(o^(e|~i),t,e,s,r,a)}function a(e,n){e[n>>5]|=128<<n%32,e[(n+64>>>9<<4)+14]=n;var a,c,p,u,h,_=1732584193,f=-271733879,d=-1732584194,l=271733878;for(a=0;a<e.length;a+=16)c=_,p=f,u=d,h=l,_=o(_,f,d,l,e[a],7,-680876936),l=o(l,_,f,d,e[a+1],12,-389564586),d=o(d,l,_,f,e[a+2],17,606105819),f=o(f,d,l,_,e[a+3],22,-1044525330),_=o(_,f,d,l,e[a+4],7,-176418897),l=o(l,_,f,d,e[a+5],12,1200080426),d=o(d,l,_,f,e[a+6],17,-1473231341),f=o(f,d,l,_,e[a+7],22,-45705983),_=o(_,f,d,l,e[a+8],7,1770035416),l=o(l,_,f,d,e[a+9],12,-1958414417),d=o(d,l,_,f,e[a+10],17,-42063),f=o(f,d,l,_,e[a+11],22,-1990404162),_=o(_,f,d,l,e[a+12],7,1804603682),l=o(l,_,f,d,e[a+13],12,-40341101),d=o(d,l,_,f,e[a+14],17,-1502002290),f=o(f,d,l,_,e[a+15],22,1236535329),_=i(_,f,d,l,e[a+1],5,-165796510),l=i(l,_,f,d,e[a+6],9,-1069501632),d=i(d,l,_,f,e[a+11],14,643717713),f=i(f,d,l,_,e[a],20,-373897302),_=i(_,f,d,l,e[a+5],5,-701558691),l=i(l,_,f,d,e[a+10],9,38016083),d=i(d,l,_,f,e[a+15],14,-660478335),f=i(f,d,l,_,e[a+4],20,-405537848),_=i(_,f,d,l,e[a+9],5,568446438),l=i(l,_,f,d,e[a+14],9,-1019803690),d=i(d,l,_,f,e[a+3],14,-187363961),f=i(f,d,l,_,e[a+8],20,1163531501),_=i(_,f,d,l,e[a+13],5,-1444681467),l=i(l,_,f,d,e[a+2],9,-51403784),d=i(d,l,_,f,e[a+7],14,1735328473),f=i(f,d,l,_,e[a+12],20,-1926607734),_=s(_,f,d,l,e[a+5],4,-378558),l=s(l,_,f,d,e[a+8],11,-2022574463),d=s(d,l,_,f,e[a+11],16,1839030562),f=s(f,d,l,_,e[a+14],23,-35309556),_=s(_,f,d,l,e[a+1],4,-1530992060),l=s(l,_,f,d,e[a+4],11,1272893353),d=s(d,l,_,f,e[a+7],16,-155497632),f=s(f,d,l,_,e[a+10],23,-1094730640),_=s(_,f,d,l,e[a+13],4,681279174),l=s(l,_,f,d,e[a],11,-358537222),d=s(d,l,_,f,e[a+3],16,-722521979),f=s(f,d,l,_,e[a+6],23,76029189),_=s(_,f,d,l,e[a+9],4,-640364487),l=s(l,_,f,d,e[a+12],11,-421815835),d=s(d,l,_,f,e[a+15],16,530742520),f=s(f,d,l,_,e[a+2],23,-995338651),_=r(_,f,d,l,e[a],6,-198630844),l=r(l,_,f,d,e[a+7],10,1126891415),d=r(d,l,_,f,e[a+14],15,-1416354905),f=r(f,d,l,_,e[a+5],21,-57434055),_=r(_,f,d,l,e[a+12],6,1700485571),l=r(l,_,f,d,e[a+3],10,-1894986606),d=r(d,l,_,f,e[a+10],15,-1051523),f=r(f,d,l,_,e[a+1],21,-2054922799),_=r(_,f,d,l,e[a+8],6,1873313359),l=r(l,_,f,d,e[a+15],10,-30611744),d=r(d,l,_,f,e[a+6],15,-1560198380),f=r(f,d,l,_,e[a+13],21,1309151649),_=r(_,f,d,l,e[a+4],6,-145523070),l=r(l,_,f,d,e[a+11],10,-1120210379),d=r(d,l,_,f,e[a+2],15,718787259),f=r(f,d,l,_,e[a+9],21,-343485551),_=t(_,c),f=t(f,p),d=t(d,u),l=t(l,h);return[_,f,d,l]}function c(t){var e,n="";for(e=0;e<32*t.length;e+=8)n+=String.fromCharCode(t[e>>5]>>>e%32&255);return n}function p(t){var e,n=[];for(n[(t.length>>2)-1]=void 0,e=0;e<n.length;e+=1)n[e]=0;for(e=0;e<8*t.length;e+=8)n[e>>5]|=(255&t.charCodeAt(e/8))<<e%32;return n}function u(t){return c(a(p(t),8*t.length))}function h(t,e){var n,o,i=p(t),s=[],r=[];for(s[15]=r[15]=void 0,i.length>16&&(i=a(i,8*t.length)),n=0;n<16;n+=1)s[n]=909522486^i[n],r[n]=1549556828^i[n];return o=a(s.concat(p(e)),512+8*e.length),c(a(r.concat(o),640))}function _(t){var e,n,o="0123456789abcdef",i="";for(n=0;n<t.length;n+=1)e=t.charCodeAt(n),i+=o.charAt(e>>>4&15)+o.charAt(15&e);return i}function f(t){return unescape(encodeURIComponent(t))}function d(t){return u(f(t))}function l(t){return _(d(t))}function g(t,e){return h(f(t),f(e))}function m(t,e){return _(g(t,e))}function y(t,e,n){return e?n?g(e,t):m(e,t):n?d(t):l(t)}return y}},function(t,e,n){"use strict";var o=n(4),i=function(t){this.channel=t,this.rid=this.channel.generateRid(),this.times=1};i.prototype.setEvent=function(t){return this.event=t,this},i.prototype.setData=function(t){return this.data=t,this},i.prototype.onSuccess=function(t){return"function"==typeof t&&(this.success=t),this},i.prototype.onFail=function(t){return"function"==typeof t&&(this.fail=t),this},i.prototype.onTimeout=function(t){if("function"==typeof t){this.timeout=t;var e=this;this.respTimeoutTaskId=setTimeout(function(){e.respTimeoutTask()},o.RESP_TIMEOUT)}return this},i.prototype.onAck=function(t){return"function"==typeof t&&(this.ack=t),this},i.prototype.onInnerCall=function(t){return"function"==typeof t&&(this.innerCall=t),this},i.prototype.onUserInfoGet=function(t){return"function"==typeof t&&(this.userInfoGet=t),this},i.prototype.addHook=function(t){return"function"==typeof t&&(this.hook=t),this},i.prototype.ackTimeoutTask=function(){if(this.times<o.RETRY_TIMES){this.channel.send(this.event,this._data),this.times++;var t=this;this.ackTimeoutTaskId=setTimeout(function(){t.ackTimeoutTask()},o.ACK_TIMEOUT)}else this.timeout&&this.timeout({rid:this.rid,data:this.data,response_timeout:!1}),delete this.channel.dataCache[this.rid];return this},i.prototype.respTimeoutTask=function(){if(this.times<o.RETRY_TIMES){this.channel.send(this.event,this._data),this.times++;var t=this;this.respTimeoutTaskId=setTimeout(function(){t.respTimeoutTask()},o.RESP_TIMEOUT)}else this.timeout&&this.timeout({rid:this.rid,data:this.data,response_timeout:!0}),delete this.channel.dataCache[this.rid];return this},i.prototype.cleanAckTimeout=function(){return this.ackTimeoutTaskId&&clearTimeout(this.ackTimeoutTaskId),this},i.prototype.cleanRespTimeout=function(){return this.respTimeoutTaskId&&clearTimeout(this.respTimeoutTaskId),this},i.prototype.send=function(){if(!this.event||!this.data)return void console.error("发send fail，event and data can not empty");var t=this;return this.ackTimeoutTaskId=setTimeout(function(){t.ackTimeoutTask()},o.ACK_TIMEOUT),this._data=JSON.parse(JSON.stringify(this.data)),this._data.rid=this.rid,this.channel.send(this.event,this._data),this.channel.dataCache[this.rid]=this,this},t.exports=i},function(t,e){"use strict";var n=function(t,e){this.current_user=t,this.current_appkey=e,this.version=1,this.from_platform="web"};n.prototype.setNeedReceipt=function(t){return this.need_receipt=t,this},n.prototype.setNoOffline=function(t){return this.no_offline=t,this},n.prototype.setNoNotification=function(t){return this.no_notification=t,this},n.prototype.setType=function(t){return this.type=t,this},n.prototype.setAtList=function(t){return this.at_list=t,this},n.prototype.setAppkey=function(t){return t&&(this.appkey=t),this},n.prototype.setTarget=function(t,e){return this.target_id=t.toString(),this.target_name=e,this},n.prototype.setFromName=function(t){return this.from_name=t,this},n.prototype.setText=function(t,e){return this.msg_type="text",this.msg_body={text:t},e&&(this.msg_body.extras=e),this},n.prototype.setImage=function(t,e){return this.msg_type="image",this.msg_body={media_id:t.media_id,media_crc32:t.media_crc32,width:t.width,height:t.height,format:t.format,fsize:t.fsize},e&&(this.msg_body.extras=e),this},n.prototype.setFile=function(t,e){return this.msg_type="file",this.msg_body={media_id:t.media_id,media_crc32:t.media_crc32,hash:t.hash,fsize:t.fsize,fname:t.fname},e&&(this.msg_body.extras=e),this},n.prototype.setVoice=function(t,e){return this.msg_type="voice",this.msg_body={media_id:t.media_id,media_crc32:t.media_crc32,hash:t.hash,fsize:t.fsize,duration:t.duration,format:t.format},e&&(this.msg_body.extras=e),this},n.prototype.setCustomNotification=function(t){return t&&(this.custom_notification=t),this},n.prototype.setLocation=function(t,e){return this.msg_type="location",this.msg_body={latitude:t.latitude,longitude:t.longitude,scale:t.scale,label:t.label},e&&(this.msg_body.extras=e),this},n.prototype.setCustom=function(t){return this.msg_type="custom",this.msg_body=t,this},n.prototype.build=function(){var t=this.current_user;if("single"!=this.type&&"group"!=this.type&&"across_single"!=this.type&&"chatroom"!=this.type)return console.log("type must be single or group or chatroom");if(!this.target_id)return console.error("target_id must not null");if("text"==this.msg_type){if(!this.msg_body.text&&this.at_list&&"single"!=this.type)this.msg_body.text=" ";else if(!this.msg_body.text&&!this.at_list)return console.error("未设置文本消息内容")}else if("custom"==this.msg_type){if(!this.msg_body)return console.error("custom对象不能为空")}else if("image"==this.msg_type){if(!this.msg_body.media_id)return console.error("未设置image消息media_id字段");if(!this.msg_body.media_crc32)return console.error("未设置image消息media_crc32字段");if(!this.msg_body.width)return console.error("未设置image消息width字段");if(!this.msg_body.height)return console.error("未设置image消息height字段")}else if("file"==this.msg_type){if(!this.msg_body.media_id)return console.error("未设置file消息media_id字段");if(!this.msg_body.media_crc32)return console.error("未设置file消息media_crc32字段");if(!this.msg_body.fsize)return console.error("未设置file消息fsize字段");if(!this.msg_body.fname)return console.error("未设置file消息fname字段")}else if("location"==this.msg_type){if(!this.msg_body.latitude)return console.error("未设置location消息latitude字段");if(!this.msg_body.longitude)return console.error("未设置location消息longitude字段");if(!this.msg_body.scale)return console.error("未设置location消息scale字段");if(!this.msg_body.label)return console.error("未设置location消息label字段")}else{if("voice"!=this.msg_type)return console.error("请设置合法的msg_type");if(!this.msg_body.media_id)return console.error("未设置voice消息media_id字段");if(!this.msg_body.media_crc32)return console.error("未设置voice消息media_crc32字段")}var e={version:this.version,target_type:this.type,from_platform:this.from_platform,target_id:this.target_id,target_name:this.target_name,from_id:t,from_name:this.from_name,create_time:(new Date).getTime(),msg_type:this.msg_type,msg_body:this.msg_body};this.appkey&&(e.target_appkey=this.appkey,e.from_appkey=this.current_appkey);var n={content:e};if(n.no_offline=this.no_offline,n.no_notification=this.no_notification,n.custom_notification=this.custom_notification,n.target_name=e.target_name,n.need_receipt=this.need_receipt,"single"==e.target_type)n.target_name=e.target_id;else if(n.target_gid=e.target_id,this.at_list&&this.at_list instanceof Array)n.users=this.at_list;else if(this.at_list&&!(this.at_list instanceof Array))return console.error("参数值不合法，at_list必须为数组类型");return this.appkey?n.appkey=this.appkey:n.appkey=this.current_appkey,n},t.exports=n},function(t,e){"use strict";var n=function(t){this.args=t};n.prototype.onSuccess=function(t){return"function"==typeof t&&(this.success=t),this},n.prototype.onFail=function(t){return"function"==typeof t&&(this.fail=t),this},n.prototype.onTimeout=function(t){return"function"==typeof t&&(this.timeout=t),this},n.prototype.onAck=function(t){return"function"==typeof t&&(this.ack=t),this},t.exports=n},function(t,e){"use strict";var n={};n.isBlack=function(t){return!(t&&"string"==typeof t&&t.length>0)};var o={};o.addItem=function(t,e){wx.setStorage({key:t.toString(),data:e})},o.removeItems=function(t){wx.getStorageInfo({success:function(e){var n=e.keys,o=[];n.forEach(function(e){try{var n=wx.getStorageSync(e);n===t&&o.push(e)}catch(t){}}),o.forEach(function(t){wx.removeStorage({key:t,success:function(t){}})})}})},o.getItem=function(t){return wx.getStorageSync(t)};var i={};i.delRepeatItem=function(t){var e=[];return t.forEach(function(t){e.indexOf(t)===-1&&null!=t&&e.push(t)}),e};var s={};s.rooms={},t.exports={StringUtils:n,StorageUtils:o,ArrayUtils:i,Cache:s}},function(t,e,n){var o=n(26);e.protocol=3;var i=e.packets={open:0,close:1,ping:2,pong:3,message:4,upgrade:5,noop:6},s=o(i),r={type:"error",data:"parser error"};e.encodePacket=function(t,e,n,o){"function"==typeof n&&(o=n,n=null);var s=i[t.type];return void 0!==t.data&&(s+=String(t.data)),o(""+s)},e.decodePacket=function(t,e,n){if(void 0===t)return r;if("string"==typeof t){var o=t.charAt(0);return Number(o)==o&&s[o]?t.length>1?{type:s[o],data:t.substring(1)}:{type:s[o]}:r}}},function(t,e){t.exports=Object.keys||function(t){var e=[],n=Object.prototype.hasOwnProperty;for(var o in t)n.call(t,o)&&e.push(o);return e}},function(t,e,n){t.exports=n(30),t.exports.parser=n(2)},function(t,e,n){function o(t,e){"object"==typeof t&&(e=t,t=void 0),e=e||{};var n,o=s(t),r=o.source,u=o.id,h=o.path,_=p[u]&&h in p[u].nsps,f=e.forceNew||e["force new connection"]||!1===e.multiplex||_;return f?(c("ignoring socket cache for %s",r),n=a(r,e)):(p[u]||(c("new io instance for %s",r),p[u]=a(r,e)),n=p[u]),o.query&&!e.query?e.query=o.query:e&&"object"==typeof e.query&&(e.query=i(e.query)),n.socket(o.path,e)}function i(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return e.join("&")}var s=n(29),r=n(5),a=n(6),c=n(1)("socket.io-client");t.exports=e=o;var p=e.managers={};e.protocol=r.protocol,e.connect=o,e.Manager=n(6),e.Socket=n(8)},function(t,e,n){function o(t,e){var n=t;null==t&&(t=e.protocol+"//"+e.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?e.protocol+t:e.host+t),/^(https?|wss?):\/\//.test(t)||(s("protocol-less url %s",t),t="undefined"!=typeof e?e.protocol+"//"+t:"https://"+t),s("parse %s",t),n=i(t)),n.port||(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";var o=n.host.indexOf(":")!==-1,r=o?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+r+":"+n.port,n.href=n.protocol+"://"+r+(e&&e.port===n.port?"":":"+n.port),n}var i=n(15),s=n(1)("socket.io-client:url");t.exports=o},function(t,e,n){function o(t,e){return this instanceof o?(e=e||{},t&&"object"==typeof t&&(e=t,t=null),t?(t=u(t),e.hostname=t.host,e.secure="https"===t.protocol||"wss"===t.protocol,e.port=t.port,t.query&&(e.query=t.query)):e.host&&(e.hostname=u(e.host).host),this.secure=!0,this.port="443",this.agent=e.agent||!1,this.hostname=e.hostname,this.port=e.port,this.query=e.query||{},"string"==typeof this.query&&(this.query=_.decode(this.query)),this.upgrade=!1!==e.upgrade,this.path=(e.path||"/engine.io").replace(/\/$/,"")+"/",this.forceJSONP=!!e.forceJSONP,this.jsonp=!1!==e.jsonp,this.forceBase64=!!e.forceBase64,this.enablesXDR=!!e.enablesXDR,this.timestampParam=e.timestampParam||"t",this.timestampRequests=e.timestampRequests,this.transports=e.transports||["polling","websocket"],this.readyState="",this.writeBuffer=[],this.prevBufferLen=0,this.policyPort=e.policyPort||843,this.rememberUpgrade=e.rememberUpgrade||!1,this.binaryType=null,this.onlyBinaryUpgrades=e.onlyBinaryUpgrades,this.perMessageDeflate=!1!==e.perMessageDeflate&&(e.perMessageDeflate||{}),!0===this.perMessageDeflate&&(this.perMessageDeflate={}),this.perMessageDeflate&&null==this.perMessageDeflate.threshold&&(this.perMessageDeflate.threshold=1024),this.pfx=e.pfx||null,this.key=e.key||null,this.passphrase=e.passphrase||null,this.cert=e.cert||null,this.ca=e.ca||null,this.ciphers=e.ciphers||null,this.rejectUnauthorized=void 0===e.rejectUnauthorized?null:e.rejectUnauthorized,this.forceNode=!!e.forceNode,this.id=null,this.upgrades=null,this.pingInterval=null,this.pingTimeout=null,this.pingIntervalTimer=null,this.pingTimeoutTimer=null,void this.open()):new o(t,e)}function i(t){var e={};for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n]);return e}var s=n(10),r=n(3),a=n(1)("engine.io-client:socket"),c=n(12),p=n(2),u=n(15),h=n(37),_=n(14);t.exports=o,o.priorWebsocketSuccess=!1,r(o.prototype),o.protocol=p.protocol,o.Socket=o,o.Transport=n(9),o.transports=n(10),o.parser=n(2),o.prototype.createTransport=function(t){a('creating transport "%s"',t);var e=i(this.query);e.EIO=p.protocol,e.transport=t,this.id&&(e.sid=this.id);var n=new s({agent:this.agent,hostname:this.hostname,port:this.port,secure:this.secure,path:this.path,query:e,forceJSONP:this.forceJSONP,jsonp:this.jsonp,forceBase64:this.forceBase64,enablesXDR:this.enablesXDR,timestampRequests:this.timestampRequests,timestampParam:this.timestampParam,policyPort:this.policyPort,socket:this,pfx:this.pfx,key:this.key,passphrase:this.passphrase,cert:this.cert,ca:this.ca,ciphers:this.ciphers,rejectUnauthorized:this.rejectUnauthorized,perMessageDeflate:this.perMessageDeflate,extraHeaders:this.extraHeaders,forceNode:this.forceNode,localAddress:this.localAddress});return n},o.prototype.open=function(){var t="websocket";this.readyState="opening";try{t=this.createTransport(t)}catch(t){return this.transports.shift(),void this.open()}t.open(),this.setTransport(t)},o.prototype.setTransport=function(t){a("setting transport %s",t.name);var e=this;this.transport&&(a("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=t,t.on("drain",function(){e.onDrain()}).on("packet",function(t){e.onPacket(t)}).on("error",function(t){e.onError(t)}).on("close",function(){e.onClose("transport close")})},o.prototype.probe=function(t){function e(){if(_.onlyBinaryUpgrades){var e=!this.supportsBinary&&_.transport.supportsBinary;h=h||e}h||(a('probe transport "%s" opened',t),u.send([{type:"ping",data:"probe"}]),u.once("packet",function(e){if(!h)if("pong"===e.type&&"probe"===e.data){if(a('probe transport "%s" pong',t),_.upgrading=!0,_.emit("upgrading",u),!u)return;o.priorWebsocketSuccess="websocket"===u.name,a('pausing current transport "%s"',_.transport.name),_.transport.pause(function(){h||"closed"!==_.readyState&&(a("changing transport and sending upgrade packet"),p(),_.setTransport(u),u.send([{type:"upgrade"}]),_.emit("upgrade",u),u=null,_.upgrading=!1,_.flush())})}else{a('probe transport "%s" failed',t);var n=new Error("probe error");n.transport=u.name,_.emit("upgradeError",n)}}))}function n(){h||(h=!0,p(),u.close(),u=null)}function i(e){var o=new Error("probe error: "+e);o.transport=u.name,n(),a('probe transport "%s" failed because of error: %s',t,e),_.emit("upgradeError",o)}function s(){i("transport closed")}function r(){i("socket closed")}function c(t){u&&t.name!==u.name&&(a('"%s" works - aborting "%s"',t.name,u.name),n())}function p(){u.removeListener("open",e),u.removeListener("error",i),u.removeListener("close",s),_.removeListener("close",r),_.removeListener("upgrading",c)}a('probing transport "%s"',t);var u=this.createTransport(t,{probe:1}),h=!1,_=this;o.priorWebsocketSuccess=!1,u.once("open",e),u.once("error",i),u.once("close",s),this.once("close",r),this.once("upgrading",c),u.open()},o.prototype.onOpen=function(){if(this.readyState="open",o.priorWebsocketSuccess="websocket"===this.transport.name,this.emit("open"),this.flush(),"open"===this.readyState&&this.upgrade&&this.transport.pause){a("starting upgrade probes");for(var t=0,e=this.upgrades.length;t<e;t++)this.probe(this.upgrades[t])}},o.prototype.onPacket=function(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(a('socket receive: type "%s", data "%s"',t.type,t.data),this.emit("packet",t),this.emit("heartbeat"),t.type){case"open":this.onHandshake(h(t.data));break;case"pong":this.setPing(),this.emit("pong");break;case"error":var e=new Error("server error");e.code=t.data,this.onError(e);break;case"message":this.emit("data",t.data),this.emit("message",t.data)}else a('packet received with socket readyState "%s"',this.readyState)},o.prototype.onHandshake=function(t){this.emit("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this.upgrades=this.filterUpgrades(t.upgrades),this.pingInterval=t.pingInterval,this.pingTimeout=t.pingTimeout,this.onOpen(),"closed"!==this.readyState&&(this.setPing(),this.removeListener("heartbeat",this.onHeartbeat),this.on("heartbeat",this.onHeartbeat))},o.prototype.onHeartbeat=function(t){clearTimeout(this.pingTimeoutTimer);var e=this;e.pingTimeoutTimer=setTimeout(function(){"closed"!==e.readyState&&e.onClose("ping timeout")},t||e.pingInterval+e.pingTimeout)},o.prototype.setPing=function(){var t=this;clearTimeout(t.pingIntervalTimer),t.pingIntervalTimer=setTimeout(function(){a("writing ping packet - expecting pong within %sms",t.pingTimeout),t.ping(),t.onHeartbeat(t.pingTimeout)},t.pingInterval)},o.prototype.ping=function(){var t=this;this.sendPacket("ping",function(){t.emit("ping")})},o.prototype.onDrain=function(){this.writeBuffer.splice(0,this.prevBufferLen),this.prevBufferLen=0,0===this.writeBuffer.length?this.emit("drain"):this.flush()},o.prototype.flush=function(){"closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length&&(a("flushing %d packets in socket",this.writeBuffer.length),this.transport.send(this.writeBuffer),this.prevBufferLen=this.writeBuffer.length,this.emit("flush"))},o.prototype.write=o.prototype.send=function(t,e,n){return this.sendPacket("message",t,e,n),this},o.prototype.sendPacket=function(t,e,n,o){if("function"==typeof e&&(o=e,e=void 0),"function"==typeof n&&(o=n,n=null),"closing"!==this.readyState&&"closed"!==this.readyState){n=n||{},n.compress=!1!==n.compress;var i={type:t,data:e,options:n};this.emit("packetCreate",i),this.writeBuffer.push(i),o&&this.once("flush",o),this.flush()}},o.prototype.close=function(){function t(){o.onClose("forced close"),a("socket closing - telling transport to close"),o.transport.close()}function e(){o.removeListener("upgrade",e),o.removeListener("upgradeError",e),t()}function n(){o.once("upgrade",e),o.once("upgradeError",e)}if("opening"===this.readyState||"open"===this.readyState){this.readyState="closing";var o=this;this.writeBuffer.length?this.once("drain",function(){this.upgrading?n():t()}):this.upgrading?n():t()}return this},o.prototype.onError=function(t){a("socket error %j",t),o.priorWebsocketSuccess=!1,this.emit("error",t),this.onClose("transport error",t)},o.prototype.onClose=function(t,e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){a('socket close with reason: "%s"',t);var n=this;clearTimeout(this.pingIntervalTimer),clearTimeout(this.pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),this.readyState="closed",this.id=null,this.emit("close",t,e),n.writeBuffer=[],n.prevBufferLen=0}},o.prototype.filterUpgrades=function(t){for(var e=[],n=0,o=t.length;n<o;n++)~c(this.transports,t[n])&&e.push(t[n]);return e}},function(t,e,n){!function(){function t(t){this.message=t}var n=e,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";t.prototype=new Error,t.prototype.name="InvalidCharacterError",n.btoa||(n.btoa=function(e){for(var n,i,s=String(e),r=0,a=o,c="";s.charAt(0|r)||(a="=",r%1);c+=a.charAt(63&n>>8-r%1*8)){if(i=s.charCodeAt(r+=.75),i>255)throw new t("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");n=n<<8|i}return c}),n.atob||(n.atob=function(e){var n=String(e).replace(/=+$/,"");if(n.length%4==1)throw new t("'atob' failed: The string to be decoded is not correctly encoded.");for(var i,s,r=0,a=0,c="";s=n.charAt(a++);~s&&(i=r%4?64*i+s:s,r++%4)?c+=String.fromCharCode(255&i>>(-2*r&6)):0)s=o.indexOf(s);return c})}()},function(t,e){function n(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}t.exports=n,n.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),n=Math.floor(e*this.jitter*t);t=0==(1&Math.floor(10*e))?t-n:t+n}return 0|Math.min(t,this.max)},n.prototype.reset=function(){this.attempts=0},n.prototype.setMin=function(t){this.ms=t},n.prototype.setMax=function(t){this.max=t},n.prototype.setJitter=function(t){this.jitter=t}},function(t,e){t.exports=function(t,e){var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},function(t,e,n){function o(){return e.colors[u++%e.colors.length]}function i(t){function n(){}function i(){var t=i,n=+new Date,s=n-(p||n);t.diff=s,t.prev=p,t.curr=n,p=n,null==t.useColors&&(t.useColors=e.useColors()),null==t.color&&t.useColors&&(t.color=o());for(var r=new Array(arguments.length),a=0;a<r.length;a++)r[a]=arguments[a];r[0]=e.coerce(r[0]),"string"!=typeof r[0]&&(r=["%o"].concat(r));var c=0;r[0]=r[0].replace(/%([a-z%])/g,function(n,o){if("%%"===n)return n;c++;var i=e.formatters[o];if("function"==typeof i){var s=r[c];n=i.call(t,s),r.splice(c,1),c--}return n}),r=e.formatArgs.apply(t,r);var u=i.log||e.log||console.log.bind(console);u.apply(t,r)}n.enabled=!1,i.enabled=!0;var s=e.enabled(t)?i:n;return s.namespace=t,s}function s(t){e.save(t);for(var n=(t||"").split(/[\s,]+/),o=n.length,i=0;i<o;i++)n[i]&&(t=n[i].replace(/[\\^$+?.()|[\]{}]/g,"\\$&").replace(/\*/g,".*?"),"-"===t[0]?e.skips.push(new RegExp("^"+t.substr(1)+"$")):e.names.push(new RegExp("^"+t+"$")))}function r(){e.enable("")}function a(t){var n,o;for(n=0,o=e.skips.length;n<o;n++)if(e.skips[n].test(t))return!1;for(n=0,o=e.names.length;n<o;n++)if(e.names[n].test(t))return!0;return!1}function c(t){return t instanceof Error?t.stack||t.message:t}e=t.exports=i.debug=i,e.coerce=c,e.disable=r,e.enable=s,e.enabled=a,e.humanize=n(36),e.names=[],e.skips=[],e.formatters={};var p,u=0},function(t,e,n){(function(e){function o(t){function n(t){if(!t)return!1;if(e.Buffer&&e.Buffer.isBuffer&&e.Buffer.isBuffer(t)||e.ArrayBuffer&&t instanceof ArrayBuffer||e.Blob&&t instanceof Blob||e.File&&t instanceof File)return!0;if(i(t)){for(var o=0;o<t.length;o++)if(n(t[o]))return!0}else if(t&&"object"==typeof t){t.toJSON&&"function"==typeof t.toJSON&&(t=t.toJSON());for(var s in t)if(Object.prototype.hasOwnProperty.call(t,s)&&n(t[s]))return!0}return!1}return n(t)}var i=n(13);t.exports=o}).call(e,function(){return this}())},function(t,e){function n(t){if(t=String(t),!(t.length>1e4)){var e=/^((?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|years?|yrs?|y)?$/i.exec(t);if(e){var n=parseFloat(e[1]),o=(e[2]||"ms").toLowerCase();switch(o){case"years":case"year":case"yrs":case"yr":case"y":return n*u;case"days":case"day":case"d":return n*p;case"hours":case"hour":case"hrs":case"hr":case"h":return n*c;case"minutes":case"minute":case"mins":case"min":case"m":return n*a;case"seconds":case"second":case"secs":case"sec":case"s":return n*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}}}function o(t){return t>=p?Math.round(t/p)+"d":t>=c?Math.round(t/c)+"h":t>=a?Math.round(t/a)+"m":t>=r?Math.round(t/r)+"s":t+"ms"}function i(t){return s(t,p,"day")||s(t,c,"hour")||s(t,a,"minute")||s(t,r,"second")||t+" ms"}function s(t,e,n){if(!(t<e))return t<1.5*e?Math.floor(t/e)+" "+n:Math.ceil(t/e)+" "+n+"s"}var r=1e3,a=60*r,c=60*a,p=24*c,u=365.25*p;t.exports=function(t,e){e=e||{};var s=typeof t;if("string"===s&&t.length>0)return n(t);if("number"===s&&isNaN(t)===!1)return e.long?i(t):o(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},function(t,e){var n=/^\s+/,o=/\s+$/;t.exports=function(t){return"string"==typeof t&&t?(t=t.replace(n,"").replace(o,""),JSON.parse(t)):null}},function(t,e){function n(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function i(t){if(u===setTimeout)return setTimeout(t,0);if((u===n||!u)&&setTimeout)return u=setTimeout,setTimeout(t,0);try{return u(t,0)}catch(e){try{return u.call(null,t,0)}catch(e){return u.call(this,t,0)}}}function s(t){if(h===clearTimeout)return clearTimeout(t);if((h===o||!h)&&clearTimeout)return h=clearTimeout,clearTimeout(t);try{return h(t)}catch(e){try{return h.call(null,t)}catch(e){return h.call(this,t)}}}function r(){l&&f&&(l=!1,f.length?d=f.concat(d):g=-1,d.length&&a())}function a(){if(!l){var t=i(r);l=!0;for(var e=d.length;e;){for(f=d,d=[];++g<e;)f&&f[g].run();g=-1,e=d.length}f=null,l=!1,s(t)}}function c(t,e){this.fun=t,this.array=e}function p(){}var u,h,_=t.exports={};!function(){try{u="function"==typeof setTimeout?setTimeout:n}catch(t){u=n}try{h="function"==typeof clearTimeout?clearTimeout:o}catch(t){h=o}}();var f,d=[],l=!1,g=-1;_.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];d.push(new c(t,e)),1!==d.length||l||i(a)},c.prototype.run=function(){this.fun.apply(null,this.array)},_.title="browser",_.browser=!0,_.env={},_.argv=[],_.version="",_.versions={},_.on=p,_.addListener=p,_.once=p,_.off=p,_.removeListener=p,_.removeAllListeners=p,_.emit=p,_.binding=function(t){throw new Error("process.binding is not supported")},_.cwd=function(){return"/"},_.chdir=function(t){throw new Error("process.chdir is not supported")},_.umask=function(){return 0}},function(t,e,n){(function(t){var o=n(13),i=n(16);e.deconstructPacket=function(t){function e(t){if(!t)return t;if(i(t)){var s={_placeholder:!0,num:n.length};return n.push(t),s}if(o(t)){for(var r=new Array(t.length),a=0;a<t.length;a++)r[a]=e(t[a]);return r}if("object"==typeof t&&!(t instanceof Date)){var r={};for(var c in t)r[c]=e(t[c]);return r}return t}var n=[],s=t.data,r=t;return r.data=e(s),r.attachments=n.length,{packet:r,buffers:n}},e.reconstructPacket=function(t,e){function n(t){if(t&&t._placeholder){var i=e[t.num];return i}if(o(t)){for(var s=0;s<t.length;s++)t[s]=n(t[s]);return t}if(t&&"object"==typeof t){for(var r in t)t[r]=n(t[r]);return t}return t}return t.data=n(t.data),t.attachments=void 0,t},e.removeBlobs=function(e,n){function s(e,c,p){if(!e)return e;if(t.Blob&&e instanceof Blob||t.File&&e instanceof File){r++;var u=new FileReader;u.onload=function(){p?p[c]=this.result:a=this.result,--r||n(a)},u.readAsArrayBuffer(e)}else if(o(e))for(var h=0;h<e.length;h++)s(e[h],h,e);else if(e&&"object"==typeof e&&!i(e))for(var _ in e)s(e[_],_,e)}var r=0,a=e;s(a),r||n(a)}}).call(e,function(){return this}())},function(t,e){function n(t){if(t)return o(t)}function o(t){for(var e in n.prototype)t[e]=n.prototype[e];return t}t.exports=n,n.prototype.on=n.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks[t]=this._callbacks[t]||[]).push(e),this},n.prototype.once=function(t,e){function n(){o.off(t,n),e.apply(this,arguments)}var o=this;return this._callbacks=this._callbacks||{},n.fn=e,this.on(t,n),this},n.prototype.off=n.prototype.removeListener=n.prototype.removeAllListeners=n.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n=this._callbacks[t];if(!n)return this;if(1==arguments.length)return delete this._callbacks[t],this;for(var o,i=0;i<n.length;i++)if(o=n[i],o===e||o.fn===e){n.splice(i,1);break}return this},n.prototype.emit=function(t){this._callbacks=this._callbacks||{};var e=[].slice.call(arguments,1),n=this._callbacks[t];if(n){n=n.slice(0);for(var o=0,i=n.length;o<i;++o)n[o].apply(this,e)}return this},n.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks[t]||[]},n.prototype.hasListeners=function(t){return!!this.listeners(t).length}},function(t,e){function n(t,e){var n=[];e=e||0;for(var o=e||0;o<t.length;o++)n[o-e]=t[o];return n}t.exports=n},function(t,e){"use strict";function n(t){var e="";do e=r[t%a]+e,t=Math.floor(t/a);while(t>0);return e}function o(t){var e=0;for(u=0;u<t.length;u++)e=e*a+c[t.charAt(u)];return e}function i(){var t=n(+new Date);return t!==s?(p=0,s=t):t+"."+n(p++)}for(var s,r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_".split(""),a=64,c={},p=0,u=0;u<a;u++)c[r[u]]=u;i.encode=n,i.decode=o,t.exports=i}])});