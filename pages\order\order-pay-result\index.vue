<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">订单支付结果</block>
		</cu-custom>
		<view class="cu-card">
			<view class="cu-item">
				<view class="solid-bottom text-xxl padding text-center" v-if="orderInfo.isPay == '1'">
					<text class="cuIcon-roundcheckfill text-green"> 支付成功</text>
				</view>
				<view class="margin-left-sm margin-top flex">
					<text class="margin-left flex-sub text-sm">配送方式</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.deliveryWay == '1' ? '普通快递' : orderInfo.deliveryWay == '2' ? '上门自提' : ''}}</view>
				</view>
				<view class="margin-left-sm margin-top flex">
					<text class="margin-left flex-sub text-sm">订单编号</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.orderNo}}</view>
				</view>
				<view class="margin-left-sm margin-top flex">
					<text class="margin-left flex-sub text-sm">创建时间</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.createTime}}</view>
				</view>
				<view class="margin-left-sm margin-top flex" v-if="orderInfo.paymentTime">
					<text class="margin-left flex-sub text-sm">付款时间</text>
					<view class="flex-twice text-sm text-gray">{{orderInfo.paymentTime}}</view>
				</view>
				<view class="padding flex flex-direction">
				  <navigator class="cu-btn lg" :class="'bg-'+theme.themeColor"
				  :url="'/pages/order/order-detail/index?id=' + orderInfo.id">查看订单</navigator>
				  <navigator class="cu-btn margin-tb-sm lg" :class="'line-'+theme.themeColor" open-type="switchTab"
				  url="/pages/home/<USER>">返回首页</navigator>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const app = getApp();
	import api from 'utils/api'
	import util from 'utils/util'

	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				orderInfo: {},
				id: ''
			};
		},

		components: {
			
		},
		props: {},

		onShow() {
			
		},

		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			this.id = options.id
			app.initPage().then(res => {
				this.orderGet(this.id);
			});
		},

		methods: {
			orderGet(id) {
				let that = this;
				api.orderGet(id).then(res => {
					let orderInfo = res.data
					this.orderInfo = orderInfo
				});
			},
		}
	};
</script>
<style>
	
</style>
