<template>
	<view>
		<!-- 自定义导航组件 -->
		<uni-nav-bar shadow="true" :titleSize="'99'" :backgroundColor="newData.backgroundColor" :color="newData.color" 
			:fixed="newData.fixed" :shadow="true" @clickRight="navClickLeft" >
			<view slot="left"></view>
			<view style=" width: 100%;">
				<view class="flex justify-center" :style="{fontSize:`${newData.fontSize}px`}">
					<p>{{newData.title}}</p>
				</view>
			</view>
			<view slot="right" ><view :style="{fontSize:`${newData.fontSize}px`}" :class="newData.titleIcon"></view></view>
		</uni-nav-bar>
		<uni-drawer ref="showRight" :width="newData.barWidth" :mode="newData.barMode" :mask-click="true">
			<scroll-view style="height: 100%;" scroll-y="true" :style="{backgroundColor:`${newData.barGroundColor}`}">
				
				<view v-for="(item,index) in newData.menuList" :key="index">
					<view v-if="index==0" :style="{height:`${newData.topHeight}px`}"></view>
					<div-base-navigator 
					style="width: 100%;display: flex; border-top: 1px solid;align-items: center;justify-content: center;"
					:style="{borderBottomColor:`${newData.barFontColor}px`,height:`${newData.barItemHeight}px`,backgroundColor:`${newData.barGroundColor}`,padding: `${newData.barItemPadding}px`,fontSize:`${newData.barFontSize}px`,color:`${newData.barFontColor}`}" :pageUrl="item.pageUrl"
						:isSystemUrl="item.isSystemUrl">
						<p >{{item.title}}</p>	
					
					</div-base-navigator>
				</view>
			</scroll-view>
		</uni-drawer>
	</view>
</template>


<script>
	const app = getApp();
	import divBaseNavigator from '@/components/div-components/div-base/div-base-navigator.vue'
	export default {
		components: {
			divBaseNavigator
		},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			}
		},
		created() {},
		mounted() {

		},
		data() {
			return {
				newData: this.value,
			};
		},
		methods: {
			navClickLeft() {
				this.showDrawer();
			},
			showDrawer() {
				this.$refs.showRight.open();
			},
			closeDrawer() {
				this.$refs.showRight.close();
			},
			goPage() {
				console.log("点击跳转", this.isSystemUrl)
				if (!this.pageUrl) {
					return;
				}
				if (this.iSystemUrl) {
					if (this.pageUrls.tabPages.indexOf(this.pageUrl) != -1) {
						uni.switchTab({
							url: this.pageUrl
						});
					} else {
						uni.navigateTo({
							url: this.pageUrl
						}).then(()=>{
							location.reload() 
						});
					}
				} else {
					window.location.href = this.pageUrl;
				}
			}

		}
	}
</script>

<style scoped lang="scss">

</style>
