<template>
	<view class="home_page" :style="pageStyle" v-if="pageShowFlag">
		<view>
			<!-- 自定义页面组件 -->
			<block v-for="(temp, index) in pageDivData.pageComponent.componentsList" :key="index">
				<!-- 弹窗组件 -->
				<template v-if="temp.componentName === 'popupComponent'">
					<view>
						<div-popup v-model="temp.data"></div-popup>
					</view>
				</template>
				<!-- 首页导航 -->
				<template v-else-if="temp.componentName === 'navTitleComponent'">
					<view>
						<nav-title v-model="temp.data"></nav-title>
					</view>
				</template>
				<!-- 单列图片 -->
				<template v-else-if="temp.componentName === 'imageComponent'">
					<view>
						<div-image v-model="temp.data"></div-image>
					</view>
				</template>
				<!-- 双列图片 -->
				<template v-else-if="temp.componentName === 'doubleRowImageComponent'">
					<view>
						<double-row-image v-model="temp.data"></double-row-image>
					</view>
				</template>
				<!-- 富文本 -->
				<template v-else-if="temp.componentName === 'richTextComponent'">
					<view>
						<base-rich-text v-model="temp.data"></base-rich-text>
					</view>
				</template>
				<!-- 轮播图 -->
				<template v-else-if="temp.componentName === 'swiperComponent'">
					<view>
						<div-swiper v-model="temp.data"></div-swiper>
					</view>
				</template>
				<!-- 视频 -->
				<template v-else-if="temp.componentName === 'videoComponent'">
					<view>
						<div-video v-model="temp.data"></div-video>
					</view>
				</template>
				<!-- 消息通知 -->
				<template v-else-if="temp.componentName === 'noticeComponent'">
					<view>
						<div-notice v-model="temp.data"></div-notice>
					</view>
				</template>
				<!-- 标题文字 -->
				<template v-else-if="temp.componentName === 'titleTextComponent'">
					<view>
						<div-title-text v-model="temp.data"></div-title-text>
					</view>
				</template>
				<!-- 导航按钮 -->
				<template v-else-if="temp.componentName === 'navButtonComponent'">
					<view>
						<div-nav-button v-model="temp.data"></div-nav-button>
					</view>
				</template>
				<!-- 悬浮按钮 -->
				<template v-else-if="temp.componentName === 'suspendBtnComponent'">
					<view>
						<suspend-btn v-model="temp.data"></suspend-btn>
					</view>
				</template>
				<!-- 商品分类   注意名字-->
				<template v-else-if="temp.componentName === 'categoryComponent'">
					<view>
						<goods-category v-model="temp.data" :scrollTop="scrollTop"></goods-category>
					</view>
				</template>
				<!-- 分割线 -->
				<template v-else-if="temp.componentName === 'cuttingLineComponent'">
					<view>
						<cutting-line v-model="temp.data"></cutting-line>
					</view>
				</template>
				<!-- 背景音乐 -->
				<template v-else-if="temp.componentName === 'musicComComponent'">
					<view>
						<music-com v-model="temp.data"></music-com>
					</view>
				</template>
				<!-- 底部导航 -->
				<template v-else-if="temp.componentName === 'tabBarComponent'">
					<view>
						<cus-tab-bar @bottomHeightShow="bottomHeightShow" v-model="temp.data"></cus-tab-bar>
					</view>
				</template>
				<!-- 多功能按钮 -->
				<template v-else-if="temp.componentName === 'functionButtonComponent'">
					<view v-if="showFunctionButtonComponent(temp.data)">
						<function-button  @bottomHeightShow="bottomHeightShow" :order="buttonOrder" v-model="temp.data"></function-button>
					</view>
				</template>
				<!-- 地图 -->
				<template v-else-if="temp.componentName === 'mapComponent'">
					<view>
						<map-com v-model="temp.data"></map-com>
					</view>
				</template>
				<!-- 热点图片 -->
				<template v-else-if="temp.componentName === 'areaImageComponent'">
					<view>
						<area-image v-model="temp.data"></area-image>
					</view>
				</template>
				<!-- 待考虑组件
				<template v-else-if="temp.componentName === 'goodsComponent'">
					<view>
						<div-goods v-model="temp.data"></div-goods>
					</view>
				</template>
				<template v-else-if="temp.componentName === 'goodsRowComponent'">
					<view>
						<div-goods-row v-model="temp.data"></div-goods-row>
					</view>
				</template>
				 -->
			</block>

			<view :class="' bg-gray ' + (loadmore3?'loading':'over')"></view>
		</view>
		<!-- 用户隐私政策授权弹框,小程序端首页不弹出 -->
		<!-- #ifdef APP-PLUS -->
		<privacy-policy v-if="showPrivacyPolicy"></privacy-policy>
		<!-- #endif -->
		
<!-- 		<navigator class="cu-btn round line-green margin-right" open-type="navigate"
			:url="'/pages/customer-service/wx-customer-service-list/index?'" >
			<view class="cuIcon-servicefill">客服</view>
		</navigator> -->
		<bottomRemind :pageDivData="pageDivData"></bottomRemind>
		<view v-if="cusTabBarShowFlag" :style="{height: `${bottomHeight}px`}"></view>
	</view>
</template>

<script>
	
	// #ifdef APP-PLUS
	import privacyPolicy from '@/components/privacy-policy/index';
	// #endif
	const app = getApp();
	const util = require("utils/util.js");
	import jweixin from '@/utils/jweixin.js'
	import api from '@/utils/api'
	import goodsCard from "components/goods-card/index";
	import goodsRow from "components/goods-row/index";
	import __config from "@/config/env";
	import divGoods from "@/components/div-components/div-goods/div-goods.vue";
	import divGoodsRow from "@/components/div-components/div-goods-row/div-goods-row.vue";



	import divNavButton from "@/components/div-components/div-nav-button/div-nav-button.vue";
	import divNotice from "@/components/div-components/div-notice/div-notice.vue";
	import divTitleText from "@/components/div-components/div-title-text/div-title-text.vue";
	import divSwiper from "@/components/div-components/div-swiper/div-swiper.vue";
	import goodsCategory from "@/components/base/category.vue";
	import navTitle from "@/components/base/navTitle.vue";
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import goodsCover from '@/components/goods-cover/goods-cover.vue'
	import suspendBtn from '@/components/base/suspendBtn.vue'
	import divImage from "@/components/div-components/div-image/div-image.vue";
	import doubleRowImage from "@/components/double-row-image/index.vue";
	import baseRichText from "@/components/rich-text/index.vue";
	import cuttingLine from "@/components/base/cuttingLine.vue";
	import musicCom from "@/components/base/musicCom.vue";
	import cusTabBar from "@/components/base/cusTabBar.vue";
	import functionButton from "@/components/base/function-button/functionButton.vue";
	import mapCom from "@/components/base/mapCom.vue";
	import areaImage from "@/components/base/areaImage.vue";
	import divVideo from "@/components/div-components/div-video/div-video.vue";
	import divPopup from "@/components/div-components/div-popup/div-popup.vue";

	import shareFriends from '@/components/share-friends/index.vue'
	
	import bottomRemind from "@/components/bottom-remind/index.vue";
	
	export default {
		components: {
			// #ifdef APP-PLUS
			privacyPolicy,
			// #endif
			goodsCard,
			goodsRow,
			divImage,
			divSwiper,
			divNavButton,
			divNotice,
			divTitleText,
			divGoods,
			divGoodsRow,
			uniNavBar,
			goodsCover,
			navTitle, //导航栏
			goodsCategory, //分类
			suspendBtn, //悬浮图标
			doubleRowImage, //双列图片
			baseRichText, //富文本
			cuttingLine, //分割线
			musicCom, //背景音乐
			cusTabBar, //底部导航
			functionButton, //多功能按钮
			mapCom, //地图
			areaImage, //热点图片
			divVideo, //视频组件
			divPopup, //弹窗组件
			bottomRemind, //底部提示
		},
		data() {
			return {
				theme: app.globalData.theme, //全局颜色变量
				showPrivacyPolicy: __config.showPrivacyPolicy,
				loadmore: true,
				pageDivData: {
					pageComponent: {
						componentsList: []
					}
				}, //首页自定义配置组件的数据
				loadmore3: true, // 自定义页面的加载状态
				bottomHeight: '', // 底部菜单高度
				cusTabBarShowFlag: false, // 底部高度开关
				buttonOrder: null, // 按钮订单
				pageStyle: {}, // 按钮订单
				pageShowFlag: false, // 页面展示开关
				scrollTop:0,//滚动高度
			};
		},
		props: {},
		mounted() {
		},
		onLoad(options) {
			// 保存别人分享来的 userCode
			// util.saveSharerUserCode(options);
			
			// #ifdef MP-WEIXIN
			// 小程序端保存参数
			if (options) {
				this.$mp = this.$mp || {};
				this.$mp.query = options;
			}
			// #endif
			
			// api.jokelLoginWxMp({}).then(res => {
			// 	let userInfo = res.data;
			// 	//同步执行
			// 	uni.setStorageSync('third_session', userInfo.thirdSession);
			// 	uni.setStorageSync('user_info', userInfo);
			
			// 	console.log("joke----公众号登录成功 ", res)
			// }).catch(res => {
			// 	console.log("joke----公众号登录失败 ", res)
			// });
			app.initPage().then(res => {
				this.loadData();
			});	
		},
		onPageScroll(e){
			this.scrollTop = e.scrollTop;
		},
		onShow() {
			//更新购物车tabar角标数量
			// uni.setTabBarBadge({
			// 	index: 3,
			// 	text: app.globalData.shoppingCartCount + ''
			// });
		},
		onShareAppMessage: function() {
			// #ifdef MP-WEIXIN
			let title = this.pageDivData.pageBase.shareTitle || '微笑商城';
			let path = 'pages/home/<USER>';
			const userInfo = uni.getStorageSync('user_info');
			const userCode = userInfo ? '?sharer_user_code=' + userInfo.userCode : '';
			path = path + userCode;
			
			// 如果有页面ID，添加到分享路径
			if (this.pageDivData && this.pageDivData.id) {
				path += (userCode ? '&' : '?') + 'page_id=' + this.pageDivData.id;
			}
			
			return {
				title: title,
				path: path,
				imageUrl: this.pageDivData.pageBase.shareImgUrl || '',
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log(res.errMsg);
					}
				},
				fail: function(res) { // 转发失败
				}
			};
			// #endif
		},

		// 分享到朋友圈
		onShareTimeline: function() {
			// #ifdef MP-WEIXIN
			let title = this.pageDivData.pageBase.shareTitle || '微笑商城';
			const userInfo = uni.getStorageSync('user_info');
			const query = userInfo ? 'sharer_user_code=' + userInfo.userCode : '';
			
			// 如果有页面ID，添加到分享路径
			let pageIdQuery = '';
			if (this.pageDivData && this.pageDivData.id) {
				pageIdQuery = 'page_id=' + this.pageDivData.id;
			}
			
			const finalQuery = query ? (pageIdQuery ? query + '&' + pageIdQuery : query) : pageIdQuery;
			
			return {
				title: title,
				query: finalQuery,
				imageUrl: this.pageDivData.pageBase.shareImgUrl || ''
			};
			// #endif
		},

		onPullDownRefresh() {
			// 显示顶部刷新图标
			uni.showNavigationBarLoading();
			this.refresh(); // 隐藏导航栏加载框
			uni.hideNavigationBarLoading(); // 停止下拉动作
			uni.stopPullDownRefresh();
		},
		onReachBottom() {
			// console.log("首页到底")
		},
		methods: {
			loadData() {
				let tenantId = '';
				let pageId = '';
				let params = {};
				
				// #ifdef H5
				tenantId = util.getUrlParam(location.href, "tenant_id");
				pageId = util.getUrlParam(location.href, "page_id");
				params = {
					id: pageId,
					appId: util.getUrlParam(location.href, "app_id"),
					tenantid: tenantId,
					shareFriendId: util.getUrlParam(location.href, "sharer_friend_id"),
				}
				// #endif
				
				// #ifdef MP
				const query = this.$mp.query || {};
				console.log(this.$mp.query);
				
				tenantId = query.tenant_id || '';
				pageId = query.page_id || '';
				params = {
					id: pageId,
					appId: query.app_id || '',
					tenantid: tenantId,
					shareFriendId: query.sharer_friend_id || '',
				}
				// #endif
				
				this.pageDivData = {
					pageComponent: {
						componentsList: []
					}
				}
        console.log("API请求参数:", params,uni.getStorageSync('third_session'));
        if(!uni.getStorageSync('third_session')){
          api.pageDeviseFreedom(params).then(res => {
            // console.log("组件", res)
            let pageDivData = res.data;
            if (pageDivData) {
              this.pageDivData = pageDivData;
              this.pageStyle={
                backgroundColor:this.pageDivData.pageBase.backgroundColor?this.pageDivData.pageBase.backgroundColor:'',
                backgroundImage:  "url("+this.pageDivData.pageBase.background+")",
              }
              uni.setNavigationBarTitle({
                title: this.pageDivData.pageBase.pageTitle
              });
              // this.getButtonOrderMsg();

              this.pageShowFlag = true;
              //判断用户页面限制
              // this.checkJump()
              //初始化微信分享
              this.initShareWx();
              this.loadmore3 = false;
            } else {
              // 如果没有设置自定义页面数据，那就显示默认原始页面
              // this.getDefaultPageData();
            }
          }).catch(err => {
            console.error(err)
          });
          return
        }
				// console.log("pagedevise请求参数",params)
				api.getButtonOrder(pageId).then(res => {
					// console.log("查询按钮订单",res)
					this.buttonOrder = res.data;
				
					api.pagedevise(params).then(res => {
						// console.log("组件", res)
						let pageDivData = res.data;
						if (pageDivData) {
							this.pageDivData = pageDivData;
							this.pageStyle={
								backgroundColor:this.pageDivData.pageBase.backgroundColor?this.pageDivData.pageBase.backgroundColor:'',
								backgroundImage:  "url("+this.pageDivData.pageBase.background+")",
							}
							uni.setNavigationBarTitle({
								title: this.pageDivData.pageBase.pageTitle
							});
							// this.getButtonOrderMsg();
							//判断用户页面限制
							this.checkJump()
							//初始化微信分享
							this.initShareWx();	
							this.loadmore3 = false;
						} else {
							// 如果没有设置自定义页面数据，那就显示默认原始页面
							// this.getDefaultPageData();
						}
					}).catch(err => {
						console.log(err)
					});
				});
			},
			refresh() {
				this.loadmore = true;
				this.loadData();
			},
			//检查用户此页面限制
			checkJump(){
				if (this.pageDivData.forbidFlag) {
					if (!this.pageDivData.pageBase.forbidPageUrl || this.pageDivData.pageBase.forbidPageUrl.indexOf('/') <
						0) {
						return;
					}
					if (this.pageDivData.pageBase.forbidIsSystemUrl) {
						return uni.navigateTo({
							url: this.pageDivData.pageBase.forbidPageUrl
						});
					} else {
						// #ifdef H5
						window.location.href = this.pageDivData.pageBase.forbidPageUrl
						// #endif
						// #ifdef MP
						uni.navigateTo({
							url: this.pageDivData.pageBase.forbidPageUrl
						});
						// #endif
					}
				}
				if (this.pageDivData.permitFlag) {
					this.pageShowFlag = true;
				}else{
					if (!this.pageDivData.pageBase.permitPageUrl || this.pageDivData.pageBase.permitPageUrl.indexOf('/') <0) {
						return;
					}
					if (this.pageDivData.pageBase.permitIsSystemUrl) {
						return uni.navigateTo({
							url: this.pageDivData.pageBase.permitPageUrl
						});
					} else {
						// #ifdef H5
						window.location.href = this.pageDivData.pageBase.permitPageUrl
						// #endif
						// #ifdef MP
						uni.navigateTo({
							url: this.pageDivData.pageBase.permitPageUrl
						});
						// #endif
					}
				}
			
			},
			jumpPage(page) {
				console.log("页面跳转", page)
				if(!page){
					return
				}
				if (!page.pageUrl || page.pageUrl.indexOf('/') < 0) {
					return;
				}
				if (page.isSystemUrl) {
					uni.navigateTo({
						url: page.pageUrl
					});
				} else {
					// #ifdef H5
					window.location.href = page.pageUrl;
					// #endif
					// #ifdef MP
					uni.navigateTo({
						url: page.pageUrl
					});
					// #endif
				}
			},
			userInfoUpdateByMp(parm) {
				let that = this;
				console.log("去授权了")
				api.userInfoUpdateByMp(parm).then(res => {
					//公众号h5网页授权时url会产生code、state参数，防止code、state被复制，需自动剔除
					// #ifdef H5
					let query = that.$Route.query;
					delete query.code;
					delete query.state;
					util.resetPageUrl(query);
					// #endif
					
					console.log("授权更新用户信息",res)
					if(res && res.data){
						let userInfo =  uni.getStorageSync('user_info');
						userInfo.headimgUrl = res.data.headimgUrl;
						userInfo.nickName = res.data.nickName;
						uni.setStorageSync('user_info', userInfo);
						this.userInfoGet();
					}
				}).catch(res => {

				});
			},
			//判断底部导航栏出现并腾出位置
			bottomHeightShow(obj) {
				// console.log("开了",obj)
				if(obj){
					this.bottomHeight =obj
					this.cusTabBarShowFlag =true
				}
			},
			//拿取当前页订单信息 用于多功能按钮的显示
			getButtonOrderMsg(){
				//判断有无 支付类型的多功能按钮
				for (let i = 0; i < this.pageDivData.pageComponent.componentsList.length; i++) {
					let obj = this.pageDivData.pageComponent.componentsList[i];
					if(obj.componentName == "functionButtonComponent" ){ //有无多功能菜单
						console.log("有多功能按钮",obj)
						// for (let j = 0; j < obj.data.buttonList.length; j++) {
						// 	if(obj.data.buttonList[j].button.type == 2 ){  //有无支付按钮
								let pageId =this.pageDivData.id;
								api.getButtonOrder(pageId).then(res => {
									console.log("查询按钮订单",res)
									this.buttonOrder = res.data;
								});
								break;
						// 	}
						// }
					}
				}
			},
			//判断是否显示多功能按钮
			showFunctionButtonComponent(obj){
				console.log("对象",obj,this.buttonOrder)
				if(obj.showRule == 1){
					return true
				}if(obj.showRule == 2 ){
					if(!this.buttonOrder){ //没有订单时显示
						return true
					}else{//有订单且未支付显示 
						if(this.buttonOrder.isPay == "1"){
							return false;
						}else{
							return true
						}
					}
				}
				if(obj.showRule == 3 &&  this.buttonOrder && this.buttonOrder.isPay == "1"){ //支付后 有订单且 确认支付了
					return true
				}
				return false ;
			},
			//初始化分享
			initShareWx() {
				// h5页面加载时 会默认初始化分享
				// #ifdef H5
				if (util.isWeiXinBrowser()) {
					let shareObj = {
						title: this.pageDivData.pageBase.shareTitle?this.pageDivData.pageBase.shareTitle:'',
						desc: this.pageDivData.pageBase.describe?this.pageDivData.pageBase.describe:'',
						imgUrl: this.pageDivData.pageBase.shareImgUrl?this.pageDivData.pageBase.shareImgUrl:''
					};
					// console.log("来分享的参数",shareObj)
					let url = util.setH5ShareUrl();
					// //重新设置新的url
					let query = this.$Route.query;
					delete query.code;
					delete query.state;
					query.sharer_friend_id = util.getUrlParam(url, "sharer_friend_id");
					util.resetPageUrl(query);
					// console.log("重选设置url",query)
					api.getJsSdkConfig({
						url: location.href
					}).then(res => {
						// #ifdef H5
						// history.replaceState(history.state, null, url);
						// #endif
						let wxConfig = res.data;
						let shareObjTemp = {
							title: shareObj.title?shareObj.title:'',
							desc: shareObj.desc?shareObj.desc:'',
							link: wxConfig.url,
							imgUrl: shareObj.imgUrl?shareObj.imgUrl:'',
						};
						jweixin.shareWxFriend(wxConfig, shareObjTemp, function() {
							// that.showModal = true;
							// uni.hideLoading();
							// console.log("初始化微信分享成功", shareObjTemp)
						}, function(e) {
							// console.log("初始化微信分享成功error",e)
						}, );
					}).catch(res => {
						console.log('调用getJsSdkConfig失败：' + res)
					});
				}
				// #endif
				
				// #ifdef MP-WEIXIN
				// 小程序分享设置
				uni.showShareMenu({
					withShareTicket: true,
					menus: ['shareAppMessage', 'shareTimeline']
				});
				// #endif
			},
		}
	};
</script>
<style>
	@import "./index.css";
</style>
