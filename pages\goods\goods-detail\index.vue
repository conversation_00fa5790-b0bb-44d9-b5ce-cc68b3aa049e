<template>
	<view  class="index_page" :style="pageStyle" v-if="pageShowFlag">
		<!-- 		<cu-custom  :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">商品详情</block>
		</cu-custom> -->
		<block v-for="(temp, index) in pageDivData.pageComponent.componentsList" :key="index">
			<!-- 弹窗组件 -->
			<template v-if="temp.componentName === 'popupComponent'">
				<view>
					<div-popup v-model="temp.data"></div-popup>
				</view>
			</template>
			<!-- 首页导航 -->
			<template v-else-if="temp.componentName === 'navTitleComponent'">
				<view>
					<nav-title v-model="temp.data"></nav-title>
				</view>
			</template>
			<!-- 单列图片 -->
			<template v-else-if="temp.componentName === 'imageComponent'">
				<view>
					<div-image v-model="temp.data"></div-image>
				</view>
			</template>
			<!-- 双列图片 -->
			<template v-else-if="temp.componentName === 'doubleRowImageComponent'">
				<view>
					<double-row-image v-model="temp.data"></double-row-image>
				</view>
			</template>
			<!-- 富文本 -->
			<template v-else-if="temp.componentName === 'richTextComponent'">
				<view>
					<base-rich-text v-model="temp.data"></base-rich-text>
				</view>
			</template>
			<!-- 轮播图 -->
			<template v-else-if="temp.componentName === 'swiperComponent'">
				<view>
					<div-swiper v-model="temp.data"></div-swiper>
				</view>
			</template>
			<!-- 消息通知 -->
			<template v-else-if="temp.componentName === 'noticeComponent'">
				<view>
					<div-notice v-model="temp.data"></div-notice>
				</view>
			</template>
			<!-- 标题文字 -->
			<template v-else-if="temp.componentName === 'titleTextComponent'">
				<view>
					<div-title-text v-model="temp.data"></div-title-text>
				</view>
			</template>
			<!-- 导航按钮 -->
			<template v-else-if="temp.componentName === 'navButtonComponent'">
				<view>
					<div-nav-button v-model="temp.data"></div-nav-button>
				</view>
			</template>
			<!-- 悬浮按钮 -->
			<template v-else-if="temp.componentName === 'suspendBtnComponent'">
				<view>
					<suspend-btn v-model="temp.data"></suspend-btn>
				</view>
			</template>
			<!-- 商品分类封面   注意名字-->
			<template v-else-if="temp.componentName === 'categoryComponent'">
				<view>
					<goods-category v-model="temp.data"></goods-category>
				</view>
			</template>
			<!-- 分割线 -->
			<template v-else-if="temp.componentName === 'cuttingLineComponent'">
				<view>
					<cutting-line v-model="temp.data"></cutting-line>
				</view>
			</template>
			<!-- 底部导航 -->
			<template v-else-if="temp.componentName === 'tabBarComponent'">
				<view>
					<cus-tab-bar @bottomHeightShow="bottomHeightShow" v-model="temp.data"></cus-tab-bar>
				</view>
			</template>
			<!-- 多功能按钮 -->
			<template v-else-if="temp.componentName === 'functionButtonComponent'">
				<view v-if="showFunctionButtonComponent(temp.data)">
					<function-button @bottomHeightShow="bottomHeightShow" :order="buttonOrder" v-model="temp.data">
					</function-button>
				</view>
			</template>
			<!-- 地图 -->
			<template v-else-if="temp.componentName === 'mapComponent'">
				<view>
					<map-com v-model="temp.data"></map-com>
				</view>
			</template>
			<!-- 热点图片 -->
			<template v-else-if="temp.componentName === 'areaImageComponent'">
				<view>
					<area-image v-model="temp.data"></area-image>
				</view>
			</template>
			<!-- 视频 -->
			<template v-else-if="temp.componentName === 'videoComponent'">
				<view>
					<div-video v-model="temp.data"></div-video>
				</view>
			</template>


			<!-- 商品封面没有分类 -->
			<template v-else-if="temp.componentName === 'goodsCoverComponent'">
				<view>
					<goods-cover v-model="temp.data"></goods-cover>
				</view>
			</template>
			<!-- 商品推荐 -->
			<template v-else-if="temp.componentName === 'goodsComponent'">
				<view>
					<div-goods v-model="temp.data"></div-goods>
				</view>
			</template>
			<!-- 商品甄选 -->
			<template v-else-if="temp.componentName === 'goodsRowComponent'">
				<view>
					<div-goods-row v-model="temp.data"></div-goods-row>
				</view>
			</template>
			<!-- 商品内容 -->
			<template v-else-if="temp.componentName === 'goodsContentComponent'">
				<view>
					<goods-content v-model="temp.data" :goods="goodsSpu"></goods-content>
				</view>
			</template>
		</block>

		<bottomRemind :pageDivData="pageDivData"></bottomRemind>
		<!-- 		<view class="cu-load bg-gray to-down">已经到底啦...</view> -->
		<view v-if="cusTabBarShowFlag" :style="{height: `${bottomHeight}px`}"></view>
	</view>
</template>

<script>
	const app = getApp();
	import api from 'utils/api'
	import util from '@/utils/util'
	//公共组件
	import divNavButton from "@/components/div-components/div-nav-button/div-nav-button.vue";
	import divNotice from "@/components/div-components/div-notice/div-notice.vue";
	import divTitleText from "@/components/div-components/div-title-text/div-title-text.vue";
	import divSwiper from "@/components/div-components/div-swiper/div-swiper.vue";
	import goodsCategory from "@/components/base/category.vue";
	import navTitle from "@/components/base/navTitle.vue";
	import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue'
	import suspendBtn from '@/components/base/suspendBtn.vue'
	import divImage from "@/components/div-components/div-image/div-image.vue";
	import doubleRowImage from "@/components/double-row-image/index.vue";
	import baseRichText from "@/components/rich-text/index.vue";
	import cuttingLine from "@/components/base/cuttingLine.vue";
	import cusTabBar from "@/components/base/cusTabBar.vue";
	import functionButton from "@/components/base/function-button/functionButton.vue";
	import jweixin from '@/utils/jweixin'
	import mapCom from "@/components/base/mapCom.vue";
	import areaImage from "@/components/base/areaImage.vue";
	import divVideo from "@/components/div-components/div-video/div-video.vue";
	import divPopup from "@/components/div-components/div-popup/div-popup.vue";
	
	//私有组件
	import goodsCover from '@/components/goods-cover/goods-cover.vue'
	import divGoods from "@/components/div-components/div-goods/div-goods.vue";
	import divGoodsRow from "@/components/div-components/div-goods-row/div-goods-row.vue";
	import goodsContent from "@/components/goods-content/goods-content.vue";
	
	import bottomRemind from "@/components/bottom-remind/index.vue";
	export default {
		components: {
			divImage,
			divSwiper,
			divNavButton,
			divNotice,
			divTitleText,
			uniNavBar,
			navTitle, //导航栏
			goodsCategory, //分类
			suspendBtn, //悬浮图标
			doubleRowImage, //双列图片
			baseRichText, //富文本
			cuttingLine, //分割线
			cusTabBar, //底部导航
			cusTabBar, //底部导航
			functionButton, //底部导航
			mapCom, //地图
			areaImage, //热点图片
			divVideo, //视频组件
			divPopup, //弹窗组件
			goodsCover, //商品封面  每天分类	
			divGoods, //商品推荐
			divGoodsRow, //商品甄选
			goodsContent, //商品内容
			bottomRemind, //底部提示
		},
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				shopInfo: {},
				cartNum: 1,
				goodsSpu: { //当前页面商品
					picUrls: []
				},
				pageDivData: {
					pageComponent: {
						componentsList: []
					}
				}, //自定义配置组件的数据
				goodsSku: {},
				goodsSpecData: [],
				goodsAppraises: {},
				currents: 1,
				modalSku: false,
				modalSkuType: '',
				shoppingCartCount: 0,
				shareShow: '',
				curLocalUrl: '',
				ensureList: [],
				modalService: '',
				modalCoupon: false,
				couponInfoList: [],
				id: "",
				posterUrl: "",
				posterShow: false,
				posterConfig: "",
				article_description: "",
				showShare: false,
				shareParams: {},
				buttonOrder: {},
				bottomHeight: '', // 底部菜单高度
				cusTabBarShowFlag: false, // 底部菜单高度
				pageStyle: {}, // 底部菜单高度
				pageShowFlag: false, // 页面展示开关
        isLogin:true
			};
		},
		props: {},
		onLoad(options) {
			// 保存别人分享来的 userCode
			console.log("来的id", options)
			console.log("来的app.globalData.pageId", app.globalData)
			util.saveSharerUserCode(options);
			let id;
			
			// #ifdef MP
			// 小程序环境下处理scene参数
			if (options.scene) {
				//接受二维码中参数
				let scenes = decodeURIComponent(options.scene).split('&');
				id = scenes[0];
			} else if (options.goods_id) {
				id = options.goods_id;
			} else if (options.id) {
				// 小程序分享可能直接使用id参数
				id = options.id;
			} else {
				// 从全局获取，或使用默认值
				id = app.globalData.goodsId || '';
			}
			// #endif
			
			// #ifdef H5
			if (options.scene) {
				//接受二维码中参数
				let scenes = decodeURIComponent(options.scene).split('&');
				id = scenes[0];
			} else {
				id = options.goods_id;
			}
			// #endif

      if(!uni.getStorageSync('third_session')){
        this.isLogin=false
      }
			this.id = id;
			console.log("拿到的id", this.id)
			app.initPage().then(res => {
				this.goodsGet(id);

				// this.couponInfoPage(id);
				// this.shoppingCartCountFun();
				// this.goodsAppraisesPage();
				// this.listEnsureBySpuId(id);
			});
		},
		onShareAppMessage: function() {
			let goodsSpu = this.goodsSpu;
			let title = goodsSpu.name;
			let imageUrl = goodsSpu.picUrls[0];

			const userInfo = uni.getStorageSync('user_info')
			let userCode = userInfo ? '&sharer_user_code=' + userInfo.userCode : ''
			let path = 'pages/goods/goods-detail/index?id=' + goodsSpu.id + userCode;
			return {
				title: title,
				path: path,
				imageUrl: imageUrl,
				success: function(res) {
					if (res.errMsg == 'shareAppMessage:ok') {
						console.log(res.errMsg);
					}
				},
				fail: function(res) { // 转发失败
				}
			};
		},

		// 添加小程序分享到朋友圈的方法
		// #ifdef MP-WEIXIN
		onShareTimeline: function() {
			let goodsSpu = this.goodsSpu;
			let title = goodsSpu.name;
			
			const userInfo = uni.getStorageSync('user_info')
			let userCode = userInfo ? 'sharer_user_code=' + userInfo.userCode : ''
			
			return {
				title: title,
				query: 'id=' + goodsSpu.id + '&' + userCode,
				imageUrl: goodsSpu.picUrls[0]
			};
		},
		// #endif
		methods: {
			loadData() {
				// 修改获取参数的方式，兼容小程序
				let tenantId = '';
				let pageId = '';
				let shareFriendId = '';
				
				// #ifdef H5
				tenantId = util.getUrlParam(location.href, "tenant_id") ? util.getUrlParam(location.href, "tenant_id") : app.globalData.tenantId;
				pageId = util.getUrlParam(location.href, "page_id") ? util.getUrlParam(location.href, "page_id") : app.globalData.pageId;
				shareFriendId = util.getUrlParam(location.href, "sharer_friend_id");
				// #endif
				
				// #ifdef MP
				const pages = getCurrentPages();
				const currentPage = pages[pages.length - 1];
				const options = currentPage.options || {};
				tenantId = options.tenant_id || app.globalData.tenantId;
				pageId = options.page_id || app.globalData.pageId;
				shareFriendId = options.sharer_friend_id || '';
				// #endif
				
				this.loadmore3 = true;
				let params = {
					id: pageId,
					tenantid: tenantId,
					shareFriendId: shareFriendId,
				}
        let promise
        if (this.isLogin){
          promise=api.pagedevise(params)
        }else{
          promise=api.pageDeviseFreedom(params)
        }
         promise.then(res => {
					console.log("初始化组件", res)
					let pageDivData = res.data;
					if (pageDivData) {
						this.pageDivData = pageDivData;
						this.pageStyle={
							backgroundColor:this.pageDivData.pageBase.backgroundColor?this.pageDivData.pageBase.backgroundColor:'',
							backgroundImage:  "url("+this.pageDivData.pageBase.background+")",
						}
						uni.setNavigationBarTitle({
							title: this.pageDivData.pageBase.pageTitle
						});
            if (this.isLogin){
              this.getButtonOrderMsg();
              //判断用户页面限制
              this.checkJump()
            }else{
              this.pageShowFlag=true
            }
						this.initShareWx();
					} else {
						// 如果没有设置自定义页面数据，那就显示默认原始页面
						// this.getDefaultPageData();
					}
				}).catch(err => {
					console.log(err)

				});
			},
			goodsGet(id) {
				console.log("请求id", id)
        let promise
        if (this.isLogin){
          promise=api.goodsGet(id)
        }else{
          promise=api.goodsGetFreedom(id)
        }
        return promise.then(res => {
					this.goodsSpu = res.data;
					this.loadData();
				});

			},
			goodsSpecGet(spuId) {
				api.goodsSpecGet({
					spuId: spuId
				}).then(res => {
					res.data ? res.data.map(t => {
						t.checked = ''
					}) : [];
					this.goodsSpecData = res.data;
				});
			},

			goodsAppraisesPage() {
				api.goodsAppraisesPage({
					current: 1,
					size: 3,
					descs: 'create_time',
					spuId: this.id
				}).then(res => {
					this.goodsAppraises = res.data;
				});
			},
			//分享功能
			shareShowFun() {
				// 分享海报需要配置的参数
				let desc = '长按识别小程序码';
				let qrCode = ""; //海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
				let shareImg = this.goodsSpu.picUrls[0]; // 海报图片

				// #ifdef H5 || APP-PLUS
				desc = '长按识别二维码';
				// h5的海报分享的图片有的有跨域问题，所以统一转成base64的
				// 之所以不在组件里面转换是因为无法区分哪张image图片需要处理，一般处理主图
				shareImg = util.imgUrlToBase64(shareImg);
				// #endif
				//海报配置请参考 https://github.com/jasondu/wxa-plugin-canvas
				let posterConfig = {
					width: 750,
					height: 1280,
					backgroundColor: '#fff',
					debug: false,
					blocks: [{
						width: 690,
						height: 808,
						x: 30,
						y: 183,
						borderWidth: 2,
						borderColor: '#f0c2a0',
						borderRadius: 20
					}, {
						width: 634,
						height: 74,
						x: 59,
						y: 770,
						backgroundColor: '#fff',
						opacity: 0.5,
						zIndex: 100
					}, ],
					texts: [{
						x: 30,
						y: 113,
						baseLine: 'top',
						text: '发现一个好物，推荐给你呀',
						fontSize: 38,
						color: '#080808'
					}, {
						x: 92,
						y: 810,
						fontSize: 38,
						baseLine: 'middle',
						text: this.goodsSpu.name,
						width: 570,
						lineNum: 1,
						color: '#080808',
						zIndex: 200
					}, {
						x: 59,
						y: 895,
						baseLine: 'middle',
						text: [{
							text: '只需',
							fontSize: 28,
							color: '#ec1731'
						}, {
							text: '¥' + this.goodsSpu.priceDown,
							fontSize: 36,
							color: '#ec1731',
							marginLeft: 30
						}, ]
					}, {
						x: 522,
						y: 895,
						baseLine: 'middle',
						text: '已售' + this.goodsSpu.saleNum,
						fontSize: 28,
						color: '#929292'
					}, {
						x: 59,
						y: 945,
						baseLine: 'middle',
						text: [{
							text: this.goodsSpu.sellPoint,
							fontSize: 28,
							color: '#929292',
							width: 570,
							lineNum: 1
						}]
					}, {
						x: 360,
						y: 1065,
						baseLine: 'top',
						text: desc,
						fontSize: 38,
						color: '#080808'
					}, {
						x: 360,
						y: 1123,
						baseLine: 'top',
						text: '超值好货快来购买',
						fontSize: 28,
						color: '#929292'
					}, ],
					images: [{
						width: 634,
						height: 634,
						x: 59,
						y: 210,
						url: shareImg
					}, {
						width: 220,
						height: 220,
						x: 92,
						y: 1020,
						url: null,
						qrCodeName: 'qrCodeName', // 二维码唯一区分标识
					}, ]
				};
				let userInfo = uni.getStorageSync('user_info');
				if (userInfo && userInfo.headimgUrl) {
					//如果有头像则显示
					posterConfig.images.push({
						width: 62,
						height: 62,
						x: 30,
						y: 30,
						borderRadius: 62,
						url: userInfo.headimgUrl
					});
					posterConfig.texts.push({
						x: 113,
						y: 61,
						baseLine: 'middle',
						text: userInfo.nickName,
						fontSize: 32,
						color: '#8d8d8d'
					});
				}
				this.shareParams = {
					title: '发现一个好物，推荐给你呀',
					desc: this.goodsSpu.name,
					imgUrl: this.goodsSpu.picUrls[0],
					scene: this.goodsSpu.id,
					page: 'pages/goods/goods-detail/index',
					posterConfig: posterConfig
				}
				this.showShare = true;
			},
			//判断底部导航栏出现并腾出位置
			bottomHeightShow(obj) {
				// console.log("开了", obj)
				if (obj) {
					this.bottomHeight = obj
					this.cusTabBarShowFlag = true
				}
			},
			//拿取当前页订单信息 用于多功能按钮的显示
			getButtonOrderMsg() {
				//判断有无 支付类型的多功能按钮
				for (let i = 0; i < this.pageDivData.pageComponent.componentsList.length; i++) {
					let obj = this.pageDivData.pageComponent.componentsList[i];
					if (obj.componentName == "functionButtonComponent") { //有无多功能菜单
						console.log("请求参数22", obj)
						for (let j = 0; j < obj.data.buttonList.length; j++) {
							if (obj.data.buttonList[j].button.type == 2) { //有无支付按钮
								let pageId = this.pageDivData.id;
								console.log("请求参数", pageId)
								api.getButtonOrder(pageId).then(res => {
									console.log("查询按钮订单", res)
									this.buttonOrder = res.data;
								});
								return
							}
						}
					}
				}
			},
			
			//判断是否显示多功能按钮
			showFunctionButtonComponent(obj) {
				if (obj.showRule == 1) {
					return true
				} else if (obj.showRule == 2 && this.buttonOrder.isPay == "0") {
					return true
				} else if (obj.showRule == 3 && this.buttonOrder.isPay == "1") {
					return true
				}
				return false
			},
			//初始化分享
			initShareWx() {
				// #ifdef H5
				// h5页面加载时 会默认初始化分享
				if (util.isWeiXinBrowser()) {
					let shareObj = {
						title: this.pageDivData.pageBase.shareTitle ? this.pageDivData.pageBase.shareTitle : '1',
						desc: this.pageDivData.pageBase.describe ? this.pageDivData.pageBase.describe : '',
						imgUrl: this.goodsSpu.shareUrls ? this.goodsSpu.shareUrls : this.pageDivData.pageBase.shareImgUrl
					};
					// console.log("来分享的参数",shareObj)
					let url = util.setH5ShareUrl();
					// //重新设置新的url
					let query = this.$Route.query;
					delete query.code;
					delete query.state;
					query.sharer_friend_id = util.getUrlParam(url, "sharer_friend_id");
					util.resetPageUrl(query);
					// console.log("重选设置url",query)
					api.getJsSdkConfig({
						url: location.href
					}).then(res => {
						// history.replaceState(history.state, null, url);
						let wxConfig = res.data;
						let shareObjTemp = {
							title: shareObj.title ? shareObj.title : '',
							desc: shareObj.desc ? shareObj.desc : '',
							link: wxConfig.url,
							imgUrl: shareObj.imgUrl ? shareObj.imgUrl : '',
						};
						jweixin.shareWxFriend(wxConfig, shareObjTemp, function() {
							// that.showModal = true;
							// uni.hideLoading();
							// console.log("初始化微信分享成功", shareObjTemp)
						}, function(e) {
							// console.log("初始化微信分享成功error",e)
						}, );
					}).catch(res => {
						console.log('调用getJsSdkConfig失败：' + res)
					});
				}
				// #endif
				
				// #ifdef MP
				// 小程序不需要在这里初始化分享，使用onShareAppMessage处理
				// #endif
			},
			//检查用户此页面限制
			checkJump(){
				if (this.pageDivData.forbidFlag) {
					if (!this.pageDivData.pageBase.forbidPageUrl || this.pageDivData.pageBase.forbidPageUrl.indexOf('/') <
						0) {
						return;
					}
					if (this.pageDivData.pageBase.forbidIsSystemUrl) {
						return uni.navigateTo({
							url: this.pageDivData.pageBase.forbidPageUrl
						});
					} else {
						// 修改为uni-app的方式打开外部链接
						// #ifdef H5
						window.location.href = this.pageDivData.pageBase.forbidPageUrl;
						// #endif
						// #ifdef MP
						uni.navigateTo({
							url: '/pages/webview/webview?url=' + encodeURIComponent(this.pageDivData.pageBase.forbidPageUrl)
						});
						// #endif
					}
				}
				if (this.pageDivData.permitFlag) {
					this.pageShowFlag = true;
				}else{
					if (!this.pageDivData.pageBase.permitPageUrl || this.pageDivData.pageBase.permitPageUrl.indexOf('/') <0) {
						return;
					}
					if (this.pageDivData.pageBase.permitIsSystemUrl) {
						return uni.navigateTo({
							url: this.pageDivData.pageBase.permitPageUrl
						});
					} else {
						// 修改为uni-app的方式打开外部链接
						// #ifdef H5
						window.location.href = this.pageDivData.pageBase.permitPageUrl;
						// #endif
						// #ifdef MP
						uni.navigateTo({
							url: '/pages/webview/webview?url=' + encodeURIComponent(this.pageDivData.pageBase.permitPageUrl)
						});
						// #endif
					}
				}
			
			},
			
		}
	};
</script>
<style>
	@import "./index.css";
</style>
