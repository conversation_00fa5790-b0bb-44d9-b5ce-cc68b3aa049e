<template>
	<view class="">
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">提现</block>
		</cu-custom>
		<view class="cu-form-group margin-top">
			<text class="cuIcon-card text-red"></text>
			<view class="title margin-left-sm font-weight">选择银行</view>
			<picker @change="bankPickerChange" :value="bankIndex" :range="bankPicker">
				<view class="picker text-blue font-weight">{{bankIndex>-1?bankPicker[bankIndex]:'转账到银行卡'}}</view>
			</picker>
		</view>
		<view class="font-weight text-gray text-right text-xs margin-sm">当前可支持提现到银行卡</view>
		<view class="margin-top bg-white" style="height: 260rpx;">
			<view class="font-weight padding-top margin-left">账号信息</view>
			<view class="cu-form-group margin-top font-weight flex">
				<input v-model="form.paymentDetail" placeholder="请输入提现的姓名和卡号" placeholder-class="input-text" name="input"></input>
			</view>
		</view>
		<!-- <view class="cu-form-group margin-top font-weight">
			<view class="title">姓名</view>
			<input class="input-box" v-model="form.bankName" placeholder="请输入姓名" placeholder-class="input-text" name="input"></input>
		</view>
		<view class="cu-form-group font-weight">
			<view class="title">银行卡号</view>
			<input class="input-box" v-model="form.bankNumber" placeholder="请输入卡号" placeholder-class="input-text" name="input"></input>
		</view> -->
		<!-- <view class="cu-form-group font-weight">
			<view class="title">开户银行</view>
			<input class="input-box" v-model="form.bankNumber" placeholder="请输入银行" placeholder-class="input-text" name="input"></input>
		</view> -->
		<view class="margin-top bg-white" style="height: 260rpx;">
			<view class="font-weight padding-top margin-left">提现金额</view>
			<view class="cu-form-group margin-top font-weight flex">
				<view class="text-price"></view>
				<input v-model="form.applyAmount" :min="0" type="number" :max="withdrawAmount" placeholder="请输入提现金额" placeholder-class="input-text" name="input"></input>
			</view>
			<view class="cu-form-group font-weight text-gray">
				<view>当前可提现金额为：<text class="text-price">{{withdrawAmount}}</text></view>
				<view>冻结中：<text class="text-price">{{frozenAmount}}</text></view>
			</view>
		</view>
		<view class="margin-top bg-white">
			<view class="font-weight padding-top margin-left">备注</view>
			<view class="cu-form-group margin-top font-weight flex">
				<input v-model="form.remarks" class="margin-left-sm"
				 placeholder="请输入备注" placeholder-class="input-text" name="input"></input>
			</view>
		</view>
		<view class="padding flex flex-direction margin-top-xl">
			<button class="cu-btn margin-tb-sm lg" :class="'bg-'+theme.themeColor" @click="submit">确认提现</button>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	const numberUtil = require("utils/numberUtil.js");
	
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				id:null,
				bankIndex: -1,
				bankPicker: [],
				userInfo: {},
				distribution: undefined,
				distributionConfig: undefined,
				frozenAmount: 0,//冻结金额
				withdrawAmount: 0,//可提现金额
				form: {
					"applyAmount": 0,
					"paymentDetail": "",
					"paymentMethod": "1",
					"remarks": "",
					"withdrawType": "1"
				}
			}
		},
		
		onLoad(e){
			this.userInfo = uni.getStorageSync('user_info')
			if(e){ // 表示需要修改的提现
				this.id = e.id;
			}
			this.initData();
		},
		methods: {
			bankPickerChange(e){
				this.bankIndex = e.target.value
				this.form.paymentDetail = this.bankPicker[this.bankIndex]
			},
			submit(){
				if(this.form.applyAmount<this.distributionConfig.withdrawMin){
					uni.showToast({
						title: '申请金额必须大于最低提现金额' + this.distributionConfig.withdrawMin,
						icon: 'none',
						duration: 3000
					})
					return;
				}
				if(this.form.applyAmount<0){
					uni.showToast({
						title: '申请金额必须大于0',
						icon: 'none',
						duration: 3000
					})
					return;
				}
				if(this.form.applyAmount>this.withdrawAmount){
					uni.showToast({
						title: '申请金额不能超过可提现金额',
						icon: 'none',
						duration: 3000
					})
					return;
				}
				if(!this.form.paymentDetail){
					uni.showToast({
						title: '提现明细必须填写',
						icon: 'none',
						duration: 3000
					})
					return;
				}
				
				// 申请提现
				if(this.id){
					api.userwithdrawRecordUpdate(this.form).then(res => {
						if(res.data) {//是分销员
							uni.showToast({
								icon: 'none',
								title: '申请成功，请耐心等待审核通过~'
							})
							uni.redirectTo({
							    url: '/pages/distribution/distribution-withdraw-list/index'
							});
						}
					});
				}else{
					api.userwithdrawRecordSave(this.form).then(res => {
						if(res.data) {//是分销员
							uni.showToast({
								icon: 'none',
								title: '申请成功，可在提现记录中查看进度哦~'
							})
							uni.redirectTo({
							    url: '/pages/distribution/distribution-withdraw-list/index'
							});
						}
					});
				}
			},
			initData(){
				//分销设置
				api.distributionConfig().then(res => {
					if(res.data) {
						this.distributionConfig = res.data
						// 银行
						this.bankPicker = this.distributionConfig.withdrawBank.split(',')
						this.bankIndex = 0
					}
				});
				// 分销员详情，当前已有佣金金额：commissionTotal
				api.distributionuser().then(res => {
					if(res.data) {
						this.distribution = res.data
						// 可提佣金金额
						let commissionAmount = this.distribution.commissionTotal-this.distribution.commissionWithdrawal;
						commissionAmount = numberUtil.numberFormat(commissionAmount, 2);
						// 分销员 冻结金额：commissionTotal
						api.distributionorderFrozenAmount().then(res => {
							if(res.data) {
								this.frozenAmount = res.data
								// 当前可提现金额 = 当前已有佣金金额 - 冻结金额 
								let withdrawAmount = commissionAmount - this.frozenAmount
								this.withdrawAmount = numberUtil.numberFormat(withdrawAmount, 2)
							} else {//没有冻结金额
								this.withdrawAmount = commissionAmount
							}
						});
					} 
				});
				if(this.id){
					// 申请提现的详情
					api.userwithdrawRecord(this.id).then(res => {
						if(res.data) {
							this.form = res.data
						}
					});
				}
				
			}
		}
	}
</script>

<style>
	.font-weight{
		font-weight: 300;
	}
	
	.input-box{
		text-align: right;
		color: # !important;
	}
	
	.input-text{
		color: rgb(177, 183, 188);
	}
</style>
