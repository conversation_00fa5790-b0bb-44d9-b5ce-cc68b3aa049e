<template>
	<!-- 商品显示组件-横向排列方式显示 -->
	<view :style="{marginBottom: `${newData.pageSpacing}px`}" class="bg-white">
		<view class="wrapper-list-goods" >
			<view class="cu-bar " style="min-height: 80upx">
				<view class="goods-selection text-df margin-left"  :style="{color: `${newData.titleColor}`}">
					<text class="text-bold" :class="newData.titleIcon"></text>
					<text class="margin-left-xs">{{newData.title}}</text>
				</view>
				<view @click="onMoreShopGoods" class="goods-more text-sm margin-right-xs">更多<text class="cuIcon-right"></text></view>
			</view>
			<view >
				<scroll-view class="scroll-view_x goods-detail" scroll-x style="width:auto;overflow:hidden;">
					<block v-for="(item, index) in newData.goodsList" :key="index">
						<navigator hover-class="none" :url="'/pages/goods/goods-detail/index?id=' + item.id"
						 class="item shadow-warp flex goods-box radius">
							<view class="img-box">
								<image :src="item.picUrls[0] ? item.picUrls[0] : '/static/public/img/no_pic.png'"></image>
							</view>
							<view class="text-cut goods-name margin-top-sm text-sm padding-left-sm">{{item.name}}</view>
							<view class="text-price text-red margin-left-sm margin-xs text-lg text-bold">{{item.priceDown}}</view>
						</navigator>
					</block>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
    export default {
		components: {
		},
	    props: {
            value: {
                type: Object,
	            default: function() {
	                return {
						title: '商品甄选',
						titleColor: 'red',
						titleIcon: 'cuIcon-message',
						pageSpacing: 0,
					    goodsList: []
	                }
	            }
            }
	    },
		data() {
            return {
				theme: app.globalData.theme, //全局颜色变量
                newData: this.value,
			};
		},
		methods: {
			onMoreShopGoods(){
				this.$emit('onMoreShopGoods');
			},
			jumpPage(page) {
				if (page) {
					uni.navigateTo({
						url: page
					});
				}
			}
		}
    }
</script>

<style scoped lang="scss">
.wrapper-list-goods {
	white-space: nowrap;
	padding:  0 30rpx 6rpx 30rpx;
}

.wrapper-list-goods .item {
	display: inline-block;
	width: 260rpx;
	height: 380rpx;
	margin-top: 10px;
	margin-bottom: 10px;
	margin-left: 10rpx;
	margin-right: 10rpx;
	border: 1rpx solid #eee;
	background-color: #fff;
}

.wrapper-list-goods .item:nth-last-child(1) {
	margin-right: 0;
}

.wrapper-list-goods .item .img-box {
	width: 100%;
	height: 260rpx;

}

.wrapper-list-goods .item .img-box image {
	width: 100%;
	height: 100%;
	border-radius: 5rpx 5rpx 0 0;
}
.goods-selection{
	margin-left: 0rpx !important;
	color: #666666;
}

.goods-more{
	margin-right: 0rpx !important;
	color: #666666;
}
.goods-box {
	width: 349rpx;
	height: 530rpx;
	background-color: #fff;
	overflow: hidden;
	margin-bottom: 20rpx;
	border-radius: 10rpx;
}

.goods-box .img-box {
	width: 100%;
	height: 349rpx;
	overflow: hidden;
}

.goods-box .img-box image {
	width: 100%;
	height: 349rpx;
}

.goods-detail{
	margin-top:-20rpx !important;
}
</style>
