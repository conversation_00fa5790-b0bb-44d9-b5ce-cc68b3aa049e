<template>
	<view class="flex justify-center">
	<view class="imgUploadComponent">
		<view
			:style="{color: `${newData.titleColor}`, fontSize: `${newData.titleSize}px`,fontWeight:`${newData.titleWeight?'bold':'normal'}`}">
			{{newData.title}}<i v-show="newData.required" style="color: #FF0000">*</i>
		</view>
		<view
			class="margin-tb-xs"
			:style="{color: `${newData.describeColor}`, fontSize: `${newData.describeSize}px`,fontWeight:`${newData.describeWeight?'bold':'normal'}`}">
			{{newData.describe}}
		</view>
		<view>
			<uni-file-picker :title="'最多选择'+newData.imgLimit+'张图片'" v-model="imageValue" :image-styles="imageStyles" :auto-upload="false" fileMediatype="image" mode="grid" @select="select" :limit="newData.imgLimit"
				@progress="progress" @success="success" @fail="fail" />
		</view>
		<!-- <button @click="chooseImage"></button> -->
	</view>
	</view>
</template>

<script>
	const util = require("utils/util.js");
	import __config from 'config/env';
	export default {
		data() {
			return {
				newData: this.value,
				imageValue: [],
				imageStyles: {
					"height": 100, // 边框高度
					"width": 100, // 边框宽度
					"border": { // 如果为 Boolean 值，可以控制边框显示与否
						"color": "#eee", // 边框颜色
						"width": "1px", // 边框宽度
						"style": "dashed", // 边框样式
						"radius": "" // 边框圆角，支持百分比
					}
				},
			};
		},

		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		created() {
			this.rateValue = this.newData.initRate
		},
		methods: {
			chooseImage() {
				uni.chooseImage({
					success: (chooseImageRes) => {
						const tempFilePaths = chooseImageRes.tempFilePaths;
						let _url = '/mallapi/file/upload';
						//#ifndef H5
						_url = __config.basePath + _url
						//#endif
						let that = this
						uni.uploadFile({
							header: {
								//#ifdef H5
								'client-type': util.isWeiXinBrowser() ? 'H5-WX' :
								'H5', //客户端类型普通H5或微信H5
								'tenant-id': getApp().globalData.tenantId,
								'app-id': getApp().globalData.appId ? getApp().globalData.appId :
								'', //微信h5有appId
								//#endif
								//#ifdef MP-WEIXIN
								'client-type': 'MA', //客户端类型小程序
								'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
								//#endif
								//#ifdef APP-PLUS
								'client-type': 'APP', //客户端类型APP
								'tenant-id': getApp().globalData.tenantId,
								//#endif
								'third-session': uni.getStorageSync('third_session') ? uni
									.getStorageSync('third_session') : '',
							},
							url: _url,
							filePath: tempFilePaths[0],
							name: 'file',
							formData: {
								'fileType': 'image',
								'dir': 'headimg/'
							},
							success: (uploadFileRes) => {
								console.log(JSON.parse(uploadFileRes.data).link)
								that.userInfo.headimgUrl = JSON.parse(uploadFileRes.data).link
							}
						});
					}
				});
			},
			// // 获取上传状态
			select(e) {
				console.log('选择文件：', e)
				const tempFilePaths = e.tempFilePaths;
				let _url = '/weixinapi/file/upload';
				//#ifndef H5
				_url = __config.basePath + _url
				//#endif
				uni.uploadFile({
					header: {
						//#ifdef H5
						'client-type': util.isWeiXinBrowser() ? 'H5-WX' :
						'H5', //客户端类型普通H5或微信H5
						'tenant-id': getApp().globalData.tenantId,
						'app-id': getApp().globalData.appId ? getApp().globalData.appId :
						'', //微信h5有appId
						//#endif
						//#ifdef MP-WEIXIN
						'client-type': 'MA', //客户端类型小程序
						'app-id': uni.getAccountInfoSync().miniProgram.appId, //小程序appId
						//#endif
						//#ifdef APP-PLUS
						'client-type': 'APP', //客户端类型APP
						'tenant-id': getApp().globalData.tenantId,
						//#endif
						'third-session': uni.getStorageSync('third_session') ? uni
							.getStorageSync('third_session') : '',
					},
					url: _url,
					filePath: e.tempFilePaths[0],
					name: 'file',
					formData: {
						'fileType': 'image',
						'dir': 'formData/'
					},
					success: (uploadFileRes) => {
						console.log("成功",JSON.parse(uploadFileRes.data).link)
					}
				});
			},
			// 获取上传进度
			progress(e) {
				console.log('上传进度：', e)
			},

			// 上传成功
			success(e) {
				console.log('上传成功')
			},

			// 上传失败
			fail(e) {
				console.log('上传失败：', e)
			},
			checkValue(){
				console.log("检验值了imgUploadComponent",this.imageValue)
				if(this.newData.required && this.imageValue.length<1){
					uni.showToast( {
						title: "请至少上传一张图片",
						icon: 'none',
						duration: 2000
					});
					return false;
				}
				return {value:this.imageValue};
			}
		}
	};
</script>
<style>
	.imgUploadComponent {
		padding: 5px 15px;
		width: 90%;
		border-bottom:1px dashed  rgba(0,0,0,.2);
	}
</style>
