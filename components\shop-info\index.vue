<!--
  - Copyright (C) 2018-2019
  - All rights reserved, Designed By www.joolun.com
  - 注意：
  - 本软件为www.joolun.com开发研制，未经购买不得使用
  - 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
  - 一经发现盗用、分享等行为，将追究法律责任，后果自负
-->
<template>
	<view class="cu-card article" :class="card?'':'no-card'" v-if="shopInfo">
		<view class="cu-item">
			<view class="shop-name cuIcon-shop margin-left margin-top-sm" @click="toShopHome(shopInfo.id)">
				<text class="text-sm text-bold margin-left-xs">{{shopInfo.name}}</text></view>
			<view class="content shop-detail margin-top-sm align-center">
				<image :src="shopInfo.imgUrl" mode="aspectFill"></image>
				<view class="text-gray">
					<view class="cuIcon-locationfill location margin-left-xs overflow-2">
						<text class="address text-sm margin-left-xs">{{shopInfo.address}}</text></view>
					<view class="cuIcon-mobilefill mobile margin-left-xs margin-top-xs" :hover-stop-propagation="true" @click="callPhone(shopInfo.phone)">
						<text class="phone text-sm margin-left-xs">{{shopInfo.phone}}</text></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	export default {
		data() {
			return {};
		},

		components: {},
		props: {
			shopInfo: {
				type: Object,
				default: () => ({})
			},
			card: {
				type: Boolean,
				default: true
			}
		},
		methods: {
			//跳转到商铺首页
			toShopHome(id) {
				uni.navigateTo({
					url: '/pages/shop/shop-detail/index?id=' + id
				});
			},

			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				});
			}
		}
	};
</script>
<style>
	.shop-detail image{
		width: 120rpx !important;
		height: 120rpx !important;
	}
	
	.location{
		width: 520rpx;
	}
</style>
