<template>
	<view>
		<view v-if="goodsObj && goodsObj.contentUrls" v-for="(item, rowIndex) in goodsObj.contentUrls" :key="rowIndex">
			<view :style="{paddingTop: `${newData.topAndBottomSpacing}px`, paddingBottom: `${newData.topAndBottomSpacing}px`, paddingLeft: `${newData.leftAndRightSpacing}px`, paddingRight: `${newData.leftAndRightSpacing}px`}">
				<image class="content_image" 
					mode="widthFix" :src='item.url'>
				</image>
			</view>
		</view>
	</view>
</template>

<script>
	import api from '@/utils/api'
	const util = require("utils/util.js");
	const app = getApp();

	export default {
		data() {
			return {
				newData: this.value,
				goodsObj:this.goods,
			};
		},
		watch:{
			goodsObj(val,oldVal){
				console.log("watch监听",this.val)
			}
		},
		computed: {
			
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
			goods: {
				type: Object,
			},
		},
		created(options) {
			console.log('商品详情展示', this.newData)
			if(this.goodsObj && this.goodsObj.contentUrls){
				this.goodsObj.contentUrls = JSON.parse(this.goodsObj.contentUrls);
			}
			// console.log('商品详情展示urls', this.goodsObj)
			
		},
		methods: {
			
		}
	};
</script>
<style>
	.content_image{
		display: block;
		width: 100%;
	}
</style>
