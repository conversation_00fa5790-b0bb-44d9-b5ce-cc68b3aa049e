<template>
  <view class="container">
    <!-- 选项卡 -->
    <view class="nav-tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index" 
        class="nav-tab" 
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        {{ tab.name }} {{ tab.count ? `(${tab.count})` : '' }}
      </view>
    </view>
    
    <!-- 优惠券列表 -->
    <view class="coupon-list" v-if="currentCoupons.length > 0">
      <!-- 优惠券卡片 -->
      <view class="coupon-card" v-for="(coupon, index) in currentCoupons" :key="index">
        <view class="coupon-shadow-top"></view>
        <view class="coupon-inner">
          <view class="coupon-left" :class="coupon.leftClass">
            <text class="coupon-amount-sm">¥</text>
            <text class="coupon-amount">{{ coupon.amount }}</text>
            <text class="coupon-type" :class="coupon.typeClass">{{ coupon.type }}</text>
          </view>
          <view class="coupon-right">
            <view class="coupon-info">
              <text class="coupon-title">{{ coupon.title }}</text>
              <text class="coupon-desc">{{ coupon.desc }}</text>
              <text class="coupon-expire-soon" v-if="coupon.expireSoon">
                <text class="icon-warning">!</text>
                即将过期
              </text>
            </view>
            <view class="coupon-footer">
              <view class="coupon-validity-row">
                <text class="coupon-validity">{{ coupon.validityText }}</text>
              </view>
              <view class="coupon-countdown-row" v-if="coupon.countdown">
                <view class="coupon-countdown" :class="coupon.countdownClass">
                  <text class="countdown-icon">⏱</text>
                  <text>{{ coupon.countdown.prefix }}</text>
                  <text class="countdown-unit" :class="coupon.countdownUnitClass">{{ coupon.countdown.days }}</text>
                  <text>天</text>
                  <text class="countdown-unit" :class="coupon.countdownUnitClass">{{ coupon.countdown.hours }}</text>
                  <text>时</text>
                  <text class="countdown-unit" :class="coupon.countdownUnitClass">{{ coupon.countdown.minutes }}</text>
                  <text>分</text>
                </view>
              </view>
            </view>
            <view class="coupon-button-container" v-if="currentTab === 0">
              <button class="coupon-button" @click="useCoupon(coupon)">立即使用</button>
            </view>
            <text class="coupon-status" v-if="coupon.status">{{ coupon.status }}</text>
          </view>
        </view>
        <view class="coupon-shadow-bottom"></view>
      </view>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-else>
      <text class="empty-icon">🎫</text>
      <text class="empty-text">{{ emptyText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentTab: 0,
      tabs: [
        { name: '未使用', count: 3 },
        { name: '已使用', count: 1 },
        { name: '已过期', count: 0 }
      ],
      unusedCoupons: [
        {
          amount: '50',
          type: '满减券',
          title: '新人专享优惠券',
          desc: '满500元可用',
          validityText: '有效期至：2025-06-09 23:59:59',
          leftClass: 'bg-red-50',
          typeClass: 'type-red',
          countdown: {
            prefix: '剩余',
            days: '154',
            hours: '07',
            minutes: '22'
          }
        },
        {
          amount: '100',
          type: '满减券',
          title: '情侣写真专享券',
          desc: '满1000元可用',
          validityText: '有效期至：2025-06-15 23:59:59',
          leftClass: 'bg-red-50',
          typeClass: 'type-red',
          countdown: {
            prefix: '剩余',
            days: '160',
            hours: '12',
            minutes: '45'
          }
        },
        {
          amount: '30',
          type: '无门槛券',
          title: '会员生日礼券',
          desc: '无门槛使用',
          validityText: '有效期至：2025-05-12 23:59:59',
          leftClass: 'bg-orange-50',
          typeClass: 'type-orange',
          expireSoon: true,
          countdown: {
            prefix: '仅剩',
            days: '7',
            hours: '03',
            minutes: '59'
          },
          countdownClass: 'countdown-urgent',
          countdownUnitClass: 'countdown-unit-urgent'
        }
      ],
      usedCoupons: [
        {
          amount: '20',
          type: '折扣券',
          title: '证件照专享券',
          desc: '满30元可用',
          validityText: '使用时间：2025-05-01 14:30:25',
          leftClass: 'bg-gray-100',
          typeClass: 'type-gray',
          status: '已使用'
        }
      ],
      expiredCoupons: []
    };
  },
  computed: {
    currentCoupons() {
      if (this.currentTab === 0) return this.unusedCoupons;
      if (this.currentTab === 1) return this.usedCoupons;
      return this.expiredCoupons;
    },
    emptyText() {
      if (this.currentTab === 0) return '暂无可用优惠券';
      if (this.currentTab === 1) return '暂无已使用优惠券';
      return '暂无已过期优惠券';
    }
  },
  onLoad() {
    console.log('优惠券页面加载完成');
  },
  methods: {
    switchTab(index) {
      this.currentTab = index;
    },
    useCoupon(coupon) {
      uni.showToast({
        title: `正在使用${coupon.title}`,
        icon: 'none'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding: 15px;
  background-color: #f8f8f8;
  min-height: 100vh;
  
  .nav-tabs {
    display: flex;
    background-color: white;
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
    
    .nav-tab {
      flex: 1;
      text-align: center;
      padding: 12px 0;
      font-size: 14px;
      color: #666;
      
      &.active {
        color: white;
        background-color: #FF6B6B;
        font-weight: bold;
      }
    }
  }
  
  .coupon-list {
    margin-bottom: 50px;
  }
  
  .coupon-card {
    position: relative;
    margin-bottom: 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  }
  
  .coupon-shadow-top,
  .coupon-shadow-bottom {
    height: 5px;
    background-color: #f8f8f8;
    position: relative;
  }
  
  .coupon-shadow-top {
    border-radius: 0 0 100% 100% / 0 0 5px 5px;
    box-shadow: 0 2px 3px -2px rgba(0,0,0,0.1) inset;
  }
  
  .coupon-shadow-bottom {
    border-radius: 100% 100% 0 0 / 5px 5px 0 0;
    box-shadow: 0 -2px 3px -2px rgba(0,0,0,0.1) inset;
  }
  
  .coupon-inner {
    display: flex;
    background-color: white;
  }
  
  .coupon-left {
    width: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px;
    position: relative;
    
    &.bg-red-50 {
      background-color: #FFF0F0;
    }
    
    &.bg-orange-50 {
      background-color: #FFF5EB;
    }
    
    &.bg-gray-100 {
      background-color: #F5F5F5;
    }
  }
  
  .coupon-right {
    flex: 1;
    padding: 10px 12px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    
    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 1px;
      background: repeating-linear-gradient(#e0e0e0 0, #e0e0e0 4px, transparent 4px, transparent 8px);
    }
  }
  
  .coupon-info {
    .coupon-title {
      font-weight: bold;
      font-size: 14px;
      display: block;
      line-height: 1.3;
    }
    
    .coupon-desc {
      font-size: 12px;
      color: #666;
      display: block;
      margin-top: 2px;
      line-height: 1.3;
    }
    
    .coupon-expire-soon {
      font-size: 10px;
      color: #FF6B00;
      display: block;
      margin-top: 2px;
      
      .icon-warning {
        display: inline-block;
        width: 12px;
        height: 12px;
        line-height: 12px;
        text-align: center;
        background-color: #FF6B00;
        color: white;
        border-radius: 50%;
        font-size: 9px;
        margin-right: 4px;
      }
    }
  }
  
  .coupon-footer {
    display: flex;
    flex-direction: column;
    margin-top: 4px;
    
    .coupon-validity-row {
      margin-bottom: 2px;
    }
    
    .coupon-validity {
      font-size: 10px;
      color: #999;
    }
    
    .coupon-countdown-row {
      margin-bottom: 4px;
    }
    
    .coupon-countdown {
      font-size: 10px;
      color: #FF6B6B;
      font-weight: 500;
      display: flex;
      align-items: center;
      
      &.countdown-urgent {
        color: #FF4500;
      }
      
      .countdown-icon {
        margin-right: 3px;
        font-size: 10px;
      }
      
      .countdown-unit {
        background-color: #FFF0F0;
        color: #FF6B6B;
        padding: 0 2px;
        border-radius: 2px;
        margin: 0 1px;
        font-weight: bold;
        
        &.countdown-unit-urgent {
          background-color: #FFF0E8;
          color: #FF4500;
        }
      }
    }
  }
  
  .coupon-button-container {
    position: absolute;
    bottom: 10px;
    right: 12px;
  }

  .coupon-button {
    font-size: 10px;
    padding: 2px 10px;
    background-color: #FF6B6B;
    color: white;
    border-radius: 50px;
    border: none;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
  }
  
  .coupon-amount {
    font-size: 24px;
    font-weight: bold;
    color: #FF6B6B;
    line-height: 1;
    
    .bg-gray-100 & {
      color: #999;
    }
  }
  
  .coupon-amount-sm {
    font-size: 14px;
    color: #FF6B6B;
    
    .bg-gray-100 & {
      color: #999;
    }
  }
  
  .coupon-type {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    margin-top: 5px;
    
    &.type-red {
      background-color: #FFEEEE;
      color: #FF6B6B;
    }
    
    &.type-orange {
      background-color: #FFF0E0;
      color: #FF8C00;
    }
    
    &.type-gray {
      background-color: #EEEEEE;
      color: #999;
    }
  }
  
  .coupon-status {
    position: absolute;
    top: -5px;
    right: -25px;
    transform: rotate(45deg);
    background-color: #cccccc;
    color: white;
    padding: 15px 25px 2px;
    font-size: 10px;
    width: 80px;
    text-align: center;
  }
  
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    color: #999;
    
    .empty-icon {
      font-size: 60px;
      color: #ddd;
      margin-bottom: 15px;
    }
    
    .empty-text {
      color: #999;
    }
  }
}
</style> 