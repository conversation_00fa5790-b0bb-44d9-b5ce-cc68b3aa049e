<template>
	<view class="flex justify-center">
		<view class="phoneCheckComponent">
			<view
				:style="{color: `${newData.titleColor}`, fontSize: `${newData.titleSize}px`,fontWeight:`${newData.titleWeight?'bold':'normal'}`}">
				{{newData.title}}<i v-show="newData.required" style="color: #FF0000">*</i>
			</view>
			<view class="margin-tb-xs"
				:style="{color: `${newData.describeColor}`, fontSize: `${newData.describeSize}px`,fontWeight:`${newData.describeWeight?'bold':'normal'}`}">
				{{newData.describe}}
			</view>
			<view class="form_comp_view  flex justify-start align-start solid ">
				<text class="form_comp_view_icon margin-sm   cuIcon-phone"></text>
				<input class="flex-twice margin-sm" @input="changePhone" v-model="phone" type="number"
					:placeholder="'请输入'+newData.title">
			</view>
			<view class="form_comp_view flex justify-start align-start ">
				<view style="width: 50%;" class=" form_comp_view flex justify-start align-start solid ">
					<text class="form_comp_view_icon margin-sm cuIcon-safe "></text>
					<input class="flex-twice margin-sm" v-model="code" type="text" placeholder="请输入验证码">
				</view>
				<view style="width: 50%;">
					<button type="default" class="cu-btn bg-gray margin-sm align-center" :disabled="msgKey"
						:class="'display:' + msgKey" size="mini" @click="getPhoneCode">{{msgText}}</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	const app = getApp();
	import validate from 'utils/validate'
	import api from '@/utils/api'
	const MSGINIT = "发送验证码",
		MSGSCUCCESS = "${time}秒后可重发",
		MSGTIME = 60;
	export default {
		data() {
			return {
				newData: this.value,
				msgKey: false,
				msgText: MSGINIT,
				msgTime: MSGTIME,
				phone: '',
				code: '',
				type: 5,
			};
		},
		components: {},
		props: {
			value: {
				type: Object,
				default: function() {
					return {}
				}
			},
		},
		methods: {
			getPhoneCode() {
				if (this.msgKey) return
				if (!validate.validateMobile(this.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none',
						duration: 3000
					});
					return;
				}
				this.msgKey = true
				api.getPhoneCode({
					type: this.type,
					phone: this.phone,
					signName: this.newData.signName,
					templateCode: this.newData.templateCode,
					tenantId: app.globalData.tenantId
				}).then(res => {
					this.msgKey = false
					if (res.code == '0') {
						uni.showToast({
							title: '验证码发送成功',
							icon: 'none',
							duration: 3000
						});
						this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
						this.msgKey = true
						const time = setInterval(() => {
							this.msgTime--
							this.msgText = MSGSCUCCESS.replace('${time}', this.msgTime)
							if (this.msgTime == 0) {
								this.msgTime = MSGTIME
								this.msgText = MSGINIT
								this.msgKey = false
								clearInterval(time)
							}
						}, 1000)
					} else {

					}
				}).catch(() => {
					this.msgKey = false
				});
			},
			changePhone(val) {
				console.log("改变值了", this.phone)
			},
			checkValue() {
				console.log("检验值了", this.phone, this.code)
				if (!this.phone) {
					uni.showToast({
						title: "请完善" + this.newData.title + "的内容",
						icon: 'none',
						duration: 2000
					});
					return false;
				}
				if (!this.code) {
					uni.showToast({
						title: "请输入验证码",
						icon: 'none',
						duration: 2000
					});
					return false;
				}
				return {
					value: this.phone,
					checkCode: this.code
				};
			}
		}
	};
</script>
<style>
	.phoneCheckComponent {
		padding: 5px 15px;
		width: 90%;
		border-bottom: 1px dashed rgba(0, 0, 0, .2);
	}

	.form_comp_view {
		min-height: 20px;
		justify-content: center;
		align-items: center;
	}

	.form_comp_view_icon {
		font-size: 18px;
	}
</style>
