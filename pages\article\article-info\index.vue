<template>
	<view>
		<cu-custom :bgColor="'bg-'+theme.backgroundColor" :isBack="true">
			<block slot="backText">返回</block>
			<block slot="content">{{articleInfo.articleTitle}}</block>
		</cu-custom>
		<view class="bg-white padding">
			<view class="text-bold text-black text-xl">{{articleInfo.articleTitle}}</view>
			<view class="flex justify-between margin-top-sm">
				<view class="text-sm text-gray">{{articleInfo.authorName}}</view>
				<view class="text-sm text-gray">{{articleInfo.updateTime}}</view>
			</view>
		</view>
		<view class="padding">
			<!-- <view class="img-box flex">
				<image class="article-image" :src="articleInfo.picUrl" mode="aspectFill"></image>
			</view> -->
			<view class="margin-top-sm text-lg" style="line-height: 2em;">
				<jyf-parser :html="article_description"></jyf-parser>
			</view>
		</view>
	</view>
</template>

<script>
	/**
	 * Copyright (C) 2018-2019
	 * All rights reserved, Designed By www.joolun.com
	 * 注意：
	 * 本软件为www.joolun.com开发研制，未经购买不得使用
	 * 购买后可获得全部源代码（禁止转卖、分享、上传到码云、github等开源平台）
	 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
	 */
	const util = require("utils/util.js");
	const app = getApp();
	import api from 'utils/api'
	export default {
		data() {
			return {
				CustomBar: this.CustomBar,
				theme: app.globalData.theme, //全局颜色变量
				articleInfo: {},
				article_description: ""
			}
		},
		
		onLoad(options) {
			// 保存别人分享来的 userCode
			util.saveSharerUserCode(options);
			let id = options.id
			this.id = id
			app.initPage().then(res => {
				this.articleGet();
			});
		},
		
		methods: {
			articleGet() {
				api.articleGet(this.id).then(res => {
					let articleInfo = res.data;
					this.articleInfo = articleInfo
					setTimeout(() => {
						this.article_description = articleInfo.articleContent;
					}, 300);
				});
			},
		}
	}
</script>

<style>
	.img-box{
		width: 100%;
		height: 240rpx;
	}
	
	.article-image{
		width: 100%;
		height: 200rpx;
		margin: 0 auto;
	}
</style>
